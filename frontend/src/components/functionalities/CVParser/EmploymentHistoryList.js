import React from 'react';

const EmploymentHistoryList = ({ employmentHistory }) => (
    <div className="employment-list">
        {employmentHistory.map((job, index) => (
            <div key={index} className="employment-card">
                <h4>{job.employerName || 'Datore sconosciuto'}</h4>
                <p><strong>Role:</strong> {job.jobTitle || 'N/D'}</p>
                <p>
                    <strong>Employment Period:</strong> {job.startDate || 'N/D'} - {job.endDate || 'N/D'}
                </p>
                <p><strong>Description:</strong> {job.description || 'N/D'}</p>
            </div>
        ))}
    </div>
);

export default EmploymentHistoryList;
