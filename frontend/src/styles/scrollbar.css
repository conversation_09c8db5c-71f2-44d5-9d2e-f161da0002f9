/* Stile per la barra di scorrimento */

/* Larghezza della barra di scorrimento  */
::-webkit-scrollbar {
    width: 10px;
    height: 10px;
}

::-webkit-scrollbar-track {
    background: #f0f0f0; /* Colore dello sfondo della barra di scorrimento */

}

::-webkit-scrollbar-thumb {
    background: #afb8c1; /* Colore della parte mobile della barra di scorrimento */
    border-left: 2px solid white; /* Aggiungi un bordo bianco */

}

::-webkit-scrollbar-thumb:hover {
    background: #0056b3; /* Colore quando si passa il mouse sopra */
}

/* Stile per i pulsanti di scorrimento (freccette) */
::-webkit-scrollbar-button {
    background-color: transparent; /* Imposta lo sfondo trasparente */
    height: 16px; /* Altezza delle freccette */
    width: 16px; /* Larghezza delle freccette */
    padding: 0; /* Rimuovi padding */
    margin: 0; /* Rimuovi margin */
    /*display: block; /* Assicura che i pulsanti occupino l'intera area */
    background-size: 100% 100%;
}

/* Personalizza la freccetta in alto o a sinistra */
::-webkit-scrollbar-button:single-button:vertical:decrement {
    background: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="%230056b3"><path d="M12 8l-6 6h12z"/></svg>') no-repeat center;
    background-size: 100%; /* Dimensioni della freccetta */
}

/* Personalizza la freccetta in basso o a destra */
::-webkit-scrollbar-button:single-button:vertical:increment {
    background: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="%230056b3"><path d="M12 16l-6-6h12z"/></svg>') no-repeat center;
    background-size: 100%;
}

/* Freccette per scorrimento orizzontale */
::-webkit-scrollbar-button:single-button:horizontal:decrement {
    background: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="%230056b3"><path d="M8 12l6-6v12z"/></svg>') no-repeat center;
    background-size: 100%;
}

::-webkit-scrollbar-button:single-button:horizontal:increment {
    background: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="%230056b3"><path d="M16 12l-6 6V6z"/></svg>') no-repeat center;
    background-size: 100%;
}