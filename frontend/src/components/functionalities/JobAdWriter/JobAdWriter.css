.jobad-user-message {
    bottom: 0;
    left: 0;
    width: 100%;
    display: flex;
    align-items: baseline;
    flex-direction: column;
    padding: 10px;
    /* Aggiunto padding per dare maggiore respiro */
}

.jobad-upload-icon,
.jobad-mic-icon {
    cursor: pointer;
    margin-right: 10px;
    font-size: 1.2em;
    color: #555;
    transition: color 0.3s ease;
}

.jobad-upload-icon:hover,
.jobad-mic-icon:hover,
.jobad-user-message button svg:hover {
    color: #000;
}

.jobad-user-message button {
    background-color: transparent;
    border: none;
    cursor: pointer;
    height: 36px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 20px;
}

.jobad-user-message button:disabled {
    cursor: not-allowed;
    opacity: 0.6;
}

.jobad-user-message button svg {
    color: #555;
    cursor: pointer;
}

.jobad-input-area {
    background-color: #fff;
    border-radius: 5px;
    border-top: 1px solid #ddd;
    width: 100%;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    /* Aggiunta ombra leggera per profondità */
    transition: box-shadow 0.3s ease;
}

.jobad-input-area.drag-over {
    background-color: #f0f8ff;
    border: 2px dashed #007bff;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
    /* Ombra più marcata in stato drag-over */
}

.jobad-input-essentials {
    align-items: center;
    display: flex;
}

.jobad-textarea-style {
    flex-grow: 1;
    resize: none;
    padding: 10px;
    /* Aumentato il padding per maggiore comfort */
    border: 1px solid #ccc;
    font-family: 'Open Sans', sans-serif;
    font-size: 14px;
    margin: 10px;
    /* Margine uniforme */
    min-height: 40px;
    max-height: 100px;
    overflow-y: auto;
    transition: height 0.3s ease, border-color 0.3s ease;
    border-radius: 4px;
    /* Arrotondamento per un look più moderno */
}

.jobad-textarea-style:focus {
    height: 200px;
    border-color: #007bff;
    /* Evidenzia il focus con un colore blu */
    outline: none;
}

.jobad-textarea-style:not(:focus) {
    height: 40px;
}

@media (max-width: 600px) {

    .jobad-textarea-style {
        font-size: 12px;
    }
}

.jobad-input-icons {
    display: flex;
    align-items: center;
    padding: 10px;
}

.jobad-input-icons-advanced {
    display: flex;
    align-items: center;
    padding: 0 10px 10px 10px;
    font-size: 0.9em;
}

.dropdown-list {
    background-color: white;
    border: 1px solid #ccc;
    border-radius: 4px;
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
    position: absolute;
    z-index: 1000;
    display: flex;
    left: 30px;
    bottom: -15px;
    flex-direction: column;
}

.dropdown-item {
    padding: 10px;
    cursor: pointer;
    transition: background-color 0.3s ease;
}

.dropdown-item.disabled {
    cursor: default;
    color: #aaa;
}

.dropdown-item:hover {
    background-color: #f0f0f0;
}

.dropdown-item.disabled:hover {
    background-color: transparent;
}

.dropdown-item-label {
    font-weight: bold;
}

.dropdown-item-description {
    font-size: 0.85rem;
    color: #666;
}

.jobad-radio-group {
    display: flex;
    gap: 15px;
    margin-bottom: 10px;
    /* Spazio sotto il gruppo di radio button */
}

.jobad-thinking-container {
    margin-bottom: 24px;
    background: #f9f9f9;
    border: 1px solid #e0e0e0;
    border-radius: 6px;
    padding: 12px;
    display: flex;
    align-items: center;
    gap: 10px;
    position: relative;
}


.jobad-thinking-spinner {
    width: 24px;
    height: 24px;
    border: 3px solid #ccc;
    border-top: 3px solid #007bff;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% {
        transform: rotate(0deg);
    }

    100% {
        transform: rotate(360deg);
    }
}

.jobad-thinking-text {
    font-size: 0.95em;
    color: #333;
}

.jobad-code-block {
    transition: all 0.3s ease;
    opacity: 1;
    max-height: 500px;
}

.jobad-code-block-hidden {
    opacity: 0;
    max-height: 0;
    transition: all 0.3s ease;
}