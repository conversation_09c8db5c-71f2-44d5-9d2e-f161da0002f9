import React from 'react';
import PersonalInfo from './PersonalInfo';
import EducationHistoryList from './EducationHistoryList';
import EmploymentHistoryList from './EmploymentHistoryList';
import SkillsDisplay from './SkillsDisplay';
import OtherInfo from './OtherInfo';
import './CVParser.css';

const CVDisplay = ({ cvData }) => {
    return (
        <div className="cv-display">
            {cvData.educationHistory && cvData.educationHistory.length > 0 && (
                <section className="cv-section education-history">
                    <h2>Education History</h2>
                    <EducationHistoryList educationHistory={cvData.educationHistory} />
                </section>
            )}
            {cvData.employmentHistory && cvData.employmentHistory.length > 0 && (
                <section className="cv-section employment-history">
                    <h2>Employment History</h2>
                    <EmploymentHistoryList employmentHistory={cvData.employmentHistory} />
                </section>
            )}
            {cvData.other && (
                <section className="cv-section other-info">
                    <h2>Other Info</h2>
                    <OtherInfo other={cvData.other} />
                </section>
            )}
            {cvData.personal && (
                <section className="cv-section personal-info">
                    <h2>Personal Info</h2>
                    <PersonalInfo personal={cvData.personal} />
                </section>
            )}
            {cvData.skills && (
                <section className="cv-section skills">
                    <h2>Skills</h2>
                    <SkillsDisplay skills={cvData.skills} />
                </section>
            )}
        </div>
    );
};

export default CVDisplay;
