import React from 'react';

const SkillsDisplay = ({ skills }) => (
    <div className="skills-display">
        <div className="skills-container">
            <h4>Computer Skills</h4>
            <div className="skills">
                {skills.computerSkill && skills.computerSkill.length > 0 ? (
                    skills.computerSkill.map((skill, index) => (
                        <span key={index} className="skill-badge">{skill}</span>
                    ))
                ) : (
                    <p>No computer skills available</p>
                )}
            </div>
        </div>
        <div className="skills-container">
            <h4>Language Skills</h4>
            <div className="skills">
                {skills.languageSkill && skills.languageSkill.length > 0 ? (
                    skills.languageSkill.map((langSkill, index) => (
                        <span key={index} className="skill-badge">
                            {langSkill.language} - {langSkill.level}
                        </span>
                    ))
                ) : (
                    <p>No language skills available</p>
                )}
            </div>
        </div>
        <div className="skills-container">
            <h4>Other Skills</h4>
            <div className="skills">
                {skills.otherSkill && skills.otherSkill.length > 0 ? (
                    skills.otherSkill.map((skill, index) => (
                        <span key={index} className="skill-badge">{skill}</span>
                    ))
                ) : (
                    <p>No other skills available</p>
                )}
            </div>
        </div>
        <div className="skills-container">
            <h4>Soft Skills</h4>
            <div className="skills">
                {skills.softSkill && skills.softSkill.length > 0 ? (
                    skills.softSkill.map((skill, index) => (
                        <span key={index} className="skill-badge">{skill}</span>
                    ))
                ) : (
                    <p>No soft skills available</p>
                )}
            </div>
        </div>
    </div>
);

export default SkillsDisplay;
