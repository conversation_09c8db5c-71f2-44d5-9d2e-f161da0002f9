version: '3.8'

services:
  postgres:
    image: postgres:15
    restart: unless-stopped
    environment:
      POSTGRES_USER: ${POSTGRES_USER}
      POSTGRES_PASSWORD: ${POSTGRES_PASSWORD}
      POSTGRES_DB: ${POSTGRES_DB}
    volumes:
      - pgdata:/var/lib/postgresql/data:z
    ports:
      - "${POSTGRES_PORT}:5432"

  adminer:
    image: adminer
    restart: unless-stopped
    ports:
      - "${POSTGRES_ADMINER_PORT}:8080"

volumes:
  pgdata:
