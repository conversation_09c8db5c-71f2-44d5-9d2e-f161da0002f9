import React, { useState, useEffect, useRef } from 'react';
import { FaFolder } from 'react-icons/fa';
import CVDisplay from './CVDisplay';
import DebugOutput from './DebugOutput';

import './CVParser.css';

const CVParser = ({
    card,
    externalResponse,
    handleSubmit,
    onFileUpload,
    onCloseFiles,
    files,
    externalInput,
    onResponse
}) => {
    const [isLoading, setIsLoading] = useState(false);
    const [cvData, setCvData] = useState(null);
    const [debugString, setDebugString] = useState('');
    const [selectedEngine, setSelectedEngine] = useState('vllm');
    const fileInputRef = useRef(null);

    useEffect(() => {
        try {
            const botResponses = externalResponse.filter(
                msg => msg.sender === 'bot' && msg.type === 'response'
            );
            if (botResponses.length === 0) return;

            const lastResponse = botResponses[botResponses.length - 1].content;
            if (lastResponse.length < 50) return;

            // Nuovo sistema di tracciamento
            let insideString = false;
            let escapeNext = false;
            const stack = [];
            const inverseChar = { '{': '}', '[': ']', '"': '"' };

            for (let i = 0; i < lastResponse.length; i++) {
                const char = lastResponse[i];

                if (insideString) {
                    if (escapeNext) {
                        escapeNext = false;
                    } else if (char === '\\') {
                        escapeNext = true;
                    } else if (char === '"') {
                        insideString = false;
                        if (stack[stack.length - 1] === '"') stack.pop();
                    }
                } else {
                    if (char === '"') {
                        insideString = true;
                        stack.push('"');
                    } else if (char === '{' || char === '[') {
                        stack.push(char);
                    } else if (char === '}' && stack[stack.length - 1] === '{') {
                        stack.pop();
                    } else if (char === ']' && stack[stack.length - 1] === '[') {
                        stack.pop();
                    }
                }
            }

            // Aggiungi i caratteri mancanti in ordine inverso
            const closing = stack.reverse().map(c => inverseChar[c]).join('');
            const final_str = lastResponse + closing;

            // UNCOMMENT IF YOU WANT TO LOOK RESULT IN FRONTEND
            // setDebugString(final_str);
            const data = JSON.parse(final_str);
            setCvData(data);

        } catch (error) {
            console.error("Failed to parse external response", error);
        }
    }, [externalResponse]);

    const submit = async (input, files, isStream = true) => {

        // TODO: o input o file dovrebbero non essere vuoti
        // if (!input.trim()) {
        //     alert('Input cannot be empty.');
        //     return;
        // }

        // TODO: andrebbe fatto il controllo che al massimo un cv può essere caricato

        setIsLoading(true); // Inizia il caricamento della risposta     

        try {

            const api_address = `/${card.title.replace(/[\s-]+/g, '_').toLowerCase()}`;
            // console.log(api_address)
            // console.log(input)
            // console.log(isStream)

            // Utilizza submit per inviare i dati
            await handleSubmit({
                apiEndpoint: api_address,
                input: input,
                selectedOption: null,
                files: files,
                isStream: isStream,
                engine: selectedEngine,
            });

        } catch (error) {
            console.error("Error during API call", error.message);
            onResponse("Error during API call", error.message);
        } finally {
            onCloseFiles(files[0]);
            setIsLoading(false);
        }
    };

    useEffect(() => {

        if (files && files.length > 0) {
            submit(externalInput, files, true);
        }
    }, [files]); // Si attiva ogni volta che files cambia

    return (
        <div className="functionality">

            {card.hasUpload && (

                <div className="upload-section">
                    <div className="info-box">
                        Please note: The first time you submit your CV, processing may take longer as the server is initializing. Subsequent uploads will be processed faster.
                    </div>

                    <div style={{ "padding-bottom": "20px" }}>
                        <div
                            className={`upload-area ${isLoading ? 'disabled' : ''}`}
                            onClick={(e) => {
                                if (!isLoading) { // Allow the click only if loading is not in progress
                                    e.stopPropagation()
                                    fileInputRef.current.click(); // Simulates click on input
                                }
                            }}
                            onDragOver={(e) => {
                                if (!isLoading) {
                                    e.preventDefault();
                                    e.stopPropagation();
                                    e.currentTarget.classList.add('drag-over');
                                }
                            }}
                            onDragLeave={(e) => {
                                if (!isLoading) {
                                    e.preventDefault();
                                    e.stopPropagation();
                                    e.currentTarget.classList.remove('drag-over');
                                }
                            }}
                            onDrop={(e) => {
                                if (!isLoading) {
                                    e.preventDefault();
                                    e.stopPropagation();
                                    e.currentTarget.classList.remove('drag-over');
                                    onFileUpload(e);
                                }
                            }}
                        >
                            <input
                                type="file"
                                id={`file-upload-${card.id}`}
                                accept=".pdf,.doc,.docx"
                                onChange={onFileUpload}
                                style={{ display: 'none' }}
                                ref={fileInputRef} // Assegna il riferimento all'input
                                disabled={isLoading}
                            />
                            <div>
                                <FaFolder /> Upload your document here
                            </div>
                            {files && files.length > 0 && <p>File uploaded: {files[0].name}</p>}
                            {isLoading && <p className="loading-message">Processing your document, please wait...</p>}
                        </div>
                    </div>
                </div>
            )}

            {/* Mostra il DebugOutput per vedere final_str */}
            {debugString && <DebugOutput debugString={debugString} />}

            {/* Se sono presenti dati, visualizza l'intero CV */}
            {cvData && <CVDisplay cvData={cvData} />}

        </div>
    );
};

export default CVParser;