{"name": "frontend", "version": "0.1.0", "private": true, "dependencies": {"@testing-library/jest-dom": "^5.17.0", "@testing-library/react": "^13.4.0", "@testing-library/user-event": "^13.5.0", "axios": "^1.7.7", "katex": "^0.16.22", "react": "^18.3.1", "react-dom": "^18.3.1", "react-icons": "^5.3.0", "react-markdown": "^9.0.1", "react-router-dom": "^6.30.0", "react-scripts": "5.0.1", "react-syntax-highlighter": "^15.6.1", "react-transition-group": "^4.4.5", "rehype-katex": "^7.0.1", "remark-breaks": "^4.0.0", "remark-gfm": "^4.0.1", "remark-math": "^6.0.0", "wavesurfer.js": "^7.8.13", "web-vitals": "^2.1.4"}, "scripts": {"start": "BROWSER=none react-scripts start", "build": "react-scripts build", "test": "react-scripts test", "eject": "react-scripts eject"}, "eslintConfig": {"extends": ["react-app", "react-app/jest"]}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}}