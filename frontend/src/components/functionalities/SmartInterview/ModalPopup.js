import React from 'react';
import './ModalPopup.css';

const ModalPopup = ({ visible, title, message, onConfirm, onCancel }) => {
    if (!visible) return null;

    return (
        <div className="modal-overlay">
            <div className="modal-content">
                <h2>{title}</h2>
                <p>{message}</p>
                <div className="modal-actions">
                    <button className="modal-button cancel" onClick={onCancel}>Cancel</button>
                    <button className="modal-button confirm" onClick={onConfirm}>Accept</button>
                </div>
            </div>
        </div>
    );
};

export default ModalPopup;
