from __future__ import annotations

import json
import os

from dotenv import load_dotenv
from openai import OpenAI

load_dotenv()

client = OpenAI(
    base_url=os.getenv("VLLM_ENDPOINT"),
    api_key=os.getenv("VLLM_API_KEY"),
)


# Note: In vLLM 0.8.4, tool calling only works when 
# --guided-decoding-backend is set to auto. 
# Although the XGrammar enum support fix (#15878) has been merged, 
# full structured-output compatibility isn't yet released.
# https://github.com/vllm-project/vllm/pull/15878

# https://github.com/leon-seidel/vllm/blob/aa1b82f43a3dbb3a9521fc93b35e07ebc89d5a25/vllm/model_executor/guided_decoding/utils.py

def get_weather(location: str, unit: str):
    return f"Getting the weather for {location} in {unit}..."


tool_functions = {"get_weather": get_weather}

tools = [
    {
        "type": "function",
        "function": {
            "name": "get_weather",
            "description": "Get the current weather in a given location",
            "parameters": {
                "type": "object",
                "properties": {
                    "location": {
                        "type": "string",
                        "description": "City and state, e.g., 'San Francisco, CA'",
                    },
                    "unit": {"type": "string", "enum": ["celsius", "fahrenheit"]},
                },
                "required": ["location", "unit"],
            },
        },
    }
]

response = client.chat.completions.create(
    model=client.models.list().data[0].id,
    messages=[{"role": "user", "content": "What's the weather like in San Francisco?"}],
    tools=tools,
    tool_choice="auto",
)

tool_call = response.choices[0].message.tool_calls[0].function
print(f"Function called: {tool_call.name}")
print(f"Arguments: {tool_call.arguments}")
print(f"Result: {get_weather(**json.loads(tool_call.arguments))}")
