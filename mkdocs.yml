# Tutorial
# https://jameswillett.dev/getting-started-with-material-for-mkdocs/
# https://squidfunk.github.io/mkdocs-material/getting-started/
site_name: AI Playground
repo_url: https://corporate-gitlab.gigroup.com/data-driven-engineering/ai-playground

theme:
  name: material
  palette:
    # Dark Mode
    - scheme: slate
      toggle:
        icon: material/weather-sunny
        name: Dark mode
      primary: green
      accent: deep purple

    # Light Mode
    - scheme: default
      toggle:
        icon: material/weather-night
        name: Light mode
      primary: blue
      accent: deep orange

  features:
    - navigation.tabs # Tabs in alto per sezioni (Docs, Conversational AI, API Reference)
    - navigation.top # Pulsante "Torna su"
    - navigation.sections # Sidebar con sezioni collassabili
    - navigation.expand # Espande automaticamente la sezione corrente
    - navigation.footer
    - toc.follow # ToC segue lo scroll
    - version

plugins:
  - search
  - mkdocstrings
  - autorefs
  - include
  - mkapi
  
markdown_extensions:
  - admonition
  - pymdownx.details
  - attr_list
  - footnotes
  - pymdownx.highlight:
      anchor_linenums: true
      line_spans: __span
      pygments_lang_class: true
  - pymdownx.inlinehilite
  - pymdownx.snippets
  - pymdownx.superfences:
      custom_fences:
        - name: mermaid
          class: mermaid
          format: !!python/name:pymdownx.superfences.fence_code_format
  - pymdownx.tabbed:
      alternate_style: true
  - pymdownx.emoji:
      emoji_index: !!python/name:material.extensions.emoji.twemoji
      emoji_generator: !!python/name:material.extensions.emoji.to_svg
  - toc:
      permalink: true
  - md_in_html

# Esempio di navigazione con sezioni
nav:
  - Docs:
      - Overview: index.md
      - AsyncOpenAI Performance Benchmarking: AsyncOpenAI Performance Benchmarking.md