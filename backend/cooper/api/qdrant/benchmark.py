from __future__ import annotations

import asyncio
import json
import os
import random
import time
from pathlib import Path

import httpx
import numpy as np
import polars as pl

# Run service.py
# > NOTA: DELLE VACANCY NON HANNO LATITUDINE E LONGITUDINE!

emb_version = "v02"
DATA_PATH = Path("/data/datascience/smart_matching/experiments_umberto")
employee_text_path = DATA_PATH / f"{emb_version}/employee_full_text.parquet"
qdrant_path = DATA_PATH / f"{emb_version}/models/finetuning+smart_matching+0.0.0/qdrant"

# Creare la cartella results se non esiste
qdrant_benchmark_path = qdrant_path / ".benchmark_results"
qdrant_benchmark_path.mkdir(parents=True, exist_ok=True)

random.seed(42)

API_SEARCH_URL = "http://localhost:5004/api/sm_search_documents"
N_REQUESTS = 1200  # Numero di richieste da simulare
BATCH_SIZE = 200
LIMIT_RESULTS = 100
MAX_OFFSET = 100  # Ipotizza di ottenere massimo fino al 1000 risultato
USE_GEO_FILTER = False  # Flag per decidere se usare il filtro geografico
REPEAT_VACANCY = False  # in una simulazione concorrent vuoi che si ripetano anche chiamate a stesse vacancy ogni tanto?

DATABASES = (
    pl.scan_parquet(employee_text_path)
    .select(pl.col("DATABASE").unique())
    .collect()["DATABASE"]
    .to_list()
)

QUERIES = []
for db in DATABASES:
    vacancy_ids = np.load(qdrant_path / f"{db}_vacancy_ids.npy")
    size_sample = N_REQUESTS // len(DATABASES) + 1
    QUERIES += np.random.choice(
        vacancy_ids,
        size=size_sample,
        replace=REPEAT_VACANCY,
    ).tolist()
QUERIES = QUERIES[:N_REQUESTS]

###########################
#  TODO: questa parte bo va rivista e capita
###########################
from cooper.api.qdrant.brand_stratified import Strificate


def extract_brand(text):
    # Gestione del caso in cui il JSON non sia valido
    try:
        return json.loads(text).get("brand", None)
    except Exception:
        return None


vacancy_text_path = DATA_PATH / f"{emb_version}/vacancy_full_text.parquet"
vacancy_full_text = pl.read_parquet(vacancy_text_path)
vacancy_full_text = vacancy_full_text.with_columns(
    pl.col("TEXT_VACANCY").map_elements(extract_brand).alias("brand")
)

s = Strificate()
combined_vacancies = s.stratify(vacancy_full_text)
QUERIES = s.create_unique_index(
    combined_vacancies=combined_vacancies,
    columns_to_combine=["DATABASE", "RECRUITMENT_ID"],
)
###########################
###########################

# TODO:
# - (FATTO) Creare 1 collection dei documents (candidates) e andare a caricarli in db
# - (FATTO) Creare API che riceve in input una query testuale (vacancy) e genera un vettore
# - (FATTO) Creare API che riceve vettore e fa ricerca in db tramite cosine similarity
# - (FATTO) Creare script che simula richieste dei recruiter in parallelo ricavando informazioni sui tempi medi
# - (FATTO) Aggiungere fake dati geografici lato candidates nel payload
# - (FATTO) Fare richieste con prefiltro geografico)
# - (FATTO) Ai fini di ottimizzazione del codice, caricare le queries in una collection a parte, con i corrispettivi id
# - (FATTO) Modificare get_encoded_vector dove otteniamo il vettore che ci serve dal db invece che calcolarlo ogni volta
# - Se non è presente -> errore (il vettore c'è nel momento in cui viene creata la vacancy o modificata)
# - (FATTO) Dare N_REQUESTS inidici query casuali (possono essere gli stessi anche, quindi con ripetizione) (impostare un seed) e valutare il benchmark con query
# - (FATTO) Mettere lat e lan sia lato candidato che lato query veri
# - verificare che i risultati coincidono con quelli di smart matching
# - ottimizzare ulteriormente creando delle collections documents e queries in base alla country, e.g.: candidates_documents_italy, vacancies_queries_italy, candidates_documents_spain, vacancies_queries_spain, ...

# # Funzione per generare coordinate fake
# def generate_fake_coordinates(probability_of_none=0.5):
#     if np.random.rand() < probability_of_none:
#         return None, None
#     lat = random.uniform(45.0, 46.0)  # Esempio di latitudine
#     lon = random.uniform(9.0, 10.0)  # Esempio di longitudine
#     return lat, lon


def generate_fake_radius():
    radius = random.randint(10000, 100000)  # Raggio è in metri, quindi tra 10km e 100km
    return radius


def generate_fake_offset():
    # Calcola il numero massimo di pagine possibili
    max_pages = MAX_OFFSET // LIMIT_RESULTS
    # Scegli una pagina casuale
    page_number = random.randint(0, max_pages - 1)
    # Calcola l'offset come multiplo della dimensione di pagina
    offset = page_number * LIMIT_RESULTS
    return 1  # offset


async def make_search_request(
    client: httpx.AsyncClient, query: str, limit: int, request_id: int
):
    """Esegue una singola richiesta di ricerca utilizzando il vettore."""

    # Le query sono nel formato "DATABASE-RECRUITMENT_ID"

    try:
        selectedDatabase, vacancyId_str = query.split("-")
    except ValueError:
        print(f"Query {query} non valida")
        return float("inf"), "Invalid query format"

    try:
        vacancyId = int(vacancyId_str)
    except ValueError:
        print(f"Vacancy ID non è un numero: {vacancyId_str}")
        return float("inf"), "Invalid vacancyId"

    radius = generate_fake_radius()  # Imposta un raggio
    offset = generate_fake_offset()  # Imposta un offset

    start_time = time.perf_counter()

    # Costruisci i dati da inviare come form-data
    form_data = {
        "vacancyId": vacancyId,
        "selectedDatabase": selectedDatabase,
        "useGeoFilter": USE_GEO_FILTER,  # Il backend gestisce la conversione in bool
        "radius": radius,
        "limit": limit,
        "offset": offset,
    }

    try:
        response = await client.post(API_SEARCH_URL, data=form_data, timeout=30.0)
        response.raise_for_status()
        response_json = response.json()
    except httpx.ReadTimeout:
        return float("inf"), "Timeout"
    except httpx.HTTPError as exc:
        print(f"HTTP error: {exc}")
        return float("inf"), str(exc)
    except Exception as exc:
        print(f"Errore generico: {exc}")
        return float("inf"), str(exc)

    end_time = time.perf_counter()
    elapsed_time = end_time - start_time

    # Processa la vacancy (doppio parsing sul campo "text")
    try:
        json_vacancy = json.loads(response_json["vacancy"]["text"])
        json_vacancy_text = json.loads(json_vacancy.pop("TEXT_VACANCY"))
        json_vacancy_final = {**json_vacancy, **json_vacancy_text}
    except Exception as e:
        print(f"Error processing vacancy: {e}")
        json_vacancy_final = {}

    # Processa i candidati
    list_json_candidates = []
    for cand in response_json["candidates"]:
        try:
            json_candidate = json.loads(cand["text"])
            json_candidate_text = json.loads(json_candidate.pop("TEXT_EMPLOYEE"))
            json_candidate_final = {**json_candidate, **json_candidate_text}
            list_json_candidates.append(json_candidate_final)
        except Exception as e:
            print(f"Error processing candidate: {e}")
            continue

    # Struttura finale
    output_data = {
        "elapsed_time": elapsed_time,
        "vacancy": json_vacancy_final,
        "candidates": list_json_candidates,
    }

    # Salva la risposta in un file JSON
    response_filename = qdrant_benchmark_path / f"response_{request_id}.json"
    with open(response_filename, "w") as f:
        json.dump(output_data, f, indent=4, ensure_ascii=False)

    return elapsed_time, response_json


async def simulate_requests():
    """Simula più richieste di ricerca in parallelo con un vettore pre-calcolato."""
    async with httpx.AsyncClient() as client:
        responses = []
        for i in range(0, N_REQUESTS, BATCH_SIZE):
            # Esegui le richieste in batch
            tasks = [
                make_search_request(client, QUERIES[j], LIMIT_RESULTS, j)
                for j in range(i, min(i + BATCH_SIZE, N_REQUESTS))
            ]
            batch_responses = await asyncio.gather(*tasks)
            responses.extend(batch_responses)

        # print(responses)
    # Estrai i tempi delle richieste
    requests_time = [
        resp[0] for resp in responses if resp[0] != float("inf")
    ]  # Escludi i timeout

    # Calcola la media dei tempi
    avg_time = sum(requests_time) / len(requests_time) if requests_time else 0

    return {
        "avg_time": avg_time,
        "n_requests": len(requests_time),
        "requests_time": requests_time,
    }


def main():
    async def run():
        print("INIT: simulate_requests")
        # Simuliamo le richieste
        result = await simulate_requests()
        print(result)

    asyncio.run(run())


if __name__ == "__main__":
    main()
