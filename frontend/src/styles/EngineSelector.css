.dropdown-header {
    padding: 10px;
    background-color: #f5f5f5;
    border: 1px solid #ccc;
    border-radius: 4px;
    cursor: pointer;
    text-align: center;
    transition: background-color 0.3s ease;
}

.dropdown-header:hover {
    background-color: #e0e0e0;
    /* Sfondo al passaggio del mouse */
}

.dropdown-list {
    background-color: white;
    border: 1px solid #ccc;
    border-radius: 4px;
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
    position: absolute;
    z-index: 1000;
    display: flex;
    bottom: 25px;
    left: 15px;
    flex-direction: column;
}

.dropdown-item {
    padding: 10px;
    cursor: pointer;
    transition: background-color 0.3s ease;
}

.dropdown-item.disabled {
    cursor: default;
    /* Indica che non è cliccabile */
    color: #aaa;
    /* Colore grigio per l'opzione disabilitata */
}

.dropdown-item:hover {
    background-color: #f0f0f0;
    /* Sfondo al passaggio del mouse per gli elementi */
}

/* Aggiungi questa regola per evitare il cambiamento di sfondo al passaggio del mouse sull'elemento disabilitato */
.dropdown-item.disabled:hover {
    background-color: transparent;
    /* Mantiene il background trasparente */
}

.dropdown-item-label {
    font-weight: bold;
    /* Grassetto per l'etichetta */
}

.dropdown-item-description {
    font-size: 0.85rem;
    /* Dimensione del font per la descrizione */
    color: #666;
    /* Colore della descrizione */
}