# Since the ".env" file is gitignored, you can use the ".env.example" file to
# build a new ".env" file when you clone the repo. Keep this file up-to-date
# when you add new variables to `.env`.

# This file will be committed to version control, so make sure not to have any
# secrets in it. If you are cloning this repo, create a copy of this file named
# ".env" and populate it with your secrets.

WORKDIR=/home/<USER>/workarea/ai_playground

# If you want to use huggingface_hub / huggingface_cli
HUGGINGFACE_TOKEN=

# React port (frontend)
PORT=3001

## AZURE OPENAI
#AZURE_OPENAI_DISTRIBUTION=gpt-35-turbo AZURE_OPENAI_API_VERSION=2024-05-01-preview
AZURE_OPENAI_DISTRIBUTION=gpt-4o
AZURE_OPENAI_ENDPOINT=https://smartmatching-qa-ai.openai.azure.com/
AZURE_OPENAI_API_KEY=
AZURE_OPENAI_API_VERSION=2024-05-01-preview

## VLLM
# deepseek-ai/DeepSeek-R1-Distill-Qwen-32B
# nm-testing/Llama-3.3-70B-Instruct-FP8-dynamic
# deepseek-ai/DeepSeek-R1-Distill-Llama-70B
# neuralmagic/Mistral-Small-24B-Instruct-2501-FP8-Dynamic
# Qwen/Qwen2.5-32B-Instruct-GPTQ-Int4
# Qwen/Qwen2.5-VL-7B-Instruct
# Qwen/QwQ-32B
# unsloth/QwQ-32B
# google/gemma-3-27b-it
# mistralai/Mistral-Small-3.1-24B-Instruct-2503
# meta-llama/Llama-4-Scout-17B-16E-Instruct
# meta-llama/Llama-4-Maverick-17B-128E-Instruct
# unsloth/Meta-Llama-3.1-8B-Instruct
# Qwen/Qwen2.5-VL-32B-Instruct
VLLM_DISTRIBUTION=Qwen/Qwen2.5-VL-32B-Instruct
VLLM_ENDPOINT=http://localhost:8081/v1
VLLM_API_KEY=token-abc123

## OLLAMA
# hf.co/bartowski/Llama-3.2-1B-Instruct-GGUF:latest
# hf.co/bartowski/Ministral-8B-Instruct-2410-GGUF:Q6_K_L
# hf.co/bartowski/Llama-3.2-3B-Instruct-GGUF:latest
OLLAMA_DISTRIBUTION=deepseek-r1:32b
OLLAMA_ENDPOINT=http://localhost:11434/v1
OLLAMA_API_KEY=ollama

## (DEPRECATED) LLAMA.CPP
## La ragione è semplicemente perché c'è ollama che è un wrapper di llama.cpp,
## in qualsiasi caso non voglio rimuoverlo dal codice, non si sa mai in futuro
# LLAMA_CPP_DISTRIBUTION=DeepSeek-R1-UD-Q2_K_XL
# LLAMA_CPP_ENDPOINT=http://localhost:8000/v1
# LLAMA_CPP_API_KEY=llama_cpp

## ELEVENLABS
ELEVENLABS_API_KEY=

# Qdrant db storage
SMART_MATCHING_QDRANT="/data/datascience/smart_matching/experiments_umberto/v05/models/finetuning+smart_matching+0.0.0/qdrant/qdrant_storage_test"
SMART_MATCHING_QDRANT_COLLECTION_QUERIES="vacancies_queries"
SMART_MATCHING_QDRANT_COLLECTION_DOCUMENTS="candidates_documents"
MULTIMODAL_RAG_QDRANT="/data/datascience/multimodal_rag/qdrant_storage"
MULTIMODAL_RAG_QDRANT_COLLECTION_DOCUMENTS="multimodal_rag"

# Cache
TRANSFORMERS_CACHE="/home/<USER>/.transformers_cache"
VLLM_CACHE="/data/datascience/.shared/.transformers_cache"

# Modelli
WHISPER_MODEL="openai/whisper-large-v3"
EMBEDDER_RAG_MODEL="intfloat/multilingual-e5-large-instruct"
EMBEDDER_SMART_MATCHING_MODEL="/data/datascience/smart_matching/experiments_umberto/v05/models/finetuning+smart_matching+0.0.0/smart_matching/final"

# Porte
SMART_MATCHING_QDRANT_PORT="6333"
MULTIMODAL_RAG_QDRANT_PORT="6334"
EMBEDDER_SMART_MATCHING_PORT="8089"
EMBEDDER_RAG_PORT="8090"
VLLM_PORT="8081"
WHISPER_PORT="8091"
MKDOCS_PORT="8080"
BACKEND_PORT="5003"

# CONTAINER
CONTAINER_SMART_MATCHING_QDRANT="qdrant_smart_matching_neural_similarity"
CONTAINER_RAG_QDRANT="qdrant_multimodal_rag"
CONTAINER_SMART_MATCHING_EMBEDDER="smart_matching_embedder"
CONTAINER_APP="cooper-backend"
CONTAINER_MKDOCS="mkdocs-server"

# POSTGRES data
POSTGRES_USER=
POSTGRES_PASSWORD=
POSTGRES_HOST=localhost
POSTGRES_PORT=5432
POSTGRES_DB=
POSTGRES_ADMINER_PORT=5433

# AUTH data
AUTH_SECRET_KEY=supersecretkey
AUTH_ALGORITHM=HS256
AUTH_ACCESS_TOKEN_EXPIRE_MINUTES=30


# GOOGLE (THIS IS A TEST for /home/<USER>/workarea/ai_playground/backend/cooper/api/ocr_beta/main_google.py)
GOOGLE_API_KEY=