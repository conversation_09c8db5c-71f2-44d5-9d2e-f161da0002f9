import React, { createContext, useContext, useState, useEffect } from "react";

const AuthContext = createContext();

export function AuthProvider({ children }) {
    // Inizializza il token dal localStorage (se presente)
    const [token, setToken] = useState(localStorage.getItem("token"));
    const [userId, setUserId] = useState(localStorage.getItem("user_id"));
    const [user, setUser] = useState(localStorage.getItem("username"));
    const [role, setRole] = useState(localStorage.getItem("role"));
    const [country, setCountry] = useState(localStorage.getItem("country"));
    const [brand, setBrand] = useState(localStorage.getItem("brand"));

    // Aggiorna localStorage quando il token cambia
    useEffect(() => {
        if (token) {
            localStorage.setItem("token", token);
        } else {
            localStorage.removeItem("token");
        }
    }, [token]);

    useEffect(() => {
        if (userId) {
            localStorage.setItem("user_id", userId);
        } else {
            localStorage.removeItem("user_id");
        }
    }, [userId]);

    useEffect(() => {
        if (user) {
            localStorage.setItem("user", user);
        } else {
            localStorage.removeItem("user");
        }
    }, [user]);

    useEffect(() => {
        if (role) {
            localStorage.setItem("role", role);
        } else {
            localStorage.removeItem("role");
        }
    }, [role]);

    useEffect(() => {
        if (country) {
            localStorage.setItem("country", country);
        } else {
            localStorage.removeItem("country");
        }
    }, [country]);

    useEffect(() => {
        if (brand) {
            localStorage.setItem("brand", brand);
        } else {
            localStorage.removeItem("brand");
        }
    }, [brand]);

    return (
        <AuthContext.Provider value={{
            token, setToken,
            userId, setUserId,
            user, setUser,
            role, setRole,
            country, setCountry,
            brand, setBrand,
        }}>
            {children}
        </AuthContext.Provider>
    );
}

// Hook personalizzato per accedere al contesto
export function useAuth() {
    return useContext(AuthContext);
}
