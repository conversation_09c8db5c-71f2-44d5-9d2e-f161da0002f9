/* Contenitore per il visualizzatore */
.audio-wave-visualizer-container {
    display: flex;
    justify-content: center;
    align-items: center;
    position: relative;
    width: 100%; /* Adatta alla larghezza */
    background: linear-gradient(135deg, #1e3c72, #2a5298);
    border-radius: 15px;
    overflow: hidden;
    box-shadow: 0 10px 20px rgba(0, 0, 0, 0.3);
}

/* Canvas per il rendering dell'onda */
.audio-wave-visualizer-container canvas {
    display: block;
    width: 100%; /* Adatta alla larghezza del contenitore */
    height: auto; /* Mantieni le proporzioni */
    background: transparent; /* Sfondo trasparente */
}

/* Effetto bagliore quando il bot è attivo */
.audio-wave-visualizer-container.active {
    animation: glowEffect 2s infinite alternate;
}

/* Bagliore dinamico */
@keyframes glowEffect {
    0% {
        box-shadow: 0 0 15px rgba(0, 123, 255, 0.8);
    }
    100% {
        box-shadow: 0 0 30px rgba(0, 123, 255, 1);
    }
}

/* Animazione dell'onda che pulsa */
@keyframes wavePulse {
    0%, 100% {
        transform: scale(1);
    }
    50% {
        transform: scale(1.03);
    }
}

/* Aggiunta di linee secondarie per un effetto artistico */
.audio-wave-visualizer-container::before,
.audio-wave-visualizer-container::after {
    content: '';
    position: absolute;
    width: 120%;
    height: 120%;
    background: radial-gradient(circle, rgba(0, 123, 255, 0.3), transparent);
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    border-radius: 50%;
    animation: pulseSecondary 3s infinite ease-in-out;
    pointer-events: none;
    z-index: 0;
}

.audio-wave-visualizer-container::after {
    animation-delay: 1.5s;
    background: radial-gradient(circle, rgba(0, 123, 255, 0.15), transparent);
}

/* Animazione delle linee secondarie */
@keyframes pulseSecondary {
    0% {
        transform: translate(-50%, -50%) scale(1);
        opacity: 1;
    }
    100% {
        transform: translate(-50%, -50%) scale(1.5);
        opacity: 0;
    }
}
