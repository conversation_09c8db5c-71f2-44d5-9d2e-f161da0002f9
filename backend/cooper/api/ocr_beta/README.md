Create the environment **ocr_beta**:

```shell
# Create and activate the env with conda
conda create --name=ocr_beta python=3.10 -y && conda activate ocr_beta && pip install uv

# Install production libs
uv pip install "markitdown[all]==0.1.1" "python-docx==1.1.2" "PyMuPDF==1.24.11" "Werkzeug==3.1.3" "pdf2image==1.17.0" "google-genai==1.14.0"
```

Remove the **ocr_beta** environment:
```shell
conda deactivate && conda remove --name=ocr_beta --all -y
```

LINK TUTORIAL -> https://qdrant.tech/documentation/search-precision/reranking-hybrid-search/



```shell
# QDRANT
docker run -p 6333:6333 -p 6334:6334 -v "qdrant_storage:/qdrant/storage:z" qdrant/qdrant
```