# backend/cooper/app.py
from __future__ import annotations

import argparse
import os

import uvicorn
from dotenv import load_dotenv
from fastapi import FastAPI
from fastapi.staticfiles import StaticFiles

from cooper.api.auth.router import auth_router
from cooper.api.database_postgres.router import db_router
from cooper.api.routes import api_router
from cooper.utils import get_logger

load_dotenv()

logger = get_logger(__name__)

logger.info("VLLM_DISTRIBUTION: %s", os.getenv("VLLM_DISTRIBUTION"))

# Configura gli argomenti da riga di comando con argparse
parser = argparse.ArgumentParser(description="Avvia il backend FastAPI")
parser.add_argument(
    "--mode",
    choices=["debug", "production"],
    default="production",
    help="Modalità di avvio (debug o production)",
)
parser.add_argument(
    "--port",
    type=int,
    default=5000,
    help="Porta su cui avviare il backend",
)
args = parser.parse_args()

# Determina se abilitare il reload
RELOAD = args.mode == "debug"
BACKEND_PORT = args.port

app = FastAPI()

# Registrazione del router
# Includi gli endpoint di autenticazione e db in un router separato
app.include_router(api_router, prefix="/api")
app.include_router(auth_router, prefix="/auth")
app.include_router(db_router, prefix="/db")


# Monta i file statici generati dal frontend
app.mount("/", StaticFiles(directory="frontend/build", html=True), name="static")

if __name__ == "__main__":
    logger.info(
        f"🚀 Avviando il backend in modalità {'DEBUG' if RELOAD else 'PRODUZIONE'} sulla porta {BACKEND_PORT}"
    )

    uvicorn.run(
        app,
        host="0.0.0.0",
        port=BACKEND_PORT,
        log_level="debug" if RELOAD else "info",
    )
