/* Stile generale per la visualizzazione del CV */
.cv-display {
    width: 97%;
    display: flex;
    flex-direction: column;
    gap: 20px;
    padding: 20px;
}

/* Ogni sezione del CV */
.cv-section {
    background-color: #f9f9f9;
    border-radius: 4px;
    padding: 20px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.cv-section h2 {
    margin-top: 0;
    margin-bottom: 10px;
    font-size: 1.5em;
    color: #333;
}

/* Stili per le card all'interno delle liste */
.education-card,
.employment-card,
.personal-info-card,
.other-info-card {
    background-color: #fff;
    border: 1px solid #ddd;
    border-radius: 4px;
    padding: 15px;
    margin-bottom: 15px;
}

/* Esempio per la lista delle skills */
.skills-display h4 {
    margin-bottom: 5px;
}

.skills-display ul {
    list-style-type: disc;
    margin-left: 20px;
}

.skills-container {
    background-color: #e9f5f5;
    /* Colore di sfondo per il box delle skills */
    border: 1px solid #ccc;
    /* Bordo del box */
    border-radius: 4px;
    padding: 15px;
    margin-bottom: 20px;
}

.skills {
    display: flex;
    flex-wrap: wrap;
    /* Permette di andare a capo se ci sono molte skills */
}

.skill-badge {
    background-color: #d1e7dd;
    border-radius: 12px;
    padding: 5px 10px;
    margin: 5px;
    border: 1px solid #aaa;
    font-size: 0.9em;
}

.info-box {
    background-color: #fff3cd;
    color: #856404;
    border: 1px solid #ffeeba;
    padding: 10px 15px;
    margin-bottom: 20px;
    border-radius: 4px;
    font-size: 0.9em;
}