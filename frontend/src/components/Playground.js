import React, { useState, useEffect, useRef } from 'react';
import { MdOutlineArticle } from 'react-icons/md';
import { FaRobot } from 'react-icons/fa';
import { AiFillThunderbolt } from 'react-icons/ai';
import { IoIosGitNetwork } from "react-icons/io";
import { BsFileEarmarkText } from 'react-icons/bs'; // Per CV Parser
import { FaVideo } from 'react-icons/fa'; // Per Smart Interview
import { HiOutlineOfficeBuilding } from 'react-icons/hi'; // Per Internal Job Posting
import Card from './Card';
import ExpandedCard from './ExpandedCard';
import '../styles/styles.css';
import api from '../services/api';
import ProtectedComponent from '../services/ProtectedComponent';
import brickSingleFunc from '../assets/brickSingleFunc.png';
import brickMultipleFunc from '../assets/brickMultipleFunc.png';
import StreamingAudioPlayer from "./StreamingAudioPlayer";
import { FaRegStar, FaStar } from 'react-icons/fa';
import Logout from '../services/Logout';


const Playground = () => {
    const [expandedCard, setExpandedCard] = useState(null);
    const [selectedCategory, setSelectedCategory] = useState('All');
    const [sidebarOpen, setSidebarOpen] = useState(false);
    const [response, setResponse] = useState([]);
    const [files, setFiles] = useState([]);
    const [externalInput, setExternalInput] = useState('');
    const [isMicActive, setIsMicActive] = useState(false);
    const [isDocsDropdownOpen, setIsDocsDropdownOpen] = useState(false);
    const audioRef = useRef(null);

    const CHAT_HISTORY_BUFFER = 10; // Parametrizza il limite della cronologia delle chat
    const [chatHistory, setChatHistory] = useState([]);

    const [textChunks, setTextChunks] = useState([]);

    const [cards, setCards] = useState([
        {
            id: 153453,
            type: "JobAdWriter",
            title: "Job-ad writer",
            description: "Write a professional job ad in seconds, without sharing confidential data with third parties.",
            category: "For Recruiters",
            color: "#C3DED6", options: [
                { value: "gigroup", label: "Gi Group" },
                { value: "grafton", label: "Grafton" },
                { value: "wyser", label: "Wyser" },
            ],
            hasUpload: true,
            hasTextInput: true,
            icon: <MdOutlineArticle />,
            isFavorite: false,
            brick: brickSingleFunc,
            minVisibilityLevel: 100,
        },
        {
            id: 225346,
            type: "CVParser",
            title: "CV Parser",
            description: "Find relevant information on a Candidate's CV.",
            category: "For Recruiters",
            color: "#C3DED6",
            hasUpload: true,
            hasTextInput: false,
            icon: <BsFileEarmarkText />,
            isFavorite: false,
            brick: brickSingleFunc,
            minVisibilityLevel: 100,
        },
        {
            id: 534343,
            type: "NextJobRecommender",
            title: "Next job recommender",
            description: "Plan your next career step.",
            category: "For Candidates",
            color: "#CEE741",
            hasUpload: false,
            hasTextInput: false,
            icon: <IoIosGitNetwork />,
            isFavorite: false,
            brick: brickMultipleFunc,
            minVisibilityLevel: 100,
        },
        {
            id: 154565,
            type: "ChatBot",
            title: "GiGPT",
            description: "Discover things about Gi Group.",
            category: "For Recruiters",
            color: "#C3DED6",
            hasUpload: true,
            hasTextInput: true,
            icon: <FaRobot />,
            isFavorite: false,
            brick: brickMultipleFunc,
            minVisibilityLevel: 100,
        },
        {
            id: 154585,
            type: "SmartMatching",
            title: "Smart Matching",
            description: "Find suitable Candidates for screening, hidden in your DB, before you spend money with job boards.",
            category: "For Recruiters",
            color: "#C3DED6",
            hasUpload: false,
            hasTextInput: true,
            icon: <AiFillThunderbolt />,
            isFavorite: false,
            brick: brickMultipleFunc,
            minVisibilityLevel: 100,
        },
        {
            id: 1545876,
            type: "SmartMatchingTest",
            title: "Smart Matching Test",
            description: "Find suitable Candidates for screening, hidden in your DB, before you spend money with job boards.",
            category: "For Recruiters",
            color: "#C3DED6",
            hasUpload: false,
            hasTextInput: true,
            icon: <AiFillThunderbolt />,
            isFavorite: false,
            brick: brickMultipleFunc,
            minVisibilityLevel: 30,
        },
        // {
        //     id: 154535,
        //     type: "AskAnswer",
        //     title: "Python Code Assistant",
        //     description: "Get help with Python code snippets, debugging, and optimization techniques.",
        //     category: "For Clients",
        //     color: "#12CDD4",
        //     icon: null,
        //     isFavorite: false,
        // },
        // {
        //     id: 164353,
        //     type: "AskAnswer",
        //     title: "Java Application Builder",
        //     description: "Easily create and manage Java applications with step-by-step guidance.",
        //     category: "For Clients",
        //     color: "#12CDD4",
        //     icon: null,
        //     isFavorite: false,
        // },
        {
            id: 103253,
            type: "SmartInterview",
            title: "Smart Interview",
            description: "Conduct virtual interviews with real-time video, audio insights, and AI-generated questions for effortless recruiting.",
            category: "For Candidates",
            color: "#CEE741",
            hasUpload: false,
            hasTextInput: false,
            icon: <FaVideo />,
            isFavorite: false,
            brick: brickMultipleFunc,
            minVisibilityLevel: 100,
        },
        {
            id: 999999,
            type: "InternalJobPosting",
            title: "Internal job posting",
            description: "Find Employees in your organization to cover your new openings. Predict their skill gaps and training needs.",
            category: "For Clients",
            color: "#12CDD4", // in linea con i tuoi colori
            hasUpload: false,
            hasTextInput: false,
            icon: <HiOutlineOfficeBuilding />,
            isFavorite: false,
            minVisibilityLevel: 100,
        },
        {
            id: 345543,
            type: "AskAnswer",
            title: "Proposed vacancies",
            description: "...",
            category: "For Recruiters",
            color: "#C3DED6",
            options: [
                { value: "gigroup", label: "Gi Group" },
                { value: "grafton", label: "Grafton" },
                { value: "wyser", label: "Wyser" },
            ],
            hasUpload: false,
            hasTextInput: false,
            icon: null,
            isFavorite: false,
            minVisibilityLevel: 100,
        },
        {
            id: 1534533,
            type: "JobAdWriter",
            title: "Job-ad writer",
            description: "Write a professional job ad in seconds.",
            category: "For Clients",
            color: "#12CDD4",
            hasUpload: true,
            hasTextInput: true,
            icon: null,
            isFavorite: false,
            minVisibilityLevel: 100,
        },
        {
            id: 454444,
            type: "AskAnswer",
            title: "Recruiter note interpreter",
            description: "Write a note about a Candidate and let me fill-in the relevant fields for you.",
            category: "For Recruiters",
            color: "#C3DED6",
            hasUpload: false,
            hasTextInput: true,
            icon: null,
            isFavorite: false,
            minVisibilityLevel: 100,
        },
        {
            id: 634333,
            type: "AskAnswer",
            title: "Vacancy finder",
            description: "Find the most suitable jobs for you, amongst active vacancies.",
            category: "For Candidates",
            color: "#CEE741",
            hasUpload: false,
            hasTextInput: false,
            icon: null,
            isFavorite: false,
            minVisibilityLevel: 100,
        },
        {
            id: 723686,
            type: "AskAnswer",
            title: "CV wizard",
            description: "Get suggestions on how to make your CV more appealing to recruiters and hiring managers.",
            category: "For Candidates",
            color: "#CEE741",
            hasUpload: true,
            hasTextInput: false,
            icon: null,
            isFavorite: false,
            minVisibilityLevel: 100,
        },
        {
            id: 894984,
            type: "AskAnswer",
            title: "Upskilling",
            description: "...",
            category: "For Clients",
            color: "#12CDD4",
            hasUpload: false,
            hasTextInput: false,
            icon: null,
            isFavorite: false,
            minVisibilityLevel: 100,
        },
        {
            id: 913454,
            type: "AskAnswer",
            title: "Text Summarization",
            description: "Upload a document and get a summarization of it.",
            category: "For Clients",
            color: "#12CDD4",
            hasUpload: true,
            hasTextInput: false,
            icon: null,
            isFavorite: false,
            minVisibilityLevel: 100,
        },
        {
            id: 112333,
            type: "AskAnswer",
            title: "CV Parser",
            description: "Find relevant information on a Candidate's CV.",
            category: "For Recruiters",
            color: "#C3DED6",
            hasUpload: true,
            hasTextInput: false,
            icon: null,
            isFavorite: false,
            minVisibilityLevel: 100,
        },
        {
            id: 125566,
            type: "AskAnswer",
            title: "Proposed vacancies",
            description: "...",
            category: "For Recruiters",
            color: "#C3DED6",
            hasUpload: false,
            hasTextInput: false,
            options: [
                { value: "gigroup", label: "Gi Group" },
                { value: "grafton", label: "Grafton" },
                { value: "wyser", label: "Wyser" }
            ],
            icon: null,
            isFavorite: false,
            minVisibilityLevel: 100,
        },
        {
            id: 137777,
            type: "AskAnswer",
            title: "Recruiter note interpreter",
            description: "Write a note about a Candidate and let me fill-in the relevant fields for you.",
            category: "For Recruiters",
            color: "#C3DED6",
            hasUpload: false,
            hasTextInput: true,
            icon: null,
            isFavorite: false,
            minVisibilityLevel: 100,
        },
        {
            id: 143345,
            type: "AskAnswer",
            title: "Job-ad writer",
            description: "Write a professional job ad in seconds.",
            category: "For Clients",
            color: "#12CDD4",
            hasUpload: true,
            hasTextInput: true,
            icon: null,
            isFavorite: false,
            minVisibilityLevel: 100,
        },
        {
            id: 162346,
            type: "AskAnswer",
            title: "Vacancy finder",
            description: "Find the most suitable jobs for you, amongst active vacancies.",
            category: "For Candidates",
            color: "#CEE741",
            hasUpload: false,
            hasTextInput: false,
            icon: null,
            isFavorite: false,
            minVisibilityLevel: 100,
        },
        {
            id: 537343,
            type: "NextJobRecommender",
            title: "Next job recommender",
            description: "Plan your next career step.",
            category: "For Recruiters",
            color: "#C3DED6",
            hasUpload: false,
            hasTextInput: false,
            icon: <IoIosGitNetwork />,
            isFavorite: false,
            brick: brickMultipleFunc,
            minVisibilityLevel: 100,
        },
        {
            id: 176644,
            type: "AskAnswer",
            title: "CV wizard",
            description: "Get suggestions on how to make your CV more appealing to recruiters and hiring managers.",
            category: "For Candidates",
            color: "#CEE741",
            hasUpload: true,
            hasTextInput: false,
            icon: null,
            isFavorite: false,
            minVisibilityLevel: 100,
        },
        {
            id: 189765,
            type: "AskAnswer",
            title: "Upskilling",
            description: "...",
            category: "For Clients",
            color: "#12CDD4",
            hasUpload: false,
            hasTextInput: false,
            icon: null,
            isFavorite: false,
            minVisibilityLevel: 100,
        },
    ]);

    const onExpand = (cardId) => {
        setExpandedCard(cardId);
        setSidebarOpen(false);
        setResponse([]);
        setFiles([]);
        setExternalInput('');
    };

    const onCloseFunc = () => {
        setExpandedCard(null);
        setResponse([]);
        setFiles([]);
        setExternalInput('');
        setChatHistory([]);
        setTextChunks([]);
    }

    const onResponse = (apiResponse) => {
        setResponse(apiResponse);
    };

    const onExternalInput = (externalInput) => {
        setExternalInput(externalInput);
    };

    // TODO: mi sa che non viene usato mai
    const resetResponse = () => {
        setResponse([]);
        setExternalInput('');
    };

    const handleMouseEnter = () => {
        setIsDocsDropdownOpen(true);
    };

    const handleMouseLeave = () => {
        setIsDocsDropdownOpen(false);
    };

    const toggleFavorite = (id) => {
        const updatedCards = cards.map(card => {
            if (card.id === id) {
                return { ...card, isFavorite: !card.isFavorite }; // Inverti lo stato di `isFavorite`
            }
            return card;
        });
        setCards(updatedCards); // Aggiorna lo stato delle cards
    };

    const onFileUpload = (event) => {
        event.stopPropagation();
        event.preventDefault();
        const files = event.target.files || event.dataTransfer.files;

        // if there is at least one file loaded 
        if (files.length > 0) {
            const uploadedFiles = [];

            // Formati supportati
            const allowedTypes = [
                'application/pdf',
                'application/msword',
                'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
                'image/jpeg',
                'image/png',
                'image/gif',
            ];

            // Itera sui file caricati
            for (const file of files) {
                if (file.size <= 10 * 1024 * 1024 && allowedTypes.includes(file.type)) {
                    uploadedFiles.push(file);
                } else {
                    alert('File not valid. Load a PDF, Word or images file with max 10MB size.');
                }
            }

            // Aggiorna lo stato con i file caricati
            setFiles((prevFiles) => [...prevFiles, ...uploadedFiles]);
        }
    };

    const onCloseFiles = (fileToRemove) => {
        setFiles((prevFiles) => prevFiles.filter((file) => file !== fileToRemove));
    };

    const categories = ['All', 'For Recruiters', 'For Candidates', 'For Clients'];

    const categoryClasses = {
        'All': 'all',
        'For Recruiters': 'for-recruiters',
        'For Candidates': 'for-candidates',
        'For Clients': 'for-clients'
    };

    // Definisci 'filteredCards' in base alla categoria selezionata
    const filteredCards = selectedCategory === 'All'
        ? cards
        : cards.filter(card => card.category === selectedCategory);


    const splitIntoChunks = (accumulatedResponse) => {
        // Sostituisce multipli \n con un singolo \n e rimuove spazi iniziali e finali
        let cleanedResponse = accumulatedResponse
            .replace(/\n+/g, "\n")
            .trim();

        // Rimuove i punti tra lettere (es. trasforma "S.p.A." in "SpA.")
        let prev;
        do {
            prev = cleanedResponse;
            cleanedResponse = cleanedResponse.replace(/([A-Za-z])\.([A-Za-z])/g, "$1$2");
        } while (cleanedResponse !== prev);

        // Definiamo i delimitatori per lo split (punto, esclamativo, interrogativo, chiusura di parentesi seguiti da spazi)
        const delimiters = /([.!?)]\s*)/;
        const parts = cleanedResponse.split(delimiters);
        const chunks = [];

        // Combina le parti in chunk, mantenendo il delimitatore insieme alla frase
        for (let i = 0; i < parts.length - 1; i += 2) {
            const sentence = parts[i].trim();
            const delimiter = parts[i + 1]?.trim() || "";
            const chunk = sentence + delimiter;
            if (chunk.length >= 20) {
                chunks.push(chunk);
            }
        }

        return chunks;
    };

    const parseSSEChunk = (rawChunk) => {

        const events = rawChunk.split('\n\n'); // Split su doppio newline, separa i vari "data: {...}"
        const results = [];

        for (const event of events) {
            if (event.trim() === '') continue; // Salta vuoti

            // NOTA: è obbligatorio avere il prefisso data per rispettare il protocollo SSE (Server-Sent Events).
            // NOTA: Rimuovo il prefisso "data: " e trimmo per sicurezza
            // NOTA: Il metodo .trim() in JavaScript rimuove solo eventuali spazi bianchi o caratteri di newline 
            // (a capo \n, \r, \t, ecc.) all'inizio e alla fine della stringa.
            // Non toglie nulla all'interno della stringa!
            const cleanChunk = event.replace(/^data:\s*/, '').trim();

            try {
                const parsed = JSON.parse(cleanChunk);
                results.push(parsed); // Aggiungi al risultato
            } catch (err) {
                console.error("❌ Errore parsing JSON:", err, "⚙️ Chunk ricevuto:", cleanChunk);
            }
        }

        return results; // Restituisco una lista di eventi
    };

    const appendMessage = (sender, type, content) => {
        setResponse((prevMessages) => {
            // Se non ci sono messaggi, aggiungiamo il primo
            if (prevMessages.length === 0) {
                return [{ sender, type, content }];
            }

            // Prendiamo l'ultimo messaggio
            const lastMessage = prevMessages[prevMessages.length - 1];

            // Se è di tipo thinking o response e sender=bot, concateniamo il contenuto al messaggio precedente
            if (
                lastMessage.sender === sender &&
                lastMessage.type === type &&
                (type === "thinking" || type === "response")
            ) {
                const updatedLast = { ...lastMessage, content: lastMessage.content + content };
                return [...prevMessages.slice(0, -1), updatedLast];
            }

            // Altrimenti, aggiungiamo un nuovo messaggio
            return [...prevMessages, { sender, type, content }];
        });
    };



    // Definisci handleSubmit centralizzata
    const handleSubmit = async ({
        apiEndpoint,
        input,
        selectedOption,
        files,
        engine,
        isStream = true,
        withChatHistory = false,
        textToSpeech = false,
    }) => {

        try {

            let CHAT_HISTORY_LIMIT = 5;

            if (engine === 'ollama') {
                CHAT_HISTORY_LIMIT = 2;
            }

            // create object with data to send to the API
            const formData = new FormData();
            formData.append('input', input);
            formData.append('isStream', isStream);
            formData.append('selectedOption', selectedOption);
            formData.append('engine_name', engine);

            if (files && files.length > 0) {
                files.forEach((file) => {
                    formData.append('files', file); // Invia la lista dei file
                });
            }

            // Send also chat history if you want to
            if (withChatHistory) {
                const chatHistoryLimit = chatHistory.slice(-CHAT_HISTORY_LIMIT)
                formData.append('chatHistory', JSON.stringify(chatHistoryLimit));
                console.log(chatHistoryLimit);
            }

            if (isStream) {

                // Chiudi il ciclo dei chunks per TTS
                if (textToSpeech) setTextChunks([]);

                formData.forEach((value, key) => {
                    console.log(`${key}:`, value);
                });

                const responseStream = await fetch('api' + apiEndpoint, {
                    method: 'POST',
                    body: formData,
                });
                appendMessage("user", "question", input);

                // rimuovi tutti i files
                setFiles([]);

                // Legge lo stream di token e aggiorna `response` ad ogni nuovo token
                const reader = responseStream.body.getReader();
                const decoder = new TextDecoder();
                let done = false;
                let accumulatedResponse = '';

                while (!done) {

                    // legge un "chunk" (una parte) di dati dallo stream. Poiché reader.read()
                    // è una funzione asincrona, viene utilizzato await per attendere il completamento della lettura.
                    const { value, done: isDone } = await reader.read();
                    done = isDone;

                    // se value è null o undefined (ad esempio alla fine dello stream), viene 
                    // passato un array vuoto (new Uint8Array()) per evitare errori
                    const chunk = decoder.decode(value || new Uint8Array(), { stream: !done });

                    // Fai quello che vuoi con type e content
                    const parsedChunks = parseSSEChunk(chunk);
                    for (const parsed of parsedChunks) {
                        const { type, content } = parsed;
                        // console.log("✅ Ricevuto dal backend:", type, content);

                        appendMessage("bot", type, content);

                        if (type === "response") {
                            accumulatedResponse += content;
                        }

                        // Aggiungi chunks per TTS, se attivo
                        if (textToSpeech) {
                            setTextChunks(() => {
                                console.log("Updated accumulatedResponse:", splitIntoChunks(accumulatedResponse));
                                return splitIntoChunks(accumulatedResponse);
                            });
                        }
                    }

                }

                // append new data to chat history
                if (withChatHistory) {
                    setChatHistory(prevHistory => {

                        const newHistory = [
                            ...prevHistory,
                            { role: 'user', content: input },
                            { role: 'system', content: accumulatedResponse }, // qui metti la response vera
                        ];

                        // Limita la lunghezza della chat history
                        return newHistory.slice(-CHAT_HISTORY_BUFFER);
                    });
                }

            } else {

                // Simulate API call
                // await new Promise(resolve => setTimeout(resolve, 1000));
                // const apiResponse = `Response for ${card.title}`;

                // Real API
                const res = await api.post(apiEndpoint, formData, {
                    headers: {
                        'Content-Type': 'multipart/form-data',
                    },
                });
                // console.log(res);

                // -----------
                setResponse(res.data);
            }

        } catch (error) {
            console.error("Error during API call", error.message);
            setResponse("Error during API call", error.message);
        }
    };

    const currentCard = expandedCard
        ? cards.find(card => card.id === expandedCard)
        : null;

    // Esempio di funzione per gestire il click sulla stellina
    const handleFavoriteClick = (e) => {
        e.stopPropagation();
        if (!currentCard) return;
        toggleFavorite(currentCard.id);
    };

    return (
        <div className={`playground ${expandedCard ? 'expanded-layout' : ''}`}>
            <StreamingAudioPlayer
                textChunks={textChunks}
                audioRef={audioRef}
                useLocalTTS={false} // SET false to use EVENLABS
            />
            <div className='playground-header'>
                <div className='playground-logo'>
                    {/* <img
                        src="/images/logo.png"
                        alt="Gi Group Logo"
                        className="playground-logo-image"
                    /> */}
                    <div style={{ display: "flex", flexDirection: "column" }}>
                        <h1 className={`typewriter playground-title ${expandedCard ? 'expanded-title' : ''}`}>AI Playground</h1>
                        <p className={`playground-subtitle ${expandedCard ? 'expanded-title' : ''}`}>Powered by the Global R&D Team</p>
                    </div>
                </div>
                <div className="playground-header-actions">
                    {/* Documentation Dropdown */}
                    <div className="playground-header-dropdown"
                        onMouseEnter={handleMouseEnter}
                        onMouseLeave={handleMouseLeave}
                    >
                        <button className="playground-header-dropdown-button">
                            Documentation
                            <svg
                                xmlns="http://www.w3.org/2000/svg"
                                width="16"
                                height="16"
                                fill="none"
                                aria-hidden="true"
                                className={`arrow-icon ${isDocsDropdownOpen ? 'rotate-180' : ''}`}
                            >
                                <path
                                    stroke="currentColor"
                                    strokeWidth="1.5"
                                    d="m4 6 4 4 4-4"
                                ></path>
                            </svg>
                        </button>
                        {isDocsDropdownOpen && (
                            <div className="playground-header-dropdown-menu">
                                <div
                                    className="playground-header-dropdown-item"
                                    onClick={() => window.location.href = "/docs"}
                                >
                                    Swagger Docs
                                </div>
                                <div
                                    className="playground-header-dropdown-item"
                                    onClick={() => window.location.href = "/redoc"}
                                >
                                    Redoc Docs
                                </div>
                            </div>
                        )}
                    </div>
                    <Logout />
                </div>
            </div>


            <div className="" style={{ display: "flex" }}>
                <div className="" style={{ backgroundColor: "rgb(128, 128, 128)", position: "relative", width: "3%", marginRight: "5px" }}></div>
                {/* #ebebeb */}
                <div className="" style={{ width: "96%" }}>
                    {expandedCard && (<div className="playground-menu">
                        {/* <div className="category-selector">
                        {categories.map(category => (
                            <button
                                key={category}
                                onClick={() => setSelectedCategory(category)}
                                className={`category-button ${categoryClasses[category]} ${selectedCategory === category ? 'active' : ''}`}
                            >
                                    {category}
                                </button>
                            ))}
                        </div> */}

                        {/* Qui sposto il "blocco" preso da ExpandedCard */}
                        {currentCard && (
                            <div style={{ display: "flex", gap: "45px" }}>
                                <div
                                    className={`playground-expanded-card`}
                                    style={{ backgroundColor: currentCard.color }}
                                >
                                    <div className="card-header">
                                        <div className="playground-expanded-card-title">
                                            {currentCard.icon &&
                                                React.cloneElement(currentCard.icon, {
                                                    style: { fontSize: '1.5em' }
                                                })}
                                            <h3 style={{ fontSize: '1em' }}>{currentCard.title}</h3>
                                        </div>
                                    </div>
                                    <p style={{ margin: "0px", fontSize: "x-small" }}>{currentCard.description}</p>
                                </div>

                                <div className="card-actions">
                                    {currentCard.brick && (
                                        <img
                                            src={currentCard.brick}
                                            alt="Brick Icon"
                                            style={{ width: '25px', height: '25px' }}
                                        />
                                    )}
                                    <div
                                        onClick={handleFavoriteClick}
                                        style={{ cursor: 'pointer' }}
                                    >
                                        {currentCard.isFavorite ? (
                                            <FaStar style={{ color: 'yellow', fontSize: '1.7em' }} />
                                        ) : (
                                            <FaRegStar style={{ fontSize: '1.7em' }} />
                                        )}
                                    </div>
                                </div>
                            </div>
                        )}


                        <div className='header-buttons'>

                            <button className="toggle-sidebar" onClick={() => setSidebarOpen(!sidebarOpen)}>
                                {sidebarOpen ? '>' : '<'}
                            </button>
                            <button className="close-button" onClick={() => onCloseFunc()}>X</button>
                        </div>

                    </div>
                    )}
                    <div className="content-area">

                        {expandedCard && (
                            <ExpandedCard
                                card={cards.find(card => card.id === expandedCard)}
                                isSidebarOpen={sidebarOpen} // Passiamo lo stato della sidebar
                                externalResponse={response} // Passa response a ExpandedCard
                                handleSubmit={handleSubmit} // Passa handleSubmit come prop
                                onFileUpload={onFileUpload}
                                onCloseFiles={onCloseFiles}
                                files={files}
                                externalInput={externalInput}
                                onResponse={onResponse} // print a specific response, useful for errors in frontend
                                toggleFavorite={toggleFavorite}
                                isMicActive={isMicActive} // Passa lo stato globale del microfono
                                setIsMicActive={setIsMicActive}
                                audioRef={audioRef}
                            />
                        )}
                        <div className={`card-container ${expandedCard ? 'sidebar' : ''} ${sidebarOpen ? 'open' : 'closed'}`}>
                            {filteredCards.map((card) => (
                                <ProtectedComponent minLevel={card.minVisibilityLevel} key={card.id}>
                                    <Card
                                        card={card}
                                        onExpand={() => onExpand(card.id)}
                                        style={{ backgroundColor: card.color }}
                                        onFileUpload={onFileUpload}
                                        isExpanded={expandedCard === card.id}
                                        resetResponse={resetResponse}
                                        handleSubmit={handleSubmit}
                                        onResponse={onResponse} // print a specific response, useful for errors in frontend
                                        files={files}
                                        onExternalInput={onExternalInput}
                                        toggleFavorite={toggleFavorite}
                                        isMicActive={isMicActive} // Passa lo stato globale del microfono
                                        setIsMicActive={setIsMicActive}
                                    />
                                </ProtectedComponent>
                            ))}
                        </div>
                    </div>
                </div>
            </div>
        </div >
    );
};

export default Playground;
