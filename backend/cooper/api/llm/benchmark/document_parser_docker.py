from __future__ import annotations

import argparse
import asyncio
import importlib.util
import json
import shutil
import time
from datetime import datetime
from pathlib import Path

import polars as pl
import yaml
from cooper.api.llm.benchmark.collect_env import get_pretty_env_info
from cooper.api.llm.benchmark.json_utils import clean_json_keys, safe_json_loads

# Se queste utilità sono disponibili altrove, aggiorna l'import di conseguenza
from cooper.utils import get_logger, parse_yaml

# Questa import fa parte dello script "personalizzato" con la classe AsyncOpenAI
# Assicurati che la libreria o la classe esista nel tuo progetto
from openai import AsyncOpenAI

# Logging configuration
logger = get_logger(__name__)


def load_template_module_from_path(path: str):
    """
    Dinamicamente carica un modulo Python da un dato path sul filesystem.
    """
    spec = importlib.util.spec_from_file_location("template_module", path)
    template_module = importlib.util.module_from_spec(spec)
    spec.loader.exec_module(template_module)
    return template_module


async def make_generate_request(
    semaphore: asyncio.Semaphore,
    client: AsyncOpenAI,
    request_id: int,
    chat_history: list | None,
    document_text: str,
    document_id: str,
    model_name: str,
    system_content: str,
    use_stream: bool,
    extra_body: dict | None,
    results_dir: Path,
):
    """
    Esegue una singola richiesta al server tramite AsyncOpenAI, gestendo il semaforo per limitare la concorrenza.
    """
    async with semaphore:
        start_time = time.perf_counter()

        try:
            messages = [
                {"role": "system", "content": system_content},
                *chat_history,
                {"role": "user", "content": document_text},
            ]
            # Esegui la chiamata diretta al server vLLM/OpenAI-like
            response = await client.chat.completions.create(
                model=model_name,
                messages=messages,
                stream=use_stream,
                extra_body=extra_body,
            )
            generated_text = response.choices[0].message.content

        except Exception as e:
            logger.error("Errore nella richiesta %s: %s", request_id, e)
            return float("inf"), str(e), request_id

        # Tenta di parsare il testo come JSON
        if extra_body:
            try:
                generated_text = safe_json_loads(clean_json_keys(generated_text))
            except Exception as e:
                logger.error(
                    "Errore nel parsing JSON per richiesta %s: %s", request_id, e
                )
                return float("inf"), str(e), request_id

        end_time = time.perf_counter()
        elapsed_time = end_time - start_time

        # Salva la risposta completa in un file JSON
        response_filename = results_dir / f"response_{request_id}.json"
        with Path(response_filename).open("w", encoding="utf-8") as f:
            json.dump(
                {
                    "request_id": request_id,
                    "document_id": document_id,
                    "response": generated_text,
                    "elapsed_time": elapsed_time,
                },
                f,
                indent=4,
                ensure_ascii=False,
            )

        return elapsed_time, generated_text, request_id


async def simulate_requests(
    df_documents: pl.DataFrame,
    model_name: str,
    base_url: str,
    api_key: str,
    max_concurrent_requests: int,
    use_stream: bool,
    system_content: str,
    extra_body: dict | None,
    results_dir: Path,
):
    """
    Gestisce l'esecuzione di richieste multiple in parallelo, utilizzando un semaforo
    per limitare il numero massimo di richieste attive contemporaneamente.
    """

    # Inizializza il client personalizzato
    client = AsyncOpenAI(base_url=base_url, api_key=api_key, max_retries=1)

    # Numero di richieste da simulare (tutte le righe del DataFrame)
    n_requests = len(df_documents)
    logger.info(
        "Starting %s requests (max_concurrent_requests=%s)",
        n_requests,
        max_concurrent_requests,
    )

    start_time = time.perf_counter()

    # Semaforo per limitare la concorrenza
    semaphore = asyncio.Semaphore(max_concurrent_requests)
    tasks = []
    chat_history = []

    for j in range(n_requests):
        document_text = df_documents["content"][j]
        if type(document_text) != str and len(document_text) > 1:
            chat_history = document_text[:-1].to_list()
            document_text = document_text[-1]["content"]
        document_id = df_documents["full_id"][j]

        task = make_generate_request(
            semaphore=semaphore,
            client=client,
            request_id=j,
            chat_history=chat_history,
            document_text=document_text,
            document_id=document_id,
            model_name=model_name,
            system_content=system_content,
            use_stream=use_stream,
            extra_body=extra_body,
            results_dir=results_dir,
        )
        tasks.append(task)

    # Esegui tutte le richieste in parallelo mantenendo il limite di concorrenza
    responses = await asyncio.gather(*tasks)

    end_time = time.perf_counter()
    total_time = end_time - start_time

    # Estrai i tempi di richiesta validi (non float("inf"))
    requests_time = [resp[0] for resp in responses if resp and resp[0] != float("inf")]

    # Calcolo dell'average time
    avg_time = sum(requests_time) / len(requests_time) if requests_time else 0

    return {
        "n_requests": n_requests,
        "max_concurrent_requests": max_concurrent_requests,
        "total_time": total_time,
        "avg_time_per_batch": avg_time,
        "n_requests_executed": len(requests_time),
        "time_per_request": total_time / n_requests if n_requests else 0,
        "requests_time": requests_time,
    }


def run(config_file: str, repeats: int):
    """
    Funzione principale che legge la configurazione, carica i dati
    e avvia l'esecuzione delle richieste in parallelo.
    """
    logger.info("Loading configuration from: %s", config_file)
    parameters = parse_yaml(config_file)

    # Leggi i parametri relativi al modello (come da primo script)
    with Path(parameters["config_engine_llm"]).open() as f:
        parameters_engine_llm = yaml.safe_load(f)
    model_name = parameters_engine_llm["model_name"]

    # Leggi parametri di concurrency e altre opzioni
    max_concurrent_requests = parameters["max_concurrent_requests"]
    use_stream = parameters["use_stream"]
    text_columns = parameters["text_columns"]
    id_columns = parameters["id_columns"]
    n_rows = parameters["n_rows"]

    # Base URL e chiave API del server da contattare direttamente (senza load balancer)
    base_url = "http://localhost:8081/v1"
    api_key = "token-abc123"

    # Caricamento modulo template (opzionale, se preferisci un template custom)
    template_module_path = parameters["template_module_path"]
    template_module = load_template_module_from_path(template_module_path)

    # Prepara system prompt e schema da template
    system_content = template_module.get_default_system_prompt()

    # Parametri base dal file di config
    document_input_file = Path(parameters["document_input_file"])
    guided_decoding_backend = parameters["guided_decoding_backend"]

    end_path = "results"
    extra_body = None
    if guided_decoding_backend:
        end_path = end_path + f"_struct_{guided_decoding_backend}"
        extra_body = {
            "guided_decoding_backend": guided_decoding_backend,
            "guided_json": template_module.get_schema(),
        }

    # Caricamento DataFrame
    logger.info("Loading documents from: %s", document_input_file)
    if n_rows:
        df_documents = pl.scan_parquet(document_input_file).head(n_rows).collect()
    else:
        df_documents = pl.read_parquet(document_input_file)

    # Estraggo le colonne dal YAML (con fallback a None per sicurezza)
    role_column = parameters.get("role_column", None)
    order_column = parameters.get("order_column", None)

    if len(text_columns) > 1:
        # Mantiene i nomi e converte in stringa JSON
        df_documents = df_documents.with_columns(
            pl.struct(text_columns)  # Crea la struct con le colonne
            .map_elements(lambda s: json.dumps({col: s[col] for col in text_columns}))
            .alias("content")
        )
    else:
        df_documents = df_documents.rename({text_columns[0]: "content"})

    if role_column:
        df_documents = (
            df_documents.sort([*id_columns, order_column])
            .rename({role_column: "role"})
            .group_by(id_columns)
            .agg([pl.struct(["role", "content"]).alias("content")])
        )

    df_documents = df_documents.with_columns(
        pl.concat_str(id_columns, separator="_").alias("full_id")
    )

    # Run asynchronous requests
    for i in range(repeats):
        if parameters["is_benchmark"]:
            timestamp = datetime.now().strftime("%Y-%m-%d_%H-%M-%S")
            results_dir = ".benchmark/" + timestamp + "/" + end_path
        results_dir = Path(parameters["results_dir"]) / results_dir
        results_dir.mkdir(parents=True, exist_ok=True)

        logger.info("Execution %s/%s", i + 1, repeats)
        logger.info("Starting asynchronous request simulation...")
        result = asyncio.run(
            simulate_requests(
                df_documents=df_documents,
                model_name=model_name,
                base_url=base_url,
                api_key=api_key,
                max_concurrent_requests=max_concurrent_requests,
                use_stream=use_stream,
                system_content=system_content,
                extra_body=extra_body,
                results_dir=results_dir,
            )
        )

        logger.info("Simulation finished. Saving summary.")
        logger.info(result)

        # Salva il risultato complessivo in un file JSON
        summary_filename = (
            results_dir.parent / f"summary_struct_{guided_decoding_backend}.json"
        )
        with Path(summary_filename).open("w", encoding="utf-8") as f:
            json.dump(result, f, indent=4, ensure_ascii=False)

        # --- QUI SALVI LE INFO DI SISTEMA
        env_info = get_pretty_env_info()
        env_info_filename = results_dir.parent / "system_env_info.txt"
        with Path(env_info_filename).open("w", encoding="utf-8") as f:
            f.write(env_info)

        # Save config file
        # Copia il file di configurazione originale nella cartella dei risultati
        if parameters["is_benchmark"]:
            filename = results_dir.parent / "config.yml"
            shutil.copy(config_file, filename)
            logger.info("Configuration file saved to: %s", filename)

            filename = results_dir.parent / "config_engine_llm.yml"
            shutil.copy(parameters["config_engine_llm"], filename)
            logger.info("Configuration engine llm file saved to: %s", filename)

        logger.info("Finished execution %s/%s", i + 1, repeats)


def main():
    """
    Parses command line arguments and runs the script.
    """

    ## CV_PARSER
    # python /home/<USER>/workarea/ai_playground/backend/cooper/api/llm/benchmark/document_parser_docker.py \
    # --config /home/<USER>/workarea/ai_playground/backend/cooper/api/llm/benchmark/assets/cv_parser/config.yml \
    # --repeats 3

    ## WORK_EXPERIENCES
    # python /home/<USER>/workarea/ai_playground/backend/cooper/api/llm/benchmark/document_parser_docker.py \
    # --config /home/<USER>/workarea/ai_playground/backend/cooper/api/llm/benchmark/assets/work_experiences/config.yml

    ## NAME_RECOGNITION
    # python /home/<USER>/workarea/ai_playground/backend/cooper/api/llm/benchmark/document_parser_docker.py \
    # --config /home/<USER>/workarea/ai_playground/backend/cooper/api/llm/benchmark/assets/name_recognition/config.yml

    parser = argparse.ArgumentParser(description="Launch direct LLM requests script.")
    parser.add_argument(
        "--config",
        type=str,
        required=True,
        help="Path to the configuration file.",
    )
    parser.add_argument(
        "--repeats",
        type=int,
        default=1,  # Default: esegue una sola volta
        help="Number of times to repeat the experiment.",
    )
    args = parser.parse_args()

    run(config_file=args.config, repeats=args.repeats)


if __name__ == "__main__":
    main()
