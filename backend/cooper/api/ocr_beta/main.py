from __future__ import annotations

import async<PERSON>
import io
from pathlib import Path

import docx  # For .docx handling
import fitz  # PyMuPDF
from markitdown import MarkItDown
from werkzeug.utils import secure_filename  # Ensure filename security


async def extract_text_from_document(filename: str, file_bytes: bytes) -> str:
    """
    Estrae il testo da un file PDF o DOCX (in memoria).
    'filename' è usato solo per logging ed eventuale check estensione.
    'file_bytes' è il contenuto già letto in memoria.

    Ritorna sempre una stringa: il testo estratto, o "" se fallisce.
    """

    text = ""

    filename = secure_filename(filename)  # Get a secure version of the filename
    print("File received: %s", filename)  # Stampa il nome del file

    try:
        # Ensure the file is handled as a binary object
        file_bytes = io.BytesIO(file_bytes)

        if filename.endswith(".pdf"):
            # Read the file into memory
            pdf_document = fitz.open(
                stream=file_bytes, filetype="pdf"
            )  # Open the PDF from a bytes stream
            for page in pdf_document:
                text += page.get_text()
            pdf_document.close()  # Chiudi il documento PDF
            print("Text extracted from PDF successfully.")

        elif filename.endswith(".docx"):
            # Handle .docx extraction using python-docx
            doc = docx.Document(file_bytes)  # Open the .docx file
            for paragraph in doc.paragraphs:
                text += paragraph.text + "\n"  # Add paragraph text with newlines
            print("Text extracted from DOCX successfully.")

        elif filename.endswith(".doc"):
            # Handle .doc files (legacy Word files)
            # You may need an external library like `pypandoc` or `pythoncom` with pywin32 for .doc support
            msg = "DOC file extraction is not yet implemented."
            raise NotImplementedError(msg)

        else:
            msg = "Unsupported file type."
            raise ValueError(msg)

    except Exception as e:
        error_message = f"Errore nell'estrazione del testo: {e}"
        print(error_message)
        text = ""  # Se c'è stato errore, restituisce stringa vuota

    return text

async def extract_text_from_file_path(file_path: str) -> str:
    """
    Estrae il testo da un file specificato dal percorso.
    
    Args:
        file_path (str): Il percorso completo del file
        
    Returns:
        str: Il testo estratto dal documento
    """
    # Ottieni il nome del file dal percorso
    filename = Path(file_path).name
    
    # Leggi il file in memoria
    with open(file_path, "rb") as f:
        file_bytes = f.read()
    
    # Chiama la funzione di estrazione
    return await extract_text_from_document(filename, file_bytes)

def markdownify(file_path: str) -> str:
    md = MarkItDown() # Set to True to enable plugins
    return md.convert(file_path)

# Esempio di utilizzo
if __name__ == "__main__":
    # file_path = "/data/datascience/ai_playground/Test OCR/Esempi_ConvDocumenti/3001003-202411-9999-DM0002-Cedolini mese_redacted.pdf"
    file_path = "/data/datascience/ai_playground/Test OCR/Esempi_ConvDocumenti/5001004-202411-9999-DM0002-Cedolini mese_redacted.pdf"
    # file_path = "/data/datascience/ai_playground/Test OCR/[CV] Umberto Cocca (Italiano) - 15-04-2022.pdf"
    # file_path = "/data/datascience/ai_playground/Test OCR/GIOVANNINI_LUCA_Cv-JUNE_2022.pdf"
    # file_path = "/data/datascience/ai_playground/Test OCR/planning.xlsx"

    # extracted_text = asyncio.run(extract_text_from_file_path(file_path))
    # print(f"Testo estratto ({len(extracted_text)} caratteri):")
    # print(extracted_text)

    result = markdownify(file_path)
    print(result.text_content)