import React, { useState, useRef, useEffect } from "react";
import PropTypes from "prop-types";

const StreamingAudioPlayer = ({
    textChunks,
    audioRef,
    startPlaybackAfterChunks = 2,
    useLocalTTS             // bool: true => usa TTS locale, false => usa streaming
}) => {
    const mediaSource = useRef(null); // MediaSource for the audio stream
    const sourceBufferRef = useRef(null); // Buffer for audio chunks
    const lastProcessedIndex = useRef(0); // Index of the last processed chunk
    const processingQueue = useRef(Promise.resolve());

    // Se preferisci gestire l'onda dentro StreamingAudioPlayer
    const [isSpeaking, setIsSpeaking] = useState(false);

    // Funzione di sintesi vocale locale
    const localSpeak = (text, callback) => {
        if (!window.speechSynthesis) return;
        window.speechSynthesis.cancel();

        const utterance = new SpeechSynthesisUtterance(text);
        utterance.lang = 'it-IT';
        utterance.onstart = () => setIsSpeaking(true);
        utterance.onend = () => {
            setIsSpeaking(false);
            if (callback) callback();
        };
        window.speechSynthesis.speak(utterance);
    };

    const handleSourceOpen = (mediaSourceInstance) => {
        console.log("MediaSource opened");
        sourceBufferRef.current = mediaSourceInstance.addSourceBuffer("audio/mpeg");
    };


    useEffect(() => {
        if (useLocalTTS) return; // Se TTS locale, non setup MediaSource

        // Set up MediaSource when the component is mounted
        const mediaSourceInstance = new MediaSource();
        mediaSource.current = mediaSourceInstance;

        if (!audioRef.current) return; // Avoid errors if audio is not yet mounted

        audioRef.current.src = URL.createObjectURL(mediaSourceInstance);
        audioRef.current.load();

        mediaSourceInstance.addEventListener("sourceopen", () =>
            handleSourceOpen(mediaSourceInstance)
        );

        return () => {
            // Cleanup when the component is unmounted
            if (mediaSourceInstance.readyState === "open") {
                mediaSourceInstance.endOfStream();
            }
            mediaSourceInstance.removeEventListener("sourceopen",
                handleSourceOpen(mediaSourceInstance)
            );
        };
    }, [audioRef, useLocalTTS]);

    // Effetto per processare i textChunks
    useEffect(() => {
        processingQueue.current = processingQueue.current.then(async () => {

            // Skip if another instance is running
            if (textChunks.length === 0) {
                lastProcessedIndex.current = 0;
                return;
            }

            // Use the ref value directly
            let currentIndex = lastProcessedIndex.current;

            while (currentIndex < textChunks.length) {
                const currentText = textChunks[currentIndex];
                console.log(`Processing chunk at index ${currentIndex}: ${currentText}`);

                try {
                    // === Se TTS locale, saltiamo la fetch e usiamo speak() ===
                    if (useLocalTTS) {
                        // Usa local TTS
                        await new Promise((resolve) => {
                            localSpeak(currentText, resolve);
                        });
                    } else {
                        // === Altrimenti, chiamiamo l'API esterna per ottenere lo stream audio ===
                        // Send the text chunk to the backend
                        const response = await fetch("/api/generate-audio", {
                            method: "POST",
                            headers: { "Content-Type": "application/json" },
                            body: JSON.stringify({ text: currentText }),
                        });

                        if (!response.body) {
                            throw new Error(`No response body for chunk: ${currentText}`);
                        }

                        // Legge lo stream di byte
                        const reader = response.body.getReader();
                        while (true) {
                            const { done, value } = await reader.read();
                            if (done) break;

                            console.log(`Chunk ${currentIndex} received, size: ${value.length}`);
                            if (
                                sourceBufferRef.current &&
                                sourceBufferRef.current.updating === false
                            ) {
                                await new Promise((resolve, reject) => {
                                    try {
                                        // Safely add the chunk to the buffer
                                        if (!sourceBufferRef.current.updating) {
                                            sourceBufferRef.current.appendBuffer(value);
                                        } else {
                                            console.error(`SourceBuffer is busy, skipping chunk ${currentIndex}`);
                                        }
                                        sourceBufferRef.current.addEventListener("updateend", () => resolve(), { once: true });
                                        sourceBufferRef.current.addEventListener("error", () => reject("Error appending buffer"), { once: true });
                                    } catch (error) {
                                        reject(error);
                                    }
                                });

                                // attiva l'audio in automatico se abbiamo processato startPlaybackAfterChunks chunk
                                if (audioRef.current && audioRef.current.paused) {
                                    audioRef.current.play();
                                    console.log(`Audio playback started. We are processing chunk number: ${currentIndex}`);
                                }
                            }
                        }
                    }
                } catch (error) {
                    console.error("Error while streaming audio:", error);
                }

                // Move to the next chunk
                currentIndex += 1;
            }

            // Update the ref value to the current index
            lastProcessedIndex.current = currentIndex;
        });
    }, [textChunks, useLocalTTS]);

    return (
        <div style={{ display: 'none' }}>
            {/* 
              Se vuoi vedere l’audio <audio> anche per test,
              rimuovi display: none 
            */}
            <audio ref={audioRef} controls style={{ display: 'none' }}>
                Your browser does not support the audio element.
            </audio>

            {/* Se vuoi mostrare un'animazione TTS locale, puoi farlo qui */}
            {useLocalTTS && isSpeaking && (
                <p style={{ color: "red" }}>Sto parlando con TTS locale...</p>
            )}
        </div>
    );
};

StreamingAudioPlayer.propTypes = {
    textChunks: PropTypes.arrayOf(PropTypes.string).isRequired, // List of text chunks to convert to audio
    startPlaybackAfterChunks: PropTypes.number, // Number of chunks after which to start playback
    useLocalTTS: PropTypes.bool // Aggiunto: decidi local TTS o streaming
};

export default StreamingAudioPlayer;
