.video-interview-container {
    font-family: Arial, sans-serif;
    margin-top: 40px;
}

/* Layout video e audio */
.video-interview {
    display: flex;
    flex-direction: row;
    gap: 10px;
    justify-content: space-around;
    margin-bottom: 20px;
}

.video-stream,
.audio-wave-container {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 10px;
}

/* Stile video candidato */
.candidate-video {
    height: 350px;
    width: 100%;
    background-color: #000;
    border: 2px solid #ccc;
    box-shadow: 0 10px 20px rgba(0, 0, 0, 0.3);
}

/* Bottone per start/end interview */
.start-interview-button,
.end-interview-button {
    margin-top: 10px;
    padding: 10px 20px;
    font-size: 16px;
    background-color: #007bff;
    margin: 20px 0px 20px 0px;
    color: #fff;
    border: none;
    width: 100%;
    cursor: pointer;
}

.start-interview-button:hover,
.end-interview-button:hover {
    background-color: #0056b3;
}

/* <PERSON><PERSON>e Microfono */
.audio-recorder-microphone {
    padding: 10px 20px;
    font-size: 16px;
    background-color: #007bff;
    color: white;
    border: none;
    border-radius: 5px;
    cursor: pointer;
}

.audio-recorder-microphone.mic-icon-recording {
    color: red;
}

/* Sezione conversazione */
.conversation-container {
    margin: 10px 0;
}

.conversation-title {
    gap: 10px;
    display: flex;
    align-items: center;
}

.conversation-title-name {
    margin-top: 10px;
    margin-bottom: 10px;
}

/* Bottone per mostrare/nascondere la conversazione */
.toggle-conversation-button {
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 10px 15px;
    border: none;
    cursor: pointer;
    gap: 5px;
    width: 100%;
    font-size: 18px;
    color: #007bff;
}

.toggle-conversation-button .arrow {
    transition: transform 0.3s ease;
}

.arrow {
    display: inline-block;
    transition: transform 0.3s;
}

/* Contenitore dei messaggi */
.messages-container {
    padding: 10px;
    background-color: #f0f0f0;
    max-height: 200px;
    overflow-y: auto;
}

/* Classe che mostra la conversazione */
.messages-container.visible {
    opacity: 1;
    transform: scaleY(1);
}

/* Stile dei messaggi */
.chat-message {
    display: flex;
    flex-direction: column;
    padding: 5px;
    background-color: #e9ecef;
    margin-bottom: 5px;
}

.chat-message.user {
    align-items: flex-start;
    background-color: #d1e7dd;
}

.chat-message.bot {
    align-items: flex-end;
    background-color: #f8d7da;
}

.chat-timestamp {
    font-size: 12px;
    color: #6c757d;
    margin-bottom: 3px;
}

.chat-sender {
    font-weight: bold;
    margin-right: 5px;
}