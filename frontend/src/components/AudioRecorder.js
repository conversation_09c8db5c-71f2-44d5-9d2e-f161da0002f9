import React, { useState } from 'react';
import { FaMicrophone } from 'react-icons/fa';
import { ImSpinner2 } from 'react-icons/im';
import '../styles/AudioRecorder.css';

const AudioRecorder = ({
    onTranscriptReceived,
    isMicActive,
    setIsMicActive,
    isRecording,
    setIsRecording,
    classStyleFaMicrophone,
}) => {
    const [mediaRecorder, setMediaRecorder] = useState(null);
    const [isProcessing, setIsProcessing] = useState(false);

    const sendAudioToBackend = (audioBlob) => {
        setIsProcessing(true);
        const formData = new FormData();
        formData.append('audio', audioBlob, 'recording.webm');

        fetch('/api/transcribe', {
            method: 'POST',
            body: formData,
        })
            .then(response => response.json())
            .then(data => {
                if (onTranscriptReceived) {
                    onTranscriptReceived(data.transcript);
                }
                setIsProcessing(false);
                setIsMicActive(false);
            })
            .catch(error => {
                console.error('Error:', error);
                setIsProcessing(false);
                setIsMicActive(false);
            });
    };

    const handleMicClick = () => {

        if (isMicActive && !isRecording) {
            // Se un altro microfono è attivo, non fare nulla
            return;
        }

        setIsMicActive(true);

        if (isRecording) {
            // Stop recording
            mediaRecorder.stop();
            setIsRecording(false);
        } else {
            // Start recording
            navigator.mediaDevices.getUserMedia({ audio: true })
                .then(stream => {
                    const recorder = new MediaRecorder(stream);
                    setMediaRecorder(recorder);
                    recorder.start();

                    const chunks = [];
                    recorder.ondataavailable = event => {
                        chunks.push(event.data);
                    };

                    recorder.onstop = () => {
                        const audioBlob = new Blob(chunks, { type: 'audio/webm' });
                        sendAudioToBackend(audioBlob);
                    };

                    setIsRecording(true);
                })
                .catch(err => {
                    console.error('Error accessing microphone:', err);
                });
        }
    };

    return (
        <div className="audio-recorder">
            {isProcessing ? (
                <ImSpinner2 className="spinner-icon" />
            ) : (
                <FaMicrophone
                    className={`${classStyleFaMicrophone} ${isRecording ? 'mic-icon-recording' : ''}`}
                    onClick={handleMicClick}
                    disabled={isMicActive && !isRecording}
                />
            )}
        </div>
    );
};

export default AudioRecorder;