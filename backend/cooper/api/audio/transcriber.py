from __future__ import annotations

import torch
from transformers import pipeline

device = torch.device("cuda:3" if torch.cuda.is_available() else "cpu")

# model = "openai/whisper-tiny" # FOR CPU
model = "openai/whisper-large-v3" # FOR GPU

# Define the parameters based on the device
model_kwargs = {}
if device.type == "cuda":
    model_kwargs["attn_implementation"] = "sdpa" # "flash_attention_2" 
    model_kwargs["cache_dir"] = "/home/<USER>/.transformers_cache"

transcriber = pipeline(
    task="automatic-speech-recognition",
    model=model,
    device=device,
    model_kwargs=model_kwargs,
    torch_dtype=torch.float16,
)

if __name__ == "__main__":
    # cd /home/<USER>/workarea/smart_matching_neural_similarity/.tmp
    # wget https://huggingface.co/datasets/reach-vb/random-audios/resolve/main/sam_altman_lex_podcast_367.flac

    import time

    # Path al file audio
    file_name = "/home/<USER>/workarea/ai_playground/.tmp/sam_altman_lex_podcast_367.flac"

    # Avvia il cronometro
    start_time = time.time()

    # Esegui la trascrizione
    outputs = transcriber(
        file_name,
        chunk_length_s=30,
        batch_size=24,
        return_timestamps=True,
    )

    print(outputs)

    # Calcola il tempo di inferenza
    end_time = time.time()
    print(f"Tempo di inferenza: {end_time - start_time:.2f} secondi")

    # 169.5 secondi