from __future__ import annotations

import logging

import numpy as np
from more_itertools import batched
from numpy.typing import Array<PERSON><PERSON>
from openai import OpenAI
from sklearn.utils.validation import check_array, check_is_fitted

from cooper.api.llm.instructor_llm.guided_extraction import GuidedTextTransformer
from cooper.api.llm.instructor_llm.extract_function import ExtractFunction
from cooper.utils import get_logger

logger = get_logger(__name__, level=logging.DEBUG)


class InstructorExtractor(GuidedTextTransformer):
    """
    InstructorExtractor

    A class that extends the GuidedTextTransformer class and provides functionality for extracting guided work experience from a given curriculum vitae (CV) using a pre-trained model and the OpenAI API.

    Parameters:
    -----------
    model_name_or_path : str
        The name or path of the pre-trained model.

    client : OpenAI
        The OpenAI client used for making API requests.

    extract_fn : Callable, optional
        The function used for extracting work experiences from the CV text. Default is 'extract_function'.

    pbar : bool, optional
        Whether to display a progress bar during the extraction process. Default is False.

    batch_size : int, optional
        The batch size for processing multiple CVs at once. Default is None.

    **model_options : dict, optional
        Additional options for the pre-trained model.

    Attributes:
    -----------
    model_name_or_path : str
        The name or path of the pre-trained model.

    client : OpenAI
        The OpenAI client used for making API requests.

    extract_fn : Callable
        The function used for extracting work experiences from the CV text.

    pbar : bool
        Whether to display a progress bar during the extraction process.

    batch_size : int
        The batch size for processing multiple CVs at once.

    model_options : dict
        Additional options for the pre-trained model.
    """

    def __init__(
        self,
        model_name_or_path: str,
        client: OpenAI,
        extract_fn: ExtractFunction,
        pbar: bool = False,
        batch_size: int | None = None,
        verbose: bool = False,
        **model_options,
    ):
        self.model_name_or_path = model_name_or_path
        self.client = client
        self.extract_fn = extract_fn
        self.pbar = pbar
        self.batch_size = batch_size
        self.verbose = verbose
        self.model_options = model_options

    def fit(self, X, y=None):
        if self.batch_size is None:
            self._batch_size = 1
        elif isinstance(self.batch_size, int) and self.batch_size > 0:
            self._batch_size = self.batch_size
        else:
            msg = "Batch size must be None or positive integer"
            raise ValueError(msg)

        self.lm_ = self.model_name_or_path, self.client

        return self

    def transform(self, X: ArrayLike, y: ArrayLike | None = None) -> np.ndarray:
        """
        Transform the input data by extracting guided work experience from each CV.

        Parameters:
        -----------
        X : ArrayLike
            The input data.

        y : ArrayLike, optional
            The target data.

        Returns:
        --------
        output : np.ndarray
            The extracted guided work experience for each CV.
        """
        check_is_fitted(self)
        if self.pbar:
            try:
                from tqdm.rich import tqdm
            except ImportError:
                msg = "Cannot find tqdm progress bar, please install it 'pip install tqdm'"
                raise ImportError(  # noqa: B904
                    msg
                )

        X = check_array(X, ensure_2d=False, dtype=object)

        self.generated_output_ = []
        data = np.ravel(X).tolist()
        self.raw_outputs_ = []
        data_batch = tuple(batched(data, n=self._batch_size))
        indices = tuple(batched(tuple(range(len(data))), n=self._batch_size))
        pbar = tqdm(data_batch, desc="Instructor extraction") if self.pbar else data

        for idx_batch, data_batch in zip(indices, pbar, strict=False):
            # obtain the json output as a string
            result_batch = self.extract_fn(
                self.lm_,
                data_batch,
                verbose=self.verbose,
            )

            # RETRY IF IT DOESN'T WORK
            if len(result_batch) == 0:
                result_batch = self.extract_fn(
                    self.lm_,
                    data_batch,
                    verbose=self.verbose,
                )

            self.raw_outputs_.append(result_batch)

        return self.raw_outputs_
