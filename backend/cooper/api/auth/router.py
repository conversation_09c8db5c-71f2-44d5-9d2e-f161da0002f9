from __future__ import annotations

from cooper.api.auth import auth, deps, schemas
from cooper.api.database_postgres import models
from fastapi import APIRouter, Depends, HTTPException
from fastapi.security import OAuth2Password<PERSON>equestForm
from sqlalchemy.orm import Session

auth_router = APIRouter()


@auth_router.post(
    "/token",
    response_model=schemas.Token,
)
def login(
    form_data: OAuth2PasswordRequestForm = Depends(),
    db: Session = Depends(deps.get_db),
):
    # Query the user by username
    user = (
        db.query(models.User).filter(models.User.username == form_data.username).first()
    )

    # If user is not found or password is invalid, raise an exception
    if not user or not auth.verify_password(form_data.password, user.hashed_password):
        raise HTTPException(status_code=400, detail="Incorrect username or password")

    # Create a token payload
    token_payload = {
        "user_id": user.id,
        "sub": user.username,
        "role": user.role,
        "country": user.country,
        "brand": user.brand,
    }
    token = auth.create_access_token(token_payload)

    # Return the access token along with the additional user information
    return {
        "access_token": token,
        "token_type": "bearer",
        "user_id": user.id,
        "username": user.username,
        "role": user.role,
        "country": user.country,
        "brand": user.brand,
    }


@auth_router.get("/protected")
def protected_route(current_user=Depends(deps.get_current_user)):
    return {"message": f"Welcome, {current_user.username}!"}
