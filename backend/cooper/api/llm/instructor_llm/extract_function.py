from __future__ import annotations

import logging

from openai import OpenAI
from pydantic import BaseModel

from cooper.utils import get_logger

logger = get_logger(__name__, level=logging.DEBUG)


class ExtractFunction:
    def __init__(
        self,
        model: BaseModel,
        few_shot_examples: str,
        system_prompt: str,
    ):
        self.model = model
        self.few_shot_examples = few_shot_examples
        self.system_prompt = system_prompt

    def __call__(self, lm, text, verbose):
        if isinstance(text, str):
            text = [text]

        model_name, client = lm
        model_name: str = model_name
        client: OpenAI = client

        if verbose:
            logger.setLevel(level=logging.DEBUG)
        try:
            user_content = ""
            if self.few_shot_examples:
                user_content = f"# Example output\n{self.few_shot_examples}\n\n"
            user_content += f"# Input \n\n----\n\n{text}\n\n----"

            messages = [
                {
                    "role": "system",
                    "content": self.system_prompt,
                },
                {
                    "role": "user",
                    "content": user_content,
                },
            ]

            extracted_data = client.chat.completions.create(
                messages=messages,
                model=model_name,
                response_model=self.model,
                response_format={"type": "json_object"},
                timeout=60,
                tool_choice="auto",
            )

            return extracted_data.json()
        except Exception as err:
            logger.warning(err)
            return []
