from __future__ import annotations

import re
from pathlib import Path
from typing import Callable

import numpy as np
import parmap
from jinja2 import Template
from numpy.typing import ArrayLike
from sklearn.base import (
    BaseEstimator,
    OneToOneFeatureMixin,
    TransformerMixin,
)
from sklearn.utils.validation import check_array, check_is_fitted

try:
    import guidance
    from guidance import gen, select
except ImportError:
    raise ModuleNotFoundError(
        "Must install guidance with llama_cpp_python==0.2.25<=0.2.226 to run this estimator"
    )


def parse_lm(text: str, tag="yaml"):
    """
    Reads a string and extracts the section contained within the triple brackets ```json <X> ```
    Parameters
    ----------

    """
    if not isinstance(text, str):
        text = str(text)
    return re.findall(f"```{tag}([\s\S]*?)```", text)[0].strip()


@guidance(stateless=True)
def work_experiences_yaml(lm, name, n_experiences: int = 1):
    prompt = ""
    for _ in range(n_experiences):
        experience = """
                -
                  # the name of the company
                  company_name: "{{company_name}}"
                  # the year of start of this job, null if not found
                  start_year: {{start_year}}
                  # the year of end of this job, null if not found
                  end_year: {{end_year}}
                  # the month of start of this job, null if not found
                  month_start: {{month_start}}
                  # the month of end of this job, NULL if not found
                  month_end: {{month_end}}
                  # very brief job description
                  description: "{{description}}"
                  # geographical location of the job, (for example address and city)
                  location: "{{location}}"
                  # seniority level of the job
                  seniority: "{{seniority}}"
                  # brief description of job title
                  job_title: "{{job_title}}"
        """
        prompt += Template(source=experience, lstrip_blocks=False).render(
            company_name=gen(name="company_name", max_tokens=16, stop='"'),
            start_year=gen(name="start_year", regex=r"((1|2)[0-9][0-9][0-9]|null)"),
            end_year=gen(name="end_year", regex=r"((1|2)[0-9][0-9][0-9]|null)"),
            month_start=gen(
                name="start_month", regex=r"(1|2|3|4|5|6|7|8|9|10|11|12|null)"
            ),
            month_end=gen(name="end_month", regex=r"(1|2|3|4|5|6|7|8|9|10|11|12|null)"),
            description=gen(name="description", stop='"'),
            location=gen(name="location", stop='"'),
            seniority=select(options=["junior", "mid", "senior", "specialist"]),
            job_title=gen(name="job_title", stop='"'),
        )
    return prompt


@guidance(stateless=True)
def soft_skills_yaml(lm, name, n_skills: int = 1):
    prompt = ""
    for _ in range(n_skills):
        skills = """
                - "{{soft_skill}}"
        """
        prompt += Template(source=skills, lstrip_blocks=False).render(
            soft_skill=gen(name="soft_skill", stop='"'),
        )
    return prompt


@guidance(stateless=True)
def hard_skills_yaml(lm, name, n_skills: int = 1):
    prompt = ""
    for _ in range(n_skills):
        skills = """
                - "{{hard_skill}}"
        """
        prompt += Template(source=skills, lstrip_blocks=False).render(
            hard_skill=gen(name="hard_skill", stop='"'),
        )
    return prompt


def generate_work_experiences(lm, cv: str, n_experiences: int | None = None):
    # first estimates how many job experiences
    if n_experiences is None:
        prompt = f"""<s> [INST] A partire da questo CV estrai il numero di esperienze lavorative in ordine temporale dalla più recente alla meno recente.[/INST]
        --- CV ----
        {cv}
        ```json
        {{
            "risposta": {select(name="n_experiences", options=[0,1,2,3,4,5,6,7,8,9,10])}
        }}
        ```
        """
        # run the command
        lm += prompt
        # fetch the answer
        n_experiences = int(lm["n_experiences"])
        if n_experiences == 0:
            return ""

    lm.reset()
    prompt = f"""
    <s> [INST] A partire da questo curriculum estrai tutte le esperienze completando lo schema seguente per ogni singola esperienza.
    Considera le esperienze di lavoro dalla più recente in ordine descrente di anno di inizio.
    Riassumi brevemente le descrizioni dei job. Se non sai cosa scrivere indica N/A o non procedere con la prossima esperienza.
    [/INST]

    --- CURRICULUM ---
    {cv}

    ---- YAML ----
    ```yaml
    # La lista delle esperienze del candidato basate sul curriculum descritto sopra.
    work_experience:
          {work_experiences_yaml(name='work experiences', n_experiences=n_experiences)}
    ```
    """

    return str(parse_lm(lm + prompt, tag="yaml"))


def generate_skills(lm, cv: str, n_skills: int = 5):
    lm.reset()
    prompt = f"""
    <s> [INST] A partire da questo curriculum estrai tutte le soft-skills e hard skills valutate in complessivo sul candidato.
    
    --- CURRICULUM ---
    {cv}

    [/INST]
    # Risposta
    ```yaml
    # soft-skills del candidato in lingua italiana come da tassonomia ESCO.
    soft_skills:
          {soft_skills_yaml(name='soft_skills', n_skills=n_skills)}
          
    # hard-skills del candidato in lingua italiana come da tassonomia ESCO.
    hard_skills:
          {hard_skills_yaml(name='hard_skills', n_skills=n_skills)}
    ```
    """
    return str(parse_lm(lm + prompt, "yaml"))


def select_from_list(lm, cv: str, options: list[str]):
    lm.reset()
    options_str = ", ".join(options)
    prompt = f"""
### Instructions:
You are assisting the recruiting service team.
Your task is to extract the following information from the customer's input:
- The job profile
You must format your response in valid YAML. If any information is missing, write "N/A" instead.

### Input:
{cv}

--------

This is the list of options to choose:
{options_str}

### Response:
```yaml
job_title: {select(options=options, recurse=False)}
```
"""
    return lm + prompt


class GuidedTextTransformer(OneToOneFeatureMixin, TransformerMixin, BaseEstimator):
    """
    GuidedCVExperienceTransformer

    A transformer class that extracts guided work experience from a given curriculum vitae (CV) using a pre-trained model.

    Parameters:
    -----------
    model_path : str or Path
        The path to the pre-trained model file.

    **model_options : dict, optional
        Additional options for the pre-trained model.

    Attributes:
    -----------
    model_path : str or Path
        The path to the pre-trained model file.

    model_options : dict, optional
        Additional options for the pre-trained model.

    Methods:
    --------
    fit(X, y=None, **fit_params)
        Fit the transformer to the input data.

        Parameters:
        -----------
        X : ArrayLike
            The input data.

        y : ArrayLike, optional
            The target data.

        **fit_params : dict, optional
            Additional fitting parameters.

        Returns:
        --------
        self : GuidedCVExperienceTransformer
            The fitted transformer instance.

    transform(X, y=None)
        Transform the input data by extracting guided work experience from each CV.

        Parameters:
        -----------
        X : ArrayLike
            The input data.

        y : ArrayLike, optional
            The target data.

        Returns:
        --------
        output : np.ndarray
            The extracted guided work experience for each CV.
    """

    def __init__(
        self,
        model_path: str | Path,
        extract_fn: Callable,
        pbar: bool = False,
        n_jobs: int | None = None,
        **model_options,
    ):
        self.model_path = model_path
        self.extract_fn = extract_fn
        self.pbar = pbar
        self.n_jobs = n_jobs
        self.model_options = model_options

    def fit(self, X: ArrayLike, y: ArrayLike | None = None, **fit_params):
        """
        Fit the transformer to the input data.
        This call loads the model in memory via LlamaCpp

        Parameters:
        -----------
        X : ArrayLike
            The input data.

        y : ArrayLike, optional
            The target data.

        **fit_params : dict, optional
            Additional fitting parameters.

        Returns:
        --------
        self : GuidedCVExperienceTransformer
            The fitted transformer instance.
        """
        if "echo" not in self.model_options:
            self.model_options["echo"] = False

        if not Path(self.model_path).exists():
            raise FileNotFoundError(f"Model not found at path {self.model_path}")
        self.lm_ = guidance.models.LlamaCpp(self.model_path, **self.model_options)
        return self

    def transform(self, X: ArrayLike, y: ArrayLike | None = None) -> np.ndarray:
        """
        Transform the input data by extracting guided work experience from each CV.

        Parameters:
        -----------
        X : ArrayLike
            The input data.

        y : ArrayLike, optional
            The target data.

        Returns:
        --------
        output : np.ndarray
            The extracted guided work experience for each CV.
        """
        check_is_fitted(self)
        if self.pbar:
            try:
                from tqdm.auto import tqdm
            except ImportError:
                raise ImportError(
                    "Cannot find tqdm progress bar, please install it 'pip install tqdm'"
                )

        X = check_array(X, ensure_2d=False, dtype=object)
        lm = self.lm_  # for guidance purpose
        generated_output = []
        if self.pbar:
            pbar = tqdm(np.ravel(X), desc="Extracting structured information...")
        else:
            pbar = np.ravel(X)
        if self.n_jobs is None:
            for x in pbar:
                # obtain the json output as a string
                result = str(self.extract_fn(lm, x))
                generated_output.append(result)
        elif isinstance(self.n_jobs, int):
            result = parmap.map(
                lambda x: str(generate_work_experiences(lm, x, n_experiences=3)),
                pbar,
                pm_processes=self.n_jobs,
            )

        return np.array(generated_output).reshape(X.shape)


class GuidedCVExperienceTransformer(GuidedTextTransformer):
    def __init__(
        self,
        model_path: str | Path,
        extract_fn: Callable = generate_work_experiences,
        pbar: bool = False,
        n_jobs: int | None = None,
        **model_options,
    ):
        super().__init__(model_path, extract_fn, pbar, n_jobs, **model_options)


class GuidedSkillsTransformer(GuidedTextTransformer):
    def __init__(
        self,
        model_path: str | Path,
        extract_fn: Callable = generate_skills,
        pbar: bool = False,
        n_jobs: int | None = None,
        **model_options,
    ):
        super().__init__(model_path, extract_fn, pbar, n_jobs, **model_options)
