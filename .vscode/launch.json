{
    "version": "0.2.0",
    "compounds": [
        {
            "name": "Start Frontend & Backend",
            "configurations": [
                "Start Frontend",
                "Start Backend"
            ]
        }
    ],
    "configurations": [
        {
            "name": "Start Frontend",
            "type": "node-terminal",
            "request": "launch",
            "command": "npm --prefix frontend run build"
        },
        {
            "name": "Start Backend",
            "type": "debugpy",
            "request": "launch",
            "program": "backend/cooper/app.py",
            "args": [
                "--mode",
                "production",
                "--port",
                "5003"
            ],
            "console": "integratedTerminal"
        },
        {
            "name": "instructor-extractor",
            "type": "debugpy",
            "request": "launch",
            "stopOnEntry": false,
            "module": "matching.cli",
            "console": "integratedTerminal",
            "justMyCode": true,
            "python": "${command:python.interpreterPath}",
            "args": [
                "instructor_llm",
                "call-api",
                "--config_file",
                "${userHome}/workarea/smart_matching_neural_similarity/notebooks/umberto/assets/configuration/employability_solutions/config_azure_test.yml"
            ]
        },
        {
            "name": "template-docker-cv-parser",
            "type": "debugpy",
            "request": "launch",
            "program": "/home/<USER>/workarea/ai_playground/backend/cooper/api/llm/benchmark/document_parser_docker.py",
            "args": [
                "--config",
                "/home/<USER>/workarea/ai_playground/backend/cooper/api/llm/benchmark/assets/cv_parser/config.yml",
                "--repeats",
                "3",
            ],
            "console": "integratedTerminal"
        },
        {
            "name": "template-cv-parser",
            "type": "debugpy",
            "request": "launch",
            "program": "/home/<USER>/workarea/ai_playground/backend/cooper/api/llm/benchmark/document_parser.py",
            "args": [
                "--config",
                "/home/<USER>/workarea/ai_playground/backend/cooper/api/llm/benchmark/assets/cv_parser/config.yml",
                "--repeats",
                "3",
            ],
            "console": "integratedTerminal"
        },
        {
            "name": "template-docker-work-experiences",
            "type": "debugpy",
            "request": "launch",
            "program": "/home/<USER>/workarea/ai_playground/backend/cooper/api/llm/benchmark/document_parser_docker.py",
            "args": [
                "--config",
                "/home/<USER>/workarea/ai_playground/backend/cooper/api/llm/benchmark/assets/work_experiences/config.yml",
                "--repeats",
                "3",
            ],
            "console": "integratedTerminal"
        },
        {
            "name": "template-work-experiences",
            "type": "debugpy",
            "request": "launch",
            "program": "/home/<USER>/workarea/ai_playground/backend/cooper/api/llm/benchmark/document_parser.py",
            "args": [
                "--config",
                "/home/<USER>/workarea/ai_playground/backend/cooper/api/llm/benchmark/assets/work_experiences/config.yml",
                "--repeats",
                "3",
            ],
            "console": "integratedTerminal"
        },
        {
            "name": "docker-name_recognition",
            "type": "debugpy",
            "request": "launch",
            "program": "/home/<USER>/workarea/ai_playground/backend/cooper/api/llm/benchmark/document_parser_docker.py",
            "args": [
                "--config",
                "/home/<USER>/workarea/ai_playground/backend/cooper/api/llm/benchmark/assets/name_recognition/config.yml",
                "--repeats",
                "3",
            ],
            "console": "integratedTerminal"
        },
        {
            "name": "name_recognition",
            "type": "debugpy",
            "request": "launch",
            "program": "/home/<USER>/workarea/ai_playground/backend/cooper/api/llm/benchmark/document_parser.py",
            "args": [
                "--config",
                "/home/<USER>/workarea/ai_playground/backend/cooper/api/llm/benchmark/assets/name_recognition/config.yml",
                "--repeats",
                "3",
            ],
            "console": "integratedTerminal"
        },
    ]
}