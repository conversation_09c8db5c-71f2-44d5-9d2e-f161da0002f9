llm_options:
    client: azure
    model_name_or_path: ${AZURE_OPENAI_DISTRIBUTION}
    base_url: ${AZURE_OPENAI_ENDPOINT}
    api_key: ${AZURE_OPENAI_API_KEY}
    api_version: ${AZURE_OPENAI_API_VERSION}

input_path: "/data/datascience/cv_extract_info/datasets/cv_sample.parquet"
output_path: "/home/<USER>/workarea/ai_playground/.tmp/cv_parsed_with_gpt4"
extract_fn: "cv_parser"
column_name: "CV_ANONYMIZED"
n_rows: 5
max_cv_length: 10_000
n_jobs: 5