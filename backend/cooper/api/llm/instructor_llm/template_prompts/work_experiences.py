from __future__ import annotations

from cooper.api.llm.instructor_llm.extract_function import ExtractFunction
from pydantic import BaseModel, Field, conlist


class ExtractFunctionCV(ExtractFunction):
    def __init__(self):
        super().__init__(
            model=WorkExperiences,
            few_shot_examples=example_work_experience,
            system_prompt=work_experience_system_prompt,
        )


class WorkExperience(BaseModel):
    company_name: str | None = Field(
        title="company_name",
        description="The name of the company",
        max_length=64,
        default=None,
    )

    job_title: str | None = Field(
        title="job_title",
        description="Based on the skills and experiences of this experience, please suggest a suitable job title ESCO.",
        max_length=64,
        default=None,
    )

    job_description: str | None = Field(
        title="job_description",
        description="Very coincise summarization of the main goals of the job, provide clarity on what is expected in terms of performance and behavior in that role.",
        default=None,
    )

    seniority: str | None = Field(
        title="seniority",
        description="The seniority level of the candidate for the job, the rank or level of experience and authority that an individual holds within a particular job or organization.",
        examples=[
            "internship",
            "junior",
            "middle",
            "senior",
            "manager",
        ],
    )

    industry: str | None = Field(
        title="industry",
        description="Industry sector, aka the broader sector or field in which a company operates.",
        examples=[
            "technology",
            "manifacturing",
            "horeca",
            "food and beverage",
            "telecommunications",
            "retail",
        ],
    )

    soft_skills: list[str] = Field(
        default_factory=list,  # Default to an empty list
        title="soft_skills",
        alias="competenze trasversali",
        description="Extract the ESCO soft skills list related to the work or related to the job_description. Soft skills are interpersonal, communication, and personal attributes that enable individuals to effectively interact with others in the workplace. Infer them if not written in the curriculum vitae",
        examples=[
            "coordinamento del personale",
            "comunicazione efficace",
            "gestione dello stress",
            "autonomia organizzativa",
            "rispetto delle scadenze",
        ],
    )

    hard_skills: list[str] = Field(
        default_factory=list,  # Default to an empty list
        title="hard_skills",
        alias="competenze tecniche",
        description="Extract the ESCO hard skills list related to the work or related to the job_description. Occupations list, they are specific, teachable abilities or knowledge that are typically quantifiable and easily measurable. Infer them if not written in the curriculum vitae",
        examples=[
            "gestione del riciclo RAEE",
            "capacità di supervisione del lavoro",
            "conoscenza dei processi di produzione",
            "elaborazione dati fiscali",
            "uso del software specifici",
        ],
    )

    location: str | None = Field(
        title="geographical location of the job",
        description="The geographical location (city and address) of the job, None if not available",
        default=None,
    )

    start_year: int = Field(
        title="year of job start",
        description="In what year did this job started (0 if not found)",
        default=0,
    )

    end_year: int = Field(
        title="year of job end",
        description="In what year did this job ended (0 if not found, or -1 if still current job)",
        default=0,
    )

    start_month: int = Field(
        title="month when the job started",
        description="The starting month of this job as integer, 0 if not found",
        default=0,
    )

    end_month: int = Field(
        title="month when the job ended",
        description="The end month of this job as integer, 0 if not found, or -1 if still current job",
        default=0,
    )


class WorkExperiences(BaseModel):
    work_experiences: conlist(WorkExperience, max_length=5)


work_experience_system_prompt = """
You are a helpful assistant tasked with extracting work experiences from candidate resumes. 
Your responses should be formatted in JSON according to the schema provided below. 
Carefully extract all relevant work experiences, listing each as a separate entry. 
Include details such as company name, job title, job description, location (city and address), as well as start and end dates. 
Generate related soft_skills and hard_skills from ESCO but in italian language. 
Understand the seniority_level of the candidate and the industry sector of reference.\n
Ensure that your response adheres to the JSON validation rules. \n!!!RISPONDI SEMPRE IN LINGUA ITALIANA!!!.
"""


example_work_experience = """
```json
[
  {
    "company_name": "Accord Phoenix",
    "job_title": "Capo Turno",
    "job_description": "Gestione della linea, coordinamento del personale operativo del turno",
    "seniority": "middle level",
    "industry": "waste management",
    "soft_skills": [
      "coordinamento del personale",
      "comunicazione efficace",
      "gestione dello stress",
      "autonomia organizzativa",
      "rispetto delle scadenze"
    ],
    "hard_skills": [
      "gestione del riciclo RAEE",
      "capacità di supervisione del lavoro",
      "conoscenza dei processi di produzione"
    ],
    "location": "ss17 Localita Boschetto 67100 L'Aquila",
    "start_year": 2018,
    "end_year": -1,
    "start_month": 3,
    "end_month": -1
  },
  {
    "company_name": "Dolci e Gelati S.r.l.",
    "job_title": "Banconiere di Bar",
    "job_description": null,
    "seniority": "entry level",
    "industry": "food and beverage",
    "soft_skills": [
      "servizio clienti",
      "comunicazione interpersonale"
    ],
    "hard_skills": [
      "competenze in pasticceria",
      "servizio al bancone",
      "preparazione caffè e bevande"
    ],
    "location": "Piazza della repubblica 17/18 Magliano de' Marsi 67062 L'Aquila",
    "start_year": 2017,
    "end_year": 2017,
    "start_month": 7,
    "end_month": 9
  },
  {
    "company_name": "CAF patronato UCI",
    "job_title": null,
    "job_description": "Inserimento dati per calcolo IMU,TASI,730,UNICO,ISEE etc.",
    "seniority": "senior",
    "industry": "taxation and accounting",
    "soft_skills": [
      "precisione",
      "rispetto delle scadenze",
      "autonomia nel lavoro"
    ],
    "hard_skills": [
      "elaborazione dati fiscali",
      "uso del software specifici",
      "conoscenza della normativa fiscale"
    ],
    "location": "Via Monte Cervaro, Avezzano",
    "start_year": 2014,
    "end_year": 2015,
    "start_month": 6,
    "end_month": 1
  },
  {
    "company_name": "Micron Technology",
    "job_title": "Operatore di produzione",
    "seniority": "junior",
    "industry": "semiconductors manufacturing",
    "job_description": null,
    "soft_skills": [
      "lavoro di squadra",
      "attendibilità",
      "puntualità"
    ],
    "hard_skills": [
      "produzione sensori di immagini",
      "uso di macchinari industriali",
      "manutenzione e controllo qualità"
    ],
    "location": "Via A. Paciotti 7 67051 Avezzano AQ",
    "start_year": 2012,
    "end_year": 2012,
    "start_month": 1,
    "end_month": 4
  },
  {
    "company_name": "F.I.D.A. s.p.a.",
    "job_title": "Operaio metalmeccanico",
    "job_description": "Addetto area tecnica, operatore PLC, montaggio, assistenza tecnica post-vendita, aiuto magazziniere",
    "seniority": "entry level",
    "industry": "manufacturing automation",
    "soft_skills": [
      "flessibilità",
      "capacità di adattamento",
      "versatilità"
    ],
    "hard_skills": [
      "prove di sicurezza elettriche",
      "test di collaudo",
      "montaggio",
      "assistenza tecnica",
      "operatore PLC"
    ],
    "location": "ss17 Localita Boschetto 67100 L'Aquila",
    "start_year": 2005,
    "end_year": 0,
    "start_month": 6,
    "end_month": 0
  }
]
```
"""
