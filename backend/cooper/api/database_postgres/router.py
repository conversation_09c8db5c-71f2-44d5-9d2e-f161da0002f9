from __future__ import annotations

from cooper.api.auth.deps import get_current_user

# o importa da dove hai definito i modelli
from cooper.api.database_postgres.vacancy import (
    SessionLocal,
    Vacancy,
)
from fastapi import APIRouter, Depends, HTTPException
from sqlalchemy.orm import Session

db_router = APIRouter()


@db_router.get("/user_vacancy")
def get_user_vacancy(
    offset: int = 0,
    current_user=Depends(get_current_user),
    db: Session = Depends(lambda: SessionLocal()),
):
    """
    Returns the vacancy for the current user based on the offset.

    This function retrieves vacancies directly from the Vacancy table by filtering where:
      - Vacancy.database matches the user's country (normalized to uppercase), and
      - Vacancy.brand matches the user's brand (normalized to capitalized form).

    The offset represents the index of the vacancy in the list of matched vacancies.

    Args:
        offset (int): The index of the vacancy to retrieve.
        current_user: The current logged-in user (with attributes: country and brand).
        db (Session): Database session.

    Returns:
        dict: Contains vacancy details, the current offset, and the total number of matching vacancies.

    Raises:
        HTTPException: If no matching vacancies are found or if the offset is out of range.
    """
    # Ensure that the user has defined country and brand
    if not current_user.country or not current_user.brand:
        raise HTTPException(status_code=400, detail="User country or brand not defined")

    # Normalize the user's country and brand to match the Vacancy records.
    # For example, if Vacancy.database is stored as uppercase and Vacancy.brand as capitalized:
    user_country = current_user.country.upper()  # e.g. "portugal" -> "PORTUGAL"
    user_brand = current_user.brand

    # Query the Vacancy table for matching records
    vacancies = (
        db.query(Vacancy)
        .filter(
            Vacancy.database == user_country,
            Vacancy.brand == user_brand,
        )
        .order_by(Vacancy.id)
        .all()
    )

    total = len(vacancies)
    if total == 0:
        raise HTTPException(status_code=404, detail="No vacancy found for this user")
    if offset < 0 or offset >= total:
        raise HTTPException(status_code=400, detail="Offset out of range")

    vacancy = vacancies[offset]
    if vacancy is None:
        raise HTTPException(status_code=404, detail="Vacancy not found")

    return {
        "vacancy": {
            "id": vacancy.id,
            "database": vacancy.database,
            "brand": vacancy.brand,
        },
        "offset": offset,
        "total": total,
    }
