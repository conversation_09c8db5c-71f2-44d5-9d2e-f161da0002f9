import React, { useState, useEffect } from 'react';
import '../styles/EngineSelector.css';

const EngineSelector = ({ selectedEngine, onEngineSelect }) => {
    const [selectedOption, setSelectedOption] = useState('ollama');
    const [isOpen, setIsOpen] = useState(false);

    const engineOptions = [
        { value: 'default', label: 'Engine', description: 'Choose a engine to enhance the experience.' }, // Prima opzione non cliccabile
        { value: 'vllm', label: 'VLLM', description: 'Deployment. Optimized for high performance.' },
        { value: 'azure_openai', label: 'Azure-OpenAI', description: 'Deployment. Use API Azure-OpenAI' },
        // { value: 'llama_cpp', label: 'Llama.cpp', description: 'Run Locally. Ideal for test activities.' },
        { value: 'ollama', label: 'OLLAMA', description: 'Run Locally. Ideal for test activities.' },
    ];

    useEffect(() => {
        setSelectedOption(selectedEngine);
    }, [selectedEngine]);

    const handleSelect = (value) => {
        if (value !== 'default') { // Impedisce la selezione della prima opzione
            setSelectedOption(value);
            onEngineSelect(value);
            setIsOpen(false);
        }
    };

    return (
        <div className="dropdown-custom">
            <div className="dropdown-list">
                {engineOptions.map((option) => (
                    <div
                        key={option.value}
                        className={`dropdown-item ${option.value === 'default' ? 'disabled' : ''}`}
                        onClick={() => handleSelect(option.value)}
                    >
                        <div className="dropdown-item-label">{option.label}</div>
                        <div className="dropdown-item-description">{option.description}</div>
                    </div>
                ))}
            </div>
        </div>
    );
};

export default EngineSelector;
