[build-system]
requires = ["setuptools", "wheel"]
build-backend = "setuptools.build_meta"

[project]
name = "cooper"
version = "0.1.0"
description = "Backend cooper"
authors = [{ name = "<PERSON><PERSON> Cocca", email = "<EMAIL>" }]
dependencies = [
    "ollama==0.3.3",
    "PyMuPDF==1.24.11",
    "python-docx==1.1.2",
    "python-dotenv==1.0.1",
    "vllm==0.6.5",
    "sglang[all]==0.4.4.post1",
    "openai==1.52.2",
    "elevenlabs==1.12.1",
    "polars==1.15.0",
    "qdrant-client==1.10.1",
    "Werkzeug==3.1.3",
    "fastapi==0.115.6",
    "python-multipart==0.0.19",
    "fastexcel==0.12.0",
    "plotly==5.24.1",
    "nbformat==5.10.4",
    "rich==13.7.1",
    "parmap==1.7.0",
    "guidance==0.1.13",
    "instructor==1.3.3",
    "joblib==1.4.2",
    "more-itertools==10.6.0",
    "scikit-learn==1.6.0",
    "pandas==2.2.2",
    "fastexcel==0.12.0",
    "python-jose[cryptography]==3.4.0",
    "passlib[bcrypt]==1.7.4",
    "sqlalchemy==2.0.39",
    "psycopg2-binary==2.9.10",
    "pydub==0.25.1",
]

[project.optional-dependencies]
dev = ["ruff==0.5.5", "ipywidgets==8.1.3", "ipykernel==6.29.5", "ruff==0.5.5"]

docs = [
    "mkdocs==1.6.1",
    "mkdocs-material==9.6.4",
    "mkdocstrings==0.28.1",
    "mkdocs_include==1.0.0",
    "mkapi==4.0.0",
]
[tool.setuptools]
packages = ["cooper"]
package-dir = { "" = "backend" }

[tool.ruff]
src = ["backend/cooper"]

[tool.ruff.lint]
extend-select = [
    "B",   # flake8-bugbear (best practices e sicurezza)
    "I",   # isort (ordinamento import)
    "ARG", # flake8-unused-arguments (argomenti inutilizzati nelle funzioni)
    "C4",  # flake8-comprehensions (ottimizzazioni con list/set/dict comprehension)
    "EM",  # flake8-errmsg (migliori messaggi di errore)
    "ICN", # flake8-import-conventions (stile delle importazioni)
    "G",   # flake8-logging-format (uso corretto del logging)
    "PGH", # pygrep-hooks (rilevazione pattern problematici nel codice)
    "PIE", # flake8-pie (best practices generali)
    "PL",  # pylint (integrazione con pylint)
    "PT",  # flake8-pytest-style (best practices per Pytest)
    "PTH", # flake8-use-pathlib (sostituzione `os.path` con `pathlib`)
    "RET", # flake8-return (corretta gestione dei `return`)
    "RUF", # Ruff-specific (regole specifiche di Ruff)
    "SIM", # flake8-simplify (codice più conciso e leggibile)
    "T20", # flake8-print (evita `print()` nel codice)
    "UP",  # pyupgrade (aggiornamenti per versioni moderne di Python)
    "YTT", # flake8-2020 (problemi di compatibilità con l'anno 2020)
    "EXE", # flake8-executable (file eseguibili correttamente formattati)
    "NPY", # NumPy specific rules (migliori pratiche per NumPy)
    "PD",  # pandas-vet (migliori pratiche per Pandas)
]

ignore = [
    "PLR09",   # Too many <...>
    "PLR2004", # Magic value used in comparison
    "ISC001",  # Conflicts with formatter
]
isort.required-imports = ["from __future__ import annotations"]
# Uncomment if using a _compat.typing backport
# typing-modules = ["smartmatching._compat.typing"]

[tool.ruff.lint.per-file-ignores]
"tests/**" = ["T20"]
"noxfile.py" = ["T20"]


[tool.pylint]
py-version = "3.10"
ignore-paths = []
reports.output-format = "colorized"
similarities.ignore-imports = "yes"
messages_control.disable = [
    "design",
    "fixme",
    "line-too-long",
    "missing-module-docstring",
    "missing-function-docstring",
    "wrong-import-position",
]
