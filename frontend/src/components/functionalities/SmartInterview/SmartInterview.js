import React, { useState, useRef, useEffect } from 'react';
import { MdGraphicEq } from 'react-icons/md'; // Icona TTS
import AudioRecorder from '../../AudioRecorder';
import AudioWaveVisualizer from './AudioWaveVisualizer';
import ModalPopup from './ModalPopup';
import './SmartInterview.css';

const SmartInterview = ({
    card,
    externalResponse,
    handleSubmit,
    audioRef
}) => {
    const [isInterviewActive, setIsInterviewActive] = useState(false);
    const [messages, setMessages] = useState([]); // Stato per i messaggi
    const [isRecording, setIsRecording] = useState(false); // Stato per la registrazione
    const [isMicActive, setIsMicActive] = useState(false); // Stato del microfono
    const [isLoading, setIsLoading] = useState(false); // Stato per il caricamento

    // === TTS locale: teniamo solo lo stato e l'icona, non più la sintesi ===
    const [textToSpeech, setTextToSpeech] = useState(false); // Controllo TTS
    const [isSpeaking, setIsSpeaking] = useState(false); // Attiva animazione onda

    // Stati per la visualizzazione dei popup
    const [showStartModal, setShowStartModal] = useState(false);
    const [showStopModal, setShowStopModal] = useState(false);

    // Stato per la visualizzazione della conversazione:
    // 0: nessun messaggio, 1: solo l'ultimo messaggio di Jessica, 2: intera conversazione
    // Calcoliamo la rotazione della freccia in base allo stato
    // Ad esempio: 0 => 0°, 1 => 90°, 2 => 180°
    const [conversationState, setConversationState] = useState(1);
    const arrowRotation = conversationState * 90;

    const candidateVideoRef = useRef(null);

    // Aggiungi un messaggio iniziale del recruiter all'inizio dell'intervista
    useEffect(() => {
        if (isInterviewActive && messages.length === 0) {
            const initialMessage = {
                sender: 'bot',
                content: 'Benvenuto! Iniziamo con la prima domanda: Puoi presentarti?',
                // timestamp: new Date().toLocaleTimeString(),
            };
            setMessages([initialMessage]);
        }
    }, [isInterviewActive]);

    /**
     * useEffect che intercetta il flusso in streaming (externalResponse)
     * e aggiorna i messaggi di tutta la conversazione "dinamicamente".
     */
    useEffect(() => {
        if (!externalResponse) return;

        setMessages(externalResponse);
    }, [externalResponse]);

    // // Gestione delle risposte provenienti dall'API
    // useEffect(() => {
    //     if (externalResponse) {
    //         setMessages((prevMessages) => {
    //             const lastMessage = prevMessages[prevMessages.length - 1];

    //             if (lastMessage?.sender === 'bot') {
    //                 // Se l'ultimo messaggio è del bot, lo aggiorniamo
    //                 const updatedLastMessage = { ...lastMessage, text: externalResponse };
    //                 return [...prevMessages.slice(0, -1), updatedLastMessage];
    //             } else {
    //                 // Altrimenti ne creiamo uno nuovo
    //                 const newBotMessage = {
    //                     sender: 'bot',
    //                     text: externalResponse,
    //                     timestamp: new Date().toLocaleTimeString(),
    //                 };
    //                 return [...prevMessages, newBotMessage];
    //             }
    //         });
    //     }
    // }, [externalResponse]);

    // Avvia l'intervista (accende webcam + microfono del candidato)
    const startInterview = () => {
        setIsInterviewActive(true);

        navigator.mediaDevices
            .getUserMedia({ video: true, audio: true })
            .then((stream) => {
                if (candidateVideoRef.current) {
                    candidateVideoRef.current.srcObject = stream;
                }
            })
            .catch((err) => console.error('Errore nell’accesso alla webcam/microfono:', err));
    };

    // Termina l'intervista
    const endInterview = () => {
        setIsInterviewActive(false);
    };

    // Invio dell'input dell'utente al backend
    const submit = async (input) => {
        if (!input) return;
        setIsLoading(true);

        try {
            const api_address = `/${card.title.replace(/[\s-]+/g, '_').toLowerCase()}`;
            await handleSubmit({
                apiEndpoint: api_address,
                input: input,
                selectedOption: null,
                files: [], // Non usato
                isStream: true,
                withChatHistory: true,
                engine: 'vllm', // Motore predefinito
                textToSpeech: textToSpeech, // Passa lo stato TTS
            });
        } catch (error) {
            console.error('Error during API call', error.message);
        } finally {
            setIsLoading(false);
        }
    };

    return (
        <div className="video-interview-container">

            {/* Popup per inizio intervista */}
            <ModalPopup
                visible={showStartModal}
                title="Get Ready!"
                message="The interview is about to start. Are you ready?"
                onConfirm={() => {
                    setShowStartModal(false);
                    startInterview();
                }}
                onCancel={() => setShowStartModal(false)}
            />

            {/* Popup per fine intervista */}
            <ModalPopup
                visible={showStopModal}
                title="Attention!"
                message="Are you sure you want to finish the interview?"
                onConfirm={() => {
                    setShowStopModal(false);
                    endInterview();
                }}
                onCancel={() => setShowStopModal(false)}
            />

            {!isInterviewActive ? (
                <div>
                    <div>
                        <h2>Welcome to Smart Interview!</h2>
                        <p>
                            You are about to start a digital interview for the position of Senior Data Scientist at BESTDATAEVER S.p.A., a leading company in the field of data and artificial intelligence. This interview will be conducted by our advanced bot, designed to simulate a real interview and assess your skills and experience in data science, particularly with Python and PyTorch.
                        </p>
                        <h3>What to Expect:</h3>
                        <ul>
                            <li>The interview will consist of a series of questions that will allow you to demonstrate your knowledge and abilities in the field of data science.</li>
                            <li>Questions may include topics such as machine learning model development, complex data analysis, and the application of technologies like PyTorch.</li>
                            <li>You will have the opportunity to respond to questions as if you were in a traditional interview. Our bot will record and evaluate your responses.</li>
                        </ul>
                        <h3>Tips for the Interview:</h3>
                        <ul>
                            <li>Ensure you have a stable internet connection and a quiet environment to avoid interruptions.</li>
                            <li>Respond to questions clearly and concisely, providing concrete examples from your previous experiences when possible.</li>
                            <li>Do not hesitate to ask for clarification if you are unsure about a question.</li>
                        </ul>
                        <p>
                            Our Goal: We want to understand how your skills and passion for data science can contribute to the success of BESTDATAEVER S.p.A. Our team is looking for a Senior Data Scientist who can not only develop advanced machine learning models but also lead and innovate within our organization.
                        </p>
                        <p>
                            Are You Ready? Click the "Start Interview" button to proceed. We wish you good luck and are eager to discover how your skills align with our needs.
                        </p>
                        <p>
                            Note: Please note that this interview is a simulation and is not a real conversation. The bot's responses are generated based on pre-defined questions and answers. If you have any questions or concerns, please do not hesitate to contact us.
                        </p>
                    </div>
                    <button onClick={() => setShowStartModal(true)} className="start-interview-button">
                        Start Interview
                    </button>
                </div>

            ) : (
                <div>
                    <button onClick={() => setShowStopModal(true)} className="end-interview-button">
                        End Interview
                    </button>

                    <div className="video-interview">
                        <div className="video-stream">
                            <video
                                ref={candidateVideoRef}
                                autoPlay
                                muted
                                className="candidate-video"
                            />
                            <span>Candidate</span>
                            <AudioRecorder
                                onTranscriptReceived={(transcript) => {
                                    submit(transcript)
                                }}
                                isMicActive={isMicActive}
                                setIsMicActive={setIsMicActive}
                                isRecording={isRecording}
                                setIsRecording={setIsRecording}
                                classStyleFaMicrophone="audio-recorder-microphone"
                            />
                        </div>
                        <div className="audio-wave-container">
                            <AudioWaveVisualizer
                                // audioRef={audioRef}
                                isSpeaking={isSpeaking}
                            />
                            <span>Recruiter</span>
                            <MdGraphicEq
                                className={`chatbot-speech-icon ${textToSpeech ? 'active' : ''}`}
                                onClick={() => setTextToSpeech((prev) => !prev)}
                                title={textToSpeech ? 'Disable Text to Speech' : 'Enable Text to Speech'}
                            />
                        </div>
                    </div>
                    <div className="conversation-container">
                        <button
                            className="toggle-conversation-button"
                            onClick={() => {
                                setConversationState((prev) => (prev + 1) % 3)
                            }}
                        >
                            <b className='conversation-title'>
                                <p className='conversation-title-name'>
                                    {conversationState === 0
                                        ? "SHOW PREVIEW CONVERSATION"
                                        : conversationState === 1
                                            ? "SHOW FULL CONVERSATION"
                                            : "HIDE CONVERSATION"}
                                </p>
                                <span
                                    className="arrow"
                                    style={{
                                        transition: "transform 0.3s",
                                        transform: `rotate(${arrowRotation}deg)`
                                    }}
                                >
                                    ▼
                                </span>
                            </b>
                        </button>
                        {conversationState !== 0 && (
                            <div className="messages-container">
                                {conversationState === 1 ? (
                                    // Mostra solo l'ultimo messaggio del bot (Jessica)
                                    (() => {
                                        const reversed = [...messages].reverse();
                                        const lastBotMessage = reversed.find((msg) => msg.sender === 'bot' && msg.type === 'response');
                                        if (!lastBotMessage) return null; // Se non c'è nessun messaggio, non renderizza nulla
                                        return (
                                            <div key={`last-${messages.indexOf(lastBotMessage)}`} className="chat-message bot">
                                                {/* <span className="chat-timestamp">[{lastBotMessage.timestamp}]</span> */}
                                                <span className="chat-sender">Jessica:</span>
                                                <span className="chat-text">{lastBotMessage.content}</span>
                                            </div>
                                        );
                                    })()
                                ) : (
                                    // conversationState === 2: mostra tutta la conversazione
                                    messages.map((msg, index) => {

                                        if (msg.type === 'stats') return null;

                                        return (

                                            <div key={index} className={`chat-message ${msg.sender}`}>
                                                {/* <span className="chat-timestamp">[{msg.timestamp}]</span> */}
                                                <span className="chat-sender">
                                                    {msg.sender === 'bot' ? 'Jessica' : 'Applicant'}:
                                                </span>
                                                <span className="chat-text">{msg.content}</span>
                                            </div>
                                        );
                                    }).filter(Boolean)
                                )}
                            </div>
                        )}
                    </div>
                </div>
            )}
        </div>
    );
};

export default SmartInterview;
