from __future__ import annotations

from cooper.api.database_postgres.database import <PERSON><PERSON>ocal
from cooper.api.database_postgres.models import User
from fastapi import Depends, HTTPException
from fastapi.security import OA<PERSON>2<PERSON><PERSON><PERSON><PERSON>earer
from jose import JWTError, jwt

from .auth import AUTH_ALGORITHM, AUTH_SECRET_KEY

oauth2_scheme = OAuth2PasswordBearer(tokenUrl="token")


def get_db():
    """
    Create and provide a database session for a request.

    This function creates a new session from SessionLocal, yields it for use in dependency injection,
    and ensures that the session is closed after the request is finished.

    Yields:
        db: A SQLAlchemy session object.
    """
    db = SessionLocal()
    try:
        yield db
    finally:
        db.close()


def get_current_user(token: str = Depends(oauth2_scheme), db=Depends(get_db)):
    """
    Retrieve the current user from the provided JWT token and database.

    This function decodes the JWT token using the defined secret key and algorithm, then extracts
    user-related information such as username, user_id, role, country, and brand from the token payload.
    It then queries the database to fetch the corresponding user object. If the token is invalid or
    the user is not found, an HTTPException is raised.

    In addition to updating the user's role (as before), this function now also updates the user's
    country and brand fields if they are provided in the token. This allows you to carry additional
    user information along with the user object.

    Args:
        token (str): JWT token obtained via OAuth2.
        db: Database session injected from the get_db dependency.

    Returns:
        User: The user object retrieved from the database with updated role, country, and brand.

    Raises:
        HTTPException: If the token is invalid (status 401) or the user is not found (status 404).
    """

    try:
        # Decode the JWT token to obtain its payload.
        payload = jwt.decode(token, AUTH_SECRET_KEY, algorithms=[AUTH_ALGORITHM])
        user_id = payload.get("user_id")
        username = payload.get("sub")
        role = payload.get("role")
        country = payload.get("country")
        brand = payload.get("brand")

        # Ensure that the essential fields are present.
        if username is None or user_id is None:
            raise Exception()
    except JWTError:
        # Raise an exception if token decoding fails.
        raise HTTPException(status_code=401, detail="Invalid credentials")

    # Retrieve the user from the database based on the user_id.
    user = db.query(User).filter(User.id == user_id).first()
    if user is None:
        raise HTTPException(status_code=404, detail="User not found")

    # Update the user's attributes based on the token payload.
    # This ensures that the additional information (country and brand) is carried along.
    if username:
        user.username = username
    if role:
        user.role = role
    if country:
        user.country = country
    if brand:
        user.brand = brand

    return user
