
# NOTE
# For Qwen2.5, the chat template in tokenizer_config.json has 
# already included support for the Hermes-style tool use.
# https://docs.vllm.ai/en/v0.8.4_a/features/tool_calling.html#qwen-models

docker run -d \
    --rm \
    --runtime nvidia \
    --shm-size=1g \
    --gpus ${GPU_VLLM} \
    -v "${VLLM_CACHE}:${VLLM_CACHE}" \
    -p "${VLLM_PORT}:${VLLM_PORT}" \
    -e HF_HUB_DOWNLOAD_TIMEOUT=120 \
    --env "HUGGING_FACE_HUB_TOKEN=${HUGGINGFACE_TOKEN}" \
    --name "${CONTAINER_VLLM}" \
    vllm/vllm-openai:v0.9.full \
    --model "${VLLM_DISTRIBUTION}" \
    --port "${VLLM_PORT}" \
    --gpu_memory_utilization 0.9 \
    --enable-prefix-caching \
    --max_model_len 30720 \
    --tensor-parallel-size 2 \
    --api-key token-abc123 \
    --download-dir "${VLLM_CACHE}" \
    --guided-decoding-backend xgrammar \
    --limit_mm_per_prompt 'image=3' \
    --enable-auto-tool-choice \
    --tool-call-parser hermes \
    --trust-remote-code
