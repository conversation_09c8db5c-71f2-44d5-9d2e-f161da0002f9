from __future__ import annotations

import os

from pdf2image import convert_from_path


def pdf_to_images(pdf_path: str, output_folder: str, dpi: int = 300, fmt: str = "png"):
    """
    Converte ogni pagina di pdf_path in un'immagine ad alta risoluzione
    e la salva in output_folder.
    - dpi controlla la risoluzione (es. 300, 600, ...)
    - fmt è il formato di output ('png', 'jpeg', ecc.).
    """
    # 1) Crea la cartella di output se non esiste
    os.makedirs(output_folder, exist_ok=True)

    # 2) Conversione
    pages = convert_from_path(pdf_path, dpi=dpi)
    for i, page in enumerate(pages, start=1):
        filename = os.path.join(output_folder, f"page_{i:03d}.{fmt}")
        # salva l’immagine (page è un PIL.Image.Image)
        page.save(filename, fmt.upper())
        print(f"Salvata: {filename}")

    print(f"✅ Completato: {len(pages)} pagine estratte in '{output_folder}'")


if __name__ == "__main__":
    """Estrai pagine di un PDF in immagini ad alta risoluzione"""

    pdf_to_images(
        pdf_path="/data/datascience/ai_playground/Test OCR/Esempi_ConvDocumenti/3001003-202411-9999-DM0002-Cedolini mese_redacted.pdf",
        output_folder="/data/datascience/ai_playground/Test OCR/Esempi_ConvDocumenti/3001003-202411-9999-DM0002-Cedolini mese_redacted",
    )
