
llm_options:
    client: azure
    model_name_or_path: ${AZURE_OPENAI_DISTRIBUTION}
    base_url: ${AZURE_OPENAI_ENDPOINT}
    api_key: ${AZURE_OPENAI_API_KEY}
    api_version: ${AZURE_OPENAI_API_VERSION}

input_path: "/data/datascience/employability_solutions/dataset/cv_extraction/0_azure_gpt_35_turbo/curriculum_vitae_subset_all_country.parquet"
output_path: "/data/datascience/employability_solutions/dataset/cv_extraction/0_azure_gpt_35_turbo/backup"
extract_fn: "cv"
column_name: "CV_CONTENT_PROCESSED"
n_rows: 100_000
max_cv_length: 10_000
n_jobs: 100