import React, { useState, useEffect, useRef } from 'react';
import ReactMarkdown from 'react-markdown';
import { FaMicrophone, FaArrowRight, FaRobot, FaFolder, FaCopy, FaSun, FaMoon } from 'react-icons/fa';
// import './AskAnswer.css';

// temi code response
import { Prism as SyntaxHighlighter } from 'react-syntax-highlighter';
import { materialLight, materialDark } from 'react-syntax-highlighter/dist/esm/styles/prism';

const AskAnswer = ({ card, externalResponse, handleSubmit, onFileUpload, files, externalInput, onResponse }) => {
    const [internalInput, setInternalInput] = useState('');
    const [selectedOption, setSelectedOption] = useState('');
    const [isLoading, setIsLoading] = useState(false);
    const [copyStatus, setCopyStatus] = useState('Copy code');
    const [isDarkTheme, setIsDarkTheme] = useState(false); // Stato per il tema
    const [fileName, setFileName] = useState('');
    const fileInputRef = useRef(null);

    const submit = async (input, file, isStream = true) => {

        // TODO: o input o file dovrebbero non essere vuoti
        // if (!input.trim()) {
        //     alert('Input cannot be empty.');
        //     return;
        // }

        setIsLoading(true); // Inizia il caricamento della risposta     

        try {

            const api_address = `/${card.title.replace(/[\s-]+/g, '_').toLowerCase()}`;
            console.log(api_address)
            console.log(input)
            console.log(isStream)

            // Utilizza submit per inviare i dati
            await handleSubmit({
                apiEndpoint: api_address,
                input: input,
                selectedOption: null, // Inserisci qui eventuali opzioni se necessario
                file: file,
                isStream: isStream,
            });

        } catch (error) {
            console.error("Error during API call", error.message);
            onResponse("Error during API call", error.message);
        } finally {
            setIsLoading(false);
        }
    };

    useEffect(() => {

        if (externalInput) {
            setInternalInput(externalInput);
        }

        if (files) {
            setFileName(files.name);
        } else {
            setFileName('');
        }

        if (externalInput || files) {
            submit(externalInput, files, true);
        }
    }, [externalInput, files]); // Si attiva ogni volta che externalInput, files cambiano

    const submitButton = (
        <button onClick={(e) => { e.stopPropagation(); submit(internalInput, null, true); }} disabled={isLoading}>
            {isLoading ? 'Waiting...' : <FaArrowRight />}
        </button>
    );

    const copyToClipboard = (text) => {
        navigator.clipboard.writeText(text).then(() => {
            setCopyStatus('Code copied ✔');
            setTimeout(() => setCopyStatus('Copy code'), 2000); // Reset after 2 seconds
        }, (err) => {
            console.error('Errore nel copiare il testo: ', err);
        });
    };

    const toggleTheme = () => {
        setIsDarkTheme((prevTheme) => !prevTheme);
    };
    const syntaxHighlighterTheme = isDarkTheme ? materialDark : materialLight; // Seleziona il tema

    const handleDragOver = (e) => {
        if (!isLoading) {
            e.preventDefault();
            e.stopPropagation();
            e.currentTarget.classList.add('drag-over');
        }
    };

    const handleDragLeave = (e) => {
        if (!isLoading) {
            e.preventDefault();
            e.stopPropagation();
            e.currentTarget.classList.remove('drag-over');
        }
    };

    const handleDrop = (e) => {
        if (!isLoading) {
            e.preventDefault();
            e.stopPropagation();
            e.currentTarget.classList.remove('drag-over');
            onFileUpload(e);
        }
    };

    return (
        <div className="functionality">

            {card.options && card.options.length > 0 && (
                <div className="radio-group">
                    <select value={selectedOption} onChange={(e) => setSelectedOption(e.target.value)}>
                        <option value="">Select an option...</option>
                        {card.options.map(option => (
                            <option key={option.value} value={option.value}>
                                {option.label}
                            </option>
                        ))}
                    </select>
                </div>
            )}

            {card.hasUpload && (
                <div
                    className={`upload-area ${isLoading ? 'disabled' : ''}`}
                    onClick={(e) => {
                        if (!isLoading) { // Allow the click only if loading is not in progress
                            e.stopPropagation()
                            fileInputRef.current.click(); // Simulates click on input
                        }
                    }}
                    onDragOver={handleDragOver}
                    onDragLeave={handleDragLeave}
                    onDrop={handleDrop}
                >
                    <input
                        type="file"
                        id={`file-upload-${card.id}`}
                        accept=".pdf,.doc,.docx"
                        onChange={onFileUpload}
                        style={{ display: 'none' }}
                        ref={fileInputRef} // Assegna il riferimento all'input
                        disabled={isLoading}
                    />
                    <div>
                        <FaFolder /> Upload your document here
                    </div>
                    {fileName && <p>File uploaded: {fileName}</p>}
                    {isLoading && <p className="loading-message">Processing your document, please wait...</p>}
                </div>
            )}

            {/* WITH STREAM 
                rimuoviamo il seguente div di input/mic/submit se dobbiamo caricare
                dei file
            */}
            <div className="input-area" style={{ display: card.hasUpload ? 'none' : 'flex' }}>
                <textarea
                    className="textarea-style"
                    value={internalInput}
                    onChange={(e) => setInternalInput(e.target.value)}
                    placeholder="Enter your input here..."
                    onKeyDown={(e) => {
                        if (e.key === 'Enter' && !isLoading) { // per l'invio da tastiera
                            e.preventDefault();  // Previene il comportamento predefinito del tasto Enter
                            submit(internalInput, null, true);  // submit and start API with streaming=true
                        }
                    }}
                />
                <FaMicrophone className="mic-icon" />
                {submitButton}
            </div>

            {externalResponse && (
                <div className="response-area">
                    <h4><FaRobot /></h4>
                    <ReactMarkdown
                        children={externalResponse}
                        components={{
                            code({ node, inline, className, children, ...props }) {
                                const match = /language-(\w+)/.exec(className || '');
                                const codeString = String(children).replace(/\n$/, '');
                                const language = match ? match[1] : null;

                                return !inline && language ? (
                                    <div style={{ position: 'relative' }}>
                                        <div className='code-language-container'>
                                            <span style={{ padding: '10px 0 0 0' }}>{language.toUpperCase()}</span>
                                            <button className='theme-toggle-button' onClick={toggleTheme}>
                                                {isDarkTheme ? <FaSun /> : <FaMoon />}
                                            </button>
                                            <button
                                                className="code-copy-button"
                                                onClick={() => copyToClipboard(codeString)}
                                            >
                                                <FaCopy style={{ marginRight: '5px' }} />
                                                {copyStatus}
                                            </button>
                                        </div>
                                        <SyntaxHighlighter
                                            style={syntaxHighlighterTheme}
                                            language={language}
                                            PreTag="div"
                                            className="code-block"
                                            showLineNumbers={false}
                                            // lineNumberStyle={{ color: '#888' }}  // Stile numeri di linea
                                            wrapLongLines={true} // To avoid horizontal scrolling in very long lines, you can enable wrapLongLines
                                            customStyle={{
                                                fontSize: '0.8em',      // Riduci la dimensione del font
                                                lineHeight: '1.5',      // Aggiusta l'altezza della linea
                                                padding: '10px 10px 10px 10px',
                                                borderRadius: '5px',
                                            }}
                                            {...props}
                                        >
                                            {codeString}
                                        </SyntaxHighlighter>
                                    </div>
                                ) : (
                                    <code className={className} {...props}>
                                        {children}
                                    </code>
                                );
                            },
                        }}
                    />
                </div>
            )}

        </div>
    );
};

export default AskAnswer;