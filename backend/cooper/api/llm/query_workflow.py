from __future__ import annotations

import asyncio
import json
import os
import re
import time

from cooper.api.llm.engines import LLMEngineFactory
from cooper.scrape.crawl_parallel import crawl_parallel
from cooper.utils import (
    encode_to_base64,
    extract_text_from_document,
    get_base64_content_from_url,
    get_logger,
)
from dotenv import load_dotenv
from openai import AsyncOpenAI
from qdrant_client import QdrantClient

load_dotenv()

logger = get_logger(__name__)

# ENV
MULTIMODAL_RAG_QDRANT_PORT = os.getenv("MULTIMODAL_RAG_QDRANT_PORT")
EMBEDDER_RAG_PORT = os.getenv("EMBEDDER_RAG_PORT")
EMBEDDER_RAG_MODEL = os.getenv("EMBEDDER_RAG_MODEL")
MULTIMODAL_RAG_QDRANT_COLLECTION_DOCUMENTS = os.getenv(
    "MULTIMODAL_RAG_QDRANT_COLLECTION_DOCUMENTS"
)

vllm_embedding_client = AsyncOpenAI(
    base_url=f"http://localhost:{EMBEDDER_RAG_PORT}/v1",
    api_key="not-needed",  # vLLM non verifica l'API key
)
qdrant_client = QdrantClient(
    f"http://localhost:{MULTIMODAL_RAG_QDRANT_PORT}",
    timeout=60.0,
)


# Funzione per chiamare l'LLM
async def call_llm(
    system_content,
    user_content,
    file_buffers: list[tuple[str, str, bytes]] | None = None,
    stream: bool = False,
    engine_name: str = "ollama",
    extra_body: dict | None = None,
    chat_history: list[dict] | None = None,
):
    """
    Esempio di funzione che:
      - carica i file in memoria (o su disco) prima di yield,
      - manda Server-Sent Events (SSE) step-by-step ('thinking' o 'response') mentre elabora i file,
      - poi genera la risposta LLM in streaming.
    """

    engine = LLMEngineFactory.get_engine(engine_name)

    ############# JUST VLLM WORKS
    if engine_name == "vllm" and await check_policy_compliance(
        engine=engine, query=user_content
    ):
        # return "We are unable to process your request as it does not comply with company policies.\n"

        msg = {
            "type": "response",
            "content": "We are unable to process your request as it does not comply with company policies.",
        }
        yield f"data: {json.dumps(msg)}\n\n"
        return
    #############

    text_links, image_links, video_links = handle_links_in_query(query=user_content)
    if text_links:
        message = {
            "type": "retrieving",
            "content": f"Anlyze link content...\n\n{text_links}\n\n",
        }
        yield f"data: {json.dumps(message)}\n\n"
        await asyncio.sleep(0.5)
        # Scrape link contents if are available
        user_content = await analyze_link_content(query=user_content, links=text_links)

    ############# JUST VLLM WORKS
    if engine_name == "vllm" and await analyze_query(engine=engine, query=user_content):
        paraphrased_query = await paraphrase_query(engine=engine, query=user_content)
        user_content = await retrieve_information(paraphrased_query)
    #############

    # Convert images and videos from URLs to Base64
    images_base64 = [
        await get_base64_content_from_url(image_link) for image_link in image_links
    ]
    videos_base64 = [
        await get_base64_content_from_url(video_link) for video_link in video_links
    ]

    # già letto i bytes ora inizio parsing
    if file_buffers:
        msg = {
            "type": "retrieving",
            "content": "Loading files completed. Start parsing...\n\n",
        }
        yield f"data: {json.dumps(msg)}\n\n"
        await asyncio.sleep(0.5)

        user_content, file_images_base64 = await process_files(
            query=user_content, file_buffers=file_buffers
        )
        # Merge images from links and uploaded images
        images_base64.extend(file_images_base64)

    # THIS IS A TEST
    # # Se arriva un'immagine o un video e l'engine non è Azure, usa il modello vision.
    # if (images_base64 or videos_base64) and not isinstance(engine, AzureOpenAIEngine):
    #     engine = LLMEngineFactory.get_engine("vllm_vision")
    #     logger.info("Vision input detected. Overriding model to %s", engine.model)

    # Prendi il generatore e lo forwardi al client
    response_generator = engine.generate_response(
        system_content=system_content,
        user_content=user_content,
        images_base64=images_base64,
        videos_base64=videos_base64,
        stream=stream,
        extra_body=extra_body,
        chat_history=chat_history,
    )

    # Flag per tracciare se siamo dentro una fase di "thinking" (ragionamento)
    is_thinking = False

    # Stats
    start_time = time.time()
    first_token_time = None
    chunk_count = 0

    # ⚠️ QwQ SPECIAL CASE: alcuni modelli (come QwQ-32B) NON iniziano correttamente con <think>
    # ma mandano direttamente la parte ragionamento come se fosse un flusso libero.
    # In questi casi, decidiamo noi di trattare TUTTA la prima parte come "thinking"
    # finché non riceviamo un </think>.
    if "QwQ-32B" in engine.model:
        is_thinking = True

    # Se voglio streammare, inoltro chunk
    async for chunk in response_generator:
        # Set first_token_time only when a non-empty chunk is received.
        # This ensures we measure the time until the first meaningful output,
        # avoiding empty tokens (e.g., whitespace) that don't reflect real response time.
        if not first_token_time and chunk.strip():
            first_token_time = time.time()
        chunk_count += 1

        if "<think>" in chunk:
            is_thinking = True
            chunk = chunk.replace("<think>", "")

        # Se troviamo il tag di chiusura </think>, significa che termina la fase di ragionamento
        if "</think>" in chunk:
            is_thinking = False
            chunk = chunk.replace("</think>", "")

        # Se siamo in modalità thinking, inviamo al client con tipo "thinking"
        if is_thinking:
            yield f"data: {json.dumps({'type': 'thinking', 'content': chunk})}\n\n"
        else:
            yield f"data: {json.dumps({'type': 'response', 'content': chunk})}\n\n"

    # Fine generazione: calcoliamo e inviamo stats
    end_time = time.time()
    total_duration = end_time - start_time
    token_per_sec = chunk_count / total_duration
    time_to_first_token = first_token_time - start_time

    # Note: A 'chunk' is not necessarily the same as a 'token'.
    # Each chunk may contain multiple tokens or even partial tokens, depending on the LLM's streaming implementation.
    # For an exact token count, further processing (e.g., tokenization) might be necessary.
    msg = {
        "type": "stats",
        "content": {
            "total_duration": round(total_duration, 2),
            "token_per_sec": round(token_per_sec),
            "time_to_first_token": round(time_to_first_token, 2),
            "num_chunks": chunk_count,
        },
    }
    yield f"data: {json.dumps(msg)}\n\n"


async def process_files(query: str, file_buffers) -> tuple[str, list[str]]:
    """
    Process a list of uploaded files: extracts text from documents and encodes images in Base64.

    Args:
         user_content
        files (List[UploadFile]): List of uploaded files.

    Returns:
        Tuple[str, List[str]]: A tuple containing:
            - user_content (str): Extracted text from documents.
            - images_base64 (List[str]): List of Base64-encoded images.
    """
    images_base64 = []

    for filename, content_type, file_bytes in file_buffers:
        if content_type in ["image/jpeg", "image/png", "image/gif"]:
            # Convert image to Base64
            image_base64 = encode_to_base64(file_bytes)
            images_base64.append(image_base64)

        elif content_type in [
            "application/pdf",
            "application/msword",
            "application/vnd.openxmlformats-officedocument.wordprocessingml.document",
        ]:
            # Extract text from document
            extracted_text = await extract_text_from_document(filename, file_bytes)
            if extracted_text:
                query += f"\nFILE CONTENT: {extracted_text}"

    return query, images_base64


async def check_policy_compliance(engine, query):
    system_content = (
        "You are a security and compliance expert for Gi Group Holding. "
        "Your responsibility is to analyze user queries and determine if they violate the company's policies, ethical guidelines, or acceptable use standards. "
        "Consider issues such as inappropriate language, data privacy violations, security risks, or attempts to access confidential or restricted information. "
        "Respond with 'yes' only if the query violates company policies; otherwise, respond with 'no'."
    )

    guided_schema = {
        "type": "string",
        "enum": ["yes", "no"],  # Limita le risposte a "yes" o "no"
    }
    extra_body = {
        "guided_decoding_backend": "xgrammar",
        "guided_json": json.dumps(guided_schema),
    }

    response = engine.generate_response(
        system_content=system_content,
        user_content=query,
        stream=False,
        extra_body=extra_body,
    )
    answer = [answer.strip().lower() async for answer in response]
    logger.info("Does the query violate company policies? %s", answer)

    return "yes" in answer[0]


def handle_links_in_query(query):
    """
    Extracts URLs from the query and categorizes them into text, image, and video links.

    Args:
        query (str): The user input query.

    Returns:
        tuple: (text_links, image_links, video_links)
    """
    # Regex pattern for URLs
    url_pattern = re.compile(r'https?://[^\s<>"]+|www\.[^\s<>"]+')
    urls = url_pattern.findall(query)

    # File extensions to categorize media types
    image_extensions = (".jpg", ".jpeg", ".png", ".gif", ".bmp", ".webp", ".svg")
    video_extensions = (".mp4", ".avi", ".mov", ".mkv", ".webm", ".flv")

    text_links, image_links, video_links = [], [], []

    for url in urls:
        url_lower = url.lower()
        if url_lower.endswith(image_extensions):
            image_links.append(url)
        elif url_lower.endswith(video_extensions):
            video_links.append(url)
        else:
            text_links.append(url)

    return text_links, image_links, video_links


async def analyze_link_content(query, links):
    # Qui decidi tu come ottenere il contenuto dei link (scraping, API, altro)

    query += (
        "\n\nThe following external sources have been retrieved from the web. "
        "Use the information from these sources **only if they are relevant and reliable for answering the question**. "
        "If the provided sources **do not sufficiently answer the question**, say that you **do not have enough information**. "
        "Alternatively, **suggest that the user provide more details or clarify their request**, if you believe it would help produce a more accurate response. "
        "Respond **in the same language as the user's query** to ensure clarity and understanding.\n\n"
        "External Sources (web content):\n"
    )

    contents = await crawl_parallel(links, max_concurrent=20)

    if contents:
        logger.info("\nCrawling completed. Retrieved %s pages.", len(contents))
        logger.info("\n=== Sample Contents (first 500 characters of each) ===")

    for link, content in zip(links, contents):
        query += f"Source: {link}\n"
        query += f"Content: {content}" + "\n\n"
        logger.info(content[:500])
        logger.info("...")

    return query


async def analyze_query(engine, query):
    prompt = f"""
        Analyze the following query and determine if it requires retrieval from
        the knowledge base of Gi Group Holding. Answer with 'yes' only if the question
        needs information specific to Gi Group Holding, such as policies, internal processes,
        or company-specific details. Answer 'no' to general or unrelated questions.
        Query: {query}
        """

    guided_schema = {
        "type": "string",
        "enum": ["yes", "no"],  # Limita le risposte a "yes" o "no"
    }
    extra_body = {
        "guided_decoding_backend": "xgrammar",
        "guided_json": json.dumps(guided_schema),
    }

    response = engine.generate_response(
        system_content="You are a system analyzing queries.",
        user_content=prompt,
        stream=False,
        extra_body=extra_body,
    )
    answer = [answer.strip().lower() async for answer in response]
    logger.info("Is RAG necessary?: %s", answer)

    return "yes" in answer[0]


async def paraphrase_query(engine, query):
    system_content = (
        "Reformulate the following user query to make it clearer, more precise, and better suited for retrieving relevant information from a knowledge base. "
        "Ensure that **the meaning is preserved** and that you **maintain the original language** of the query. "
        "This reformulation will be used to improve the accuracy of a retrieval-augmented generation (RAG) system.\n\n"
    )
    response = engine.generate_response(
        system_content=system_content,
        user_content=query,
        stream=False,
    )
    answer = [answer.strip() async for answer in response][0]  # noqa: RUF015
    logger.info("Paraphrase query: %s", answer)

    return answer


async def retrieve_information(query):
    # Generate query embedding
    response = await vllm_embedding_client.embeddings.create(
        model=EMBEDDER_RAG_MODEL,
        input=[query],  # Puoi anche fare batch qui, se vuoi
        encoding_format="float",  # Di solito restituisce float32
    )

    embedding_vllm = response.data[0].embedding

    # Retrieve relevant documents from Qdrant
    results = qdrant_client.search(
        collection_name=MULTIMODAL_RAG_QDRANT_COLLECTION_DOCUMENTS,
        query_vector=embedding_vllm,
        limit=5,
    )

    results = [result.payload for result in results]

    query += (
        "\n\nYou may use the following internal sources to answer the question, but **only if they provide relevant information**. "
        "If the sources are **not sufficient to confidently answer**, respond with one of the following options:\n"
        "- Say that you **do not have enough internal information to provide an accurate answer**.\n"
        "- OR suggest that **the user provides more details or clarifies their request**, if you think additional information could help you provide a better response.\n\n"
        "Respond **in the same language as the user's query** to ensure clarity and understanding.\n"
    )
    for res in results:
        if res["type"] == "text":
            query += "source:" + results[0]["source"] + "\n"
            query += "content:" + json.loads(res["_node_content"])["text"] + "\n\n"

    return query


if __name__ == "__main__":
    import asyncio
    import json

    async def pipeline(user_query: str):
        system_content = (
            "You are a helpful assistant for Gi Group. "
            "Answer user questions using only provided sources and your own knowledge. "
            "If you lack sufficient information, say so."
        )

        # Print the prefix once:
        print("response: ", end="", flush=True)

        # Stream and re‐emit only the 'response' pieces
        async for sse_chunk in call_llm(
            system_content=system_content,
            user_content=user_query,
            file_buffers=None,
            stream=True,
            engine_name="vllm",
            extra_body=None,
            chat_history=None,
        ):
            # each sse_chunk looks like "data: { ... }\n\n"
            if not sse_chunk.startswith("data:"):
                continue
            # remove "data: " and any surrounding whitespace
            try:
                payload = json.loads(sse_chunk.split("data:", 1)[1].strip())
            except json.JSONDecodeError:
                continue

            # only handle actual response (not 'thinking' or 'stats')
            if payload.get("type") == "response":
                text = payload.get("content", "")
                # skip empty segments
                if text:
                    print(text, end="", flush=True)

        # finally newline
        print()

    # 3) Choose your query here:
    # query = "Chi sei tu?"
    # query = "Chi è il capo di gigroup holding?"
    # query = "Chi è il più coglione di tutti?"
    # query = "Quali sono le notizie più interessanti? https://www.nbcnews.com/business"
    query = "Mi riassumi il contenuto? https://it.wikipedia.org/wiki/Gi_Group"

    # 4) Run the pipeline
    asyncio.run(pipeline(query))
