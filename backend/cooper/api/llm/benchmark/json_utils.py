from __future__ import annotations

import json
import re

from cooper.utils import get_logger

# Configurazione del logging
logger = get_logger(__name__)


def clean_json_keys(json_data):
    """<PERSON><PERSON><PERSON><PERSON> le chiavi JSON specifiche."""
    try:
        # Rimuove zeri iniziali da date specifiche
        json_data = re.sub(
            r'("(?:start_year|end_year|start_month|end_month)":)\s*0+([1-9]\d*|-?\d)',
            r"\1 \2",
            json_data,
        )
        # Rimuove virgole spuri prima delle parentesi graffe di chiusura
        return re.sub(r",\s*}", "}", json_data)
    except Exception as e:
        logger.warning("Clean_json_keys error: %s", e)
        return e


def safe_json_loads(row):
    """Effettua il parsing JSON con gestione sicura degli errori."""
    try:
        return json.loads(row)
    except json.JSONDecodeError as e:
        logger.warning("Errore JSONDecode: %s", e)
        # Questo nel caso di generazione max_model_len vllm non sufficiente
        return json.loads(
            row[: row.rfind("}")] + "}]}"
        )  # Tenta di chiudere correttamente il JSON
