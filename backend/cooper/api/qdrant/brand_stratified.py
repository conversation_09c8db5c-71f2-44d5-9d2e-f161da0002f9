from __future__ import annotations

import json

import polars as pl

# Predefined dictionary that maps each database to its brands with the desired maximum rows.
# This dictionary is used to determine how many rows to capture per (DATABASE, brand) combination.
# Note: The build_dict_of_stratification function is used to generate this structure automatically,
#       so you know which brands exist for each database. You can then copy and modify this dictionary
#       by assigning the values you need.
MAX_ROWS_PER_BRAND = {
    "POLAND": {
        "GiPRO": 0,
        "Wyser": 5,
        "Grafton": 20,
        "GiGroup": 10,
    },
    "BRAZIL": {
        "Wyser": 0,
        "Grafton": 0,
        "GiBPO": 0,
        "Gigroup": 0,
    },
    "SPAIN_WYSER": {
        "Wyser": 0,
        "Grafton": 0,
    },
    "PORTUGAL": {
        "Grafton": 0,
        "GiGroup": 0,
        "Wyser": 0,
    },
    "ITALY": {
        "GiGroup": 0,
        "Grafton": 0,
        "Wyser": 0,
    },
    "SPAIN_GIG": {
        "GiGroup": 0,
    },
}


class Strificate:
    def build_dict_of_stratification(self, vacancy_full_text: pl.DataFrame):
        """
        Automatically builds a dictionary representing the stratification structure based on unique
        combinations of 'DATABASE' and 'brand' present in the given DataFrame.

        This function is useful to understand which brands are present for each database.
        You can then copy the printed output and modify the values (e.g., maximum rows) as needed.

        Args:
            vacancy_full_text (pl.DataFrame): The input DataFrame that contains at least the 'DATABASE'
                                              and 'brand' columns.

        Returns:
            None. Prints the generated dictionary.
        """

        # Select only the 'DATABASE' and 'brand' columns, remove duplicate rows and null values.
        combinations = (
            vacancy_full_text.select(["DATABASE", "brand"]).unique().drop_nulls()
        )

        # Build a nested dictionary:
        # Key: each unique database value
        # Value: another dictionary where:
        #        Key: each unique brand corresponding to that database
        #        Value: initial value (set to 0) that you can later update manually.
        combinations = {
            db: {
                brand: 0
                for brand in combinations.filter(pl.col("DATABASE") == db)["brand"]
                .unique()
                .to_list()
            }
            for db in combinations["DATABASE"].unique().to_list()
        }

        # Pretty-print the dictionary using json.dumps
        print("Dictionary using json.dumps:")
        print(json.dumps(combinations, indent=2))

    def stratify(
        self,
        vacancy_full_text: pl.DataFrame,
        save_file: bool = False,
    ) -> pl.DataFrame:
        """
        Stratifies the input DataFrame by filtering the rows based on the (DATABASE, brand) combinations
        defined in the MAX_ROWS_PER_BRAND dictionary. For each combination, it selects the last N rows
        (using tail) where N is the maximum rows specified in the dictionary.

        Optionally, the resulting DataFrame is saved as a parquet file if save_file is True.

        Args:
            vacancy_full_text (pl.DataFrame): The input DataFrame containing vacancy information with
                                              'DATABASE' and 'brand' columns.
            save_file (bool, optional): Flag to determine whether to save the resulting DataFrame to disk.
                                        Defaults to False.

        Returns:
            pl.DataFrame: The concatenated DataFrame of all stratified subsets.
        """

        combined_vacancies = []

        # Iterate over each database and its corresponding brands from the MAX_ROWS_PER_BRAND dictionary.
        for db, brands in MAX_ROWS_PER_BRAND.items():
            for brand, max_rows in brands.items():
                # Filter the DataFrame for rows matching the current (DATABASE, brand) combination.
                filtered = vacancy_full_text.filter(
                    (pl.col("DATABASE") == db) & (pl.col("brand") == brand)
                )

                print(
                    f"\nDATABASE: {db}, BRAND: {brand}, TOTAL: {filtered.shape[0]}, MAX ROWS: {max_rows}"
                )

                # Append the last 'max_rows' rows of the filtered DataFrame to the list.
                combined_vacancies.append(filtered.tail(max_rows))

        # Concatenate all the filtered subsets into a single DataFrame.
        combined_vacancies = pl.concat(combined_vacancies)

        # Optionally, save the combined DataFrame to a parquet file.
        if save_file:
            filename = "/home/<USER>/workarea/ai_playground/backend/cooper/api/qdrant/combined_vacancies.parquet"
            combined_vacancies.write_parquet(filename)

        return combined_vacancies

    def create_unique_index(
        self,
        combined_vacancies: pl.DataFrame,
        columns_to_combine: list,
    ) -> list:
        """
        Creates a unique index by concatenating the specified columns (passed in the list 'columns_to_combine')
        into a new column named 'COMBINED'. Each column is first cast to a string (Utf8) and then joined with
        a dash '-' as a separator. The function returns a list of the combined unique identifiers.

        Args:
            combined_vacancies (pl.DataFrame): The DataFrame on which the unique index column will be created.
            columns_to_combine (list): A list of column names (as strings) that should be concatenated to form the index.

        Returns:
            list: A list containing the unique combined index values.
        """

        # Create the 'COMBINED' column by concatenating the specified columns.
        combined_vacancies = combined_vacancies.with_columns(
            pl.concat_str(
                [pl.col(col).cast(pl.Utf8) for col in columns_to_combine], separator="-"
            ).alias("COMBINED")
        )
        print(combined_vacancies)

        # Extract the 'COMBINED' column values as a list.
        combined_list = combined_vacancies["COMBINED"].to_list()
        print(combined_list)

        return combined_list


def extract_brand(text):
    # Gestione del caso in cui il JSON non sia valido
    try:
        return json.loads(text).get("brand", None)
    except Exception:
        return None


if __name__ == "__main__":
    vacancy_full_text = pl.scan_parquet(
        "/data/datascience/smart_matching/experiments_umberto/v05/vacancy_full_text.parquet"
    ).collect()

    # Aggiungi una nuova colonna con il brand
    vacancy_full_text = vacancy_full_text.with_columns(
        pl.col("TEXT_VACANCY").map_elements(extract_brand).alias("brand")
    )

    s = Strificate()

    s.build_dict_of_stratification(vacancy_full_text)

    combined_vacancies = s.stratify(vacancy_full_text, save_file=True)
    s.create_unique_index(
        combined_vacancies=combined_vacancies,
        columns_to_combine=["DATABASE", "RECRUITMENT_ID"],
    )
