## Deploy without docker (actually works well...)

Funziona molto bene, ma sarebbe da testare quanto è meglio con le ultime versioni di vllm (tenendo in considerazione anche il fatto che xgrammar non è compatibile come dovrebbe con le ultime...)

```bash
# 0. (JUST FIRST TIME) DAI PERMESSI AI FILE .sh
chmod +x /home/<USER>/workarea/ai_playground/backend/cooper/api/llm/benchmark/start_servers.sh
chmod +x /home/<USER>/workarea/ai_playground/backend/cooper/api/llm/benchmark/kill_servers.sh

# 1. VERIFICA IL NOME DEL MODELLO E L'ENGINE NEL CONFIG FILE
# /home/<USER>/workarea/ai_playground/backend/cooper/api/llm/benchmark/config_engine_llm.yml

# 2. AVVIA MULTIPLI SERVER VLLM o SGLANG ED ESEGUI IL LOAD BALANCER
BENCHMARK_PATH="/home/<USER>/workarea/ai_playground/backend/cooper/api/llm/benchmark" && conda activate ai_playground && $BENCHMARK_PATH/start_servers.sh --config_engine_llm $BENCHMARK_PATH/config_engine_llm.yml && cd $BENCHMARK_PATH && uvicorn load_balancer:app --host 0.0.0.0 --port 5000 --workers 4 --timeout-keep-alive 600


# 2. (OPTIONAL) SEGUI I LOG DI OGNI VLLM SERVER
/home/<USER>/workarea/ai_playground/backend/cooper/api/llm/benchmark/.log

######################################
# 3. FAI PARTIRE IL BENCHMARK
######################################
# CV_PARSER
python /home/<USER>/workarea/ai_playground/backend/cooper/api/llm/benchmark/document_parser.py \
--config /home/<USER>/workarea/ai_playground/backend/cooper/api/llm/benchmark/assets/cv_parser/config.yml \
--repeats 3

# EVALUATION_CV_PARSER_QWQ
python /home/<USER>/workarea/ai_playground/backend/cooper/api/llm/benchmark/document_parser.py \
--config /home/<USER>/workarea/ai_playground/backend/cooper/api/llm/benchmark/assets/evaluation_cv_parser_qwq/config.yml \
--repeats 3

# EVALUATION_CV_PARSER_TK
python /home/<USER>/workarea/ai_playground/backend/cooper/api/llm/benchmark/document_parser.py \
--config /home/<USER>/workarea/ai_playground/backend/cooper/api/llm/benchmark/assets/evaluation_cv_parser_tk/config.yml \
--repeats 3

# EVALUATION_CV_WORK_EXPERIENCES_PARSER_GPT35TURBO
python /home/<USER>/workarea/ai_playground/backend/cooper/api/llm/benchmark/document_parser.py \
--config /home/<USER>/workarea/ai_playground/backend/cooper/api/llm/benchmark/assets/evaluation_cv_work_experiences_parser_gpt35turbo/config.yml \
--repeats 3

# EVALUATION_CV_WORK_EXPERIENCES_PARSER_QWQ
python /home/<USER>/workarea/ai_playground/backend/cooper/api/llm/benchmark/document_parser.py \
--config /home/<USER>/workarea/ai_playground/backend/cooper/api/llm/benchmark/assets/evaluation_cv_work_experiences_parser_qwq/config.yml \
--repeats 3

# EVALUATION_CV_WORK_EXPERIENCES_PARSER_TK
python /home/<USER>/workarea/ai_playground/backend/cooper/api/llm/benchmark/document_parser.py \
--config /home/<USER>/workarea/ai_playground/backend/cooper/api/llm/benchmark/assets/evaluation_cv_work_experiences_parser_tk/config.yml \
--repeats 3

# NAME_RECOGNITION
python /home/<USER>/workarea/ai_playground/backend/cooper/api/llm/benchmark/document_parser.py \
--config /home/<USER>/workarea/ai_playground/backend/cooper/api/llm/benchmark/assets/name_recognition/config.yml \
--repeats 3

# NEXT_JOB_RECOMMENDER
python /home/<USER>/workarea/ai_playground/backend/cooper/api/llm/benchmark/document_parser.py \
--config /home/<USER>/workarea/ai_playground/backend/cooper/api/llm/benchmark/assets/next_job_recommender/config.yml \
--repeats 3

# WORK_EXPERIENCES
python /home/<USER>/workarea/ai_playground/backend/cooper/api/llm/benchmark/document_parser.py \
--config /home/<USER>/workarea/ai_playground/backend/cooper/api/llm/benchmark/assets/work_experiences/config.yml \
--repeats 3
######################################

# 4. CHIUDI TUTTI I VLLM SERVER
/home/<USER>/workarea/ai_playground/backend/cooper/api/llm/benchmark/kill_servers.sh
```

## Valutazioni [DEPRECATED] VECCHIO METODO:

Non conviene usare 

      --enable-prefix-caching \

L'utilizzo quando vogliamo un output strutturato è molto basso:  

      INFO 02-07 09:11:22 metrics.py:467] Avg prompt throughput: 2748.3 tokens/s, Avg generation throughput: 2.0 tokens/s, Running: 146 reqs, Swapped: 0 reqs, Pending: 104 reqs, GPU KV cache usage: 88.4%, CPU KV cache usage: 0.0%.
      
      INFO 02-07 09:11:22 metrics.py:483] Prefix cache hit rate: GPU: 1.52%, CPU: 0.00%

## DOCKER - NON FUNZIONA COME DOVREBBE

Sia in termini di velocità, sia molti documenti non vengono processati. Da capire come mai!

### Start backend

In diverse command line:

/home/<USER>/workarea/ai_playground/backend/cooper/api/llm/benchmark/document_parser_docker.py è IL NUOVO MODO PER PROCESSARE I TEMPLATE SENZA USARE IL LOAD BALANCER.PY
ma voglio essere certo che sia più veloce questo metodo rispetto all'altro mio

```bash
docker run -itd \
   --rm \
   --name vllm0 \
   --ipc host \
   --privileged \
   --network vllm_nginx \
   --runtime nvidia \
   --env CUDA_VISIBLE_DEVICES=0 \
   --shm-size=10.24gb \
   -v /data/datascience/.shared/.transformers_cache:/data/datascience/.shared/.transformers_cache \
   -p 8082:8081 \
   vllm/vllm-openai:latest \
   --model "Qwen/Qwen2.5-32B-Instruct-GPTQ-Int4" \
   --port 8081 \
   --disable-log-requests \
   --max_model_len 6144 \
   --gpu_memory_utilization 0.9 \
   --enable-prefix-caching \
   --tensor_parallel_size 1 \
   --download-dir /data/datascience/.shared/.transformers_cache

docker run -itd \
   --rm \
   --name vllm1 \
   --ipc host \
   --privileged \
   --network vllm_nginx \
   --runtime nvidia \
   --env CUDA_VISIBLE_DEVICES=1 \
   --shm-size=10.24gb \
   -v /data/datascience/.shared/.transformers_cache:/data/datascience/.shared/.transformers_cache \
   -p 8083:8081 \
   vllm/vllm-openai:latest \
   --model "Qwen/Qwen2.5-32B-Instruct-GPTQ-Int4" \
   --port 8081 \
   --disable-log-requests \
   --max_model_len 6144 \
   --gpu_memory_utilization 0.9 \
   --enable-prefix-caching \
   --tensor_parallel_size 1 \
   --download-dir /data/datascience/.shared/.transformers_cache

docker run -itd \
   --rm \
   --name vllm2 \
   --ipc host \
   --privileged \
   --network vllm_nginx \
   --runtime nvidia \
   --env CUDA_VISIBLE_DEVICES=2 \
   --shm-size=10.24gb \
   -v /data/datascience/.shared/.transformers_cache:/data/datascience/.shared/.transformers_cache \
   -p 8084:8081 \
   vllm/vllm-openai:latest \
   --model "Qwen/Qwen2.5-32B-Instruct-GPTQ-Int4" \
   --port 8081 \
   --disable-log-requests \
   --max_model_len 6144 \
   --gpu_memory_utilization 0.9 \
   --enable-prefix-caching \
   --tensor_parallel_size 1 \
   --download-dir /data/datascience/.shared/.transformers_cache

docker run -itd \
   --rm \
   --name vllm3 \
   --ipc host \
   --privileged \
   --network vllm_nginx \
   --runtime nvidia \
   --env CUDA_VISIBLE_DEVICES=3 \
   --shm-size=10.24gb \
   -v /data/datascience/.shared/.transformers_cache:/data/datascience/.shared/.transformers_cache \
   -p 8085:8081 \
   vllm/vllm-openai:latest \
   --model "Qwen/Qwen2.5-32B-Instruct-GPTQ-Int4" \
   --port 8081 \
   --disable-log-requests \
   --max_model_len 6144 \
   --gpu_memory_utilization 0.9 \
   --enable-prefix-caching \
   --tensor_parallel_size 1 \
   --download-dir /data/datascience/.shared/.transformers_cache

docker run -itd \
   --rm \
   --network vllm_nginx \
   -p 8081:8080 \
   -v "$HOME/workarea/ai_playground/backend/cooper/nginx_conf/:/etc/nginx/conf.d/" \
   --name nginx-lb \
   nginx-lb:latest

docker stop vllm0 vllm1 vllm2 vllm3 nginx-lb
```

### **📌 Flusso dettagliato delle richieste considerando il retry automatico di `AsyncOpenAI`**
#### **🔄 Flusso completo**
1️⃣ **Il client (`AsyncOpenAI`) invia una richiesta HTTP a Nginx**  
   - Esempio: `POST /v1/chat/completions`  
   - **Nginx riceve la richiesta e la inoltra a uno dei server vLLM** in base alla strategia di load balancing (`least_conn`).

2️⃣ **Nginx inoltra la richiesta a un server vLLM disponibile**
   - Se vLLM è **disponibile e veloce**, restituisce `200 OK` con la risposta.  
   - Se vLLM è **saturo o lento**, potrebbe **impiegare più tempo** del `proxy_read_timeout` di Nginx.

3️⃣ **Nginx attende la risposta da vLLM**  
   - Se la risposta arriva **entro il tempo impostato** (`proxy_read_timeout`), Nginx la invia al client.  
   - Se vLLM **impiega troppo tempo** (es. più di `proxy_read_timeout=60s`), Nginx interrompe la connessione e invia **504 Gateway Timeout** al client.

4️⃣ **Il client riceve la risposta**
   - ✅ Se la risposta è `200 OK` → **Il client termina il processo con successo.**
   - ❌ Se la risposta è `504 Gateway Timeout`, il client **inizia un retry automatico**.

---

#### **🔁 Comportamento del retry automatico di `AsyncOpenAI`**
Se il client riceve **un errore 500, 502, 503 o 504**, segue queste regole di default:

- **Tentativo iniziale fallito → Primo retry dopo pochi millisecondi.**
- **Secondo retry con backoff esponenziale → Attesa maggiore tra i retry.**
- **Numero massimo di retry: 2** (quindi in totale **3 tentativi**: richiesta originale + 2 retry).

---

#### **🔄 Flusso con retry automatico**
Se **vLLM impiega troppo tempo o è momentaneamente offline**, il client riprova automaticamente la richiesta:

1️⃣ **Prima richiesta** (tentativo originale)  
   - `AsyncOpenAI` invia la richiesta a Nginx  
   - Se tutto va bene: `200 OK` ✅  
   - Se Nginx risponde con `504 Gateway Timeout`, il client **inizia il primo retry** dopo un breve delay.

2️⃣ **Primo retry automatico (`Retry #1`)**  
   - Il client attende **qualche millisecondo o secondo** (backoff esponenziale).  
   - Reinvia la richiesta a Nginx.  
   - Se la richiesta va a buon fine: `200 OK` ✅  
   - Se fallisce di nuovo (`504 Gateway Timeout` o `500`), il client **aspetta più a lungo e riprova.**

3️⃣ **Secondo retry automatico (`Retry #2`)**  
   - Il client attende **più a lungo rispetto al primo retry**.  
   - Se la richiesta ha successo: `200 OK` ✅  
   - Se fallisce di nuovo (`504`), **il client non prova più** e segnala un errore definitivo.

---

#### **📊 Esempio di log reale con retry**

```
2025-01-29 11:28:50,892 - INFO - HTTP Request: POST http://localhost:8000/v1/chat/completions "HTTP/1.1 200 OK"
2025-01-29 11:28:51,261 - INFO - HTTP Request: POST http://localhost:8000/v1/chat/completions "HTTP/1.1 200 OK"
2025-01-29 11:28:52,347 - INFO - HTTP Request: POST http://localhost:8000/v1/chat/completions "HTTP/1.1 504 Gateway Time-out"
2025-01-29 11:28:52,347 - INFO - HTTP Request: POST http://localhost:8000/v1/chat/completions "HTTP/1.1 504 Gateway Time-out"
2025-01-29 11:28:52,348 - INFO - Retrying request to /chat/completions in 0.412259 seconds
2025-01-29 11:28:52,348 - INFO - Retrying request to /chat/completions in 0.386886 seconds
```
- La prima richiesta ha successo (`200 OK`).
- Alcune richieste ricevono `504 Gateway Timeout` perché vLLM ha impiegato troppo tempo a rispondere.
- `AsyncOpenAI` effettua **retry automatici** con attesa tra i tentativi.

### **📌 Come funziona il blocco del backend con `max_fails` e `fail_timeout`?**
Quando configuri un **upstream backend** come questo:

```nginx
upstream backend {
    least_conn;
    server vllm0:8000 max_fails=3 fail_timeout=10s;
    server vllm1:8000 max_fails=3 fail_timeout=10s;
    server vllm2:8000 max_fails=3 fail_timeout=10s;
    server vllm3:8000 max_fails=3 fail_timeout=10s;
}
```
🔹 **`max_fails=3`** → Se un server (es. `vllm0`) fallisce **3 volte di seguito**, Nginx lo considera "non disponibile".  
🔹 **`fail_timeout=10s`** → Nginx **smetterà di inviare richieste a quel server per 10 secondi**.  
🔹 Dopo 10 secondi, **Nginx riproverà a usare quel server**.

---

#### **📊 Esempio pratico**
Immaginiamo che **vllm2** sia sovraccarico e inizi a restituire **504 Gateway Timeout**.

1️⃣ Il client invia una richiesta → Nginx inoltra a `vllm2`.  
   - ❌ `vllm2` risponde con **504 Gateway Timeout** → 1° fallimento.  
2️⃣ Il client (o retry automatico) invia una nuova richiesta → Nginx la manda di nuovo a `vllm2`.  
   - ❌ `vllm2` risponde con **504 Gateway Timeout** → 2° fallimento.  
3️⃣ Terza richiesta → Nginx prova di nuovo con `vllm2`.  
   - ❌ `vllm2` fallisce per la **terza volta** → Nginx **lo esclude per 10s**.  
4️⃣ Per i successivi 10 secondi, **Nginx NON invia più richieste a `vllm2`**, distribuendo il carico su `vllm0`, `vllm1` e `vllm3`.  
5️⃣ Dopo 10 secondi, Nginx **riprova a usare `vllm2`**.  
   - ✅ Se `vllm2` risponde bene, Nginx riprende a usarlo normalmente.  
   - ❌ Se `vllm2` continua a fallire, il ciclo si ripete.

---

#### **📌 Cosa succede se tutti i server falliscono?**
Se **tutti** i server `vllm0`, `vllm1`, `vllm2` e `vllm3` raggiungono `max_fails=3`, **Nginx proverà comunque a inviare richieste** a uno di loro perché **non ha alternative**.

**Soluzione per evitare questo scenario?**  
Puoi configurare un **server di fallback** o aumentare il tempo di `fail_timeout`.

Esempio di fallback:
```nginx
upstream backend {
    least_conn;
    server vllm0:8000 max_fails=3 fail_timeout=10s;
    server vllm1:8000 max_fails=3 fail_timeout=10s;
    server vllm2:8000 max_fails=3 fail_timeout=10s;
    server vllm3:8000 max_fails=3 fail_timeout=10s;
    server backup_server:8000 backup;  # Questo server viene usato solo se tutti gli altri falliscono
}
```
🔹 `backup` → Questo server **viene usato solo se tutti gli altri sono falliti**.

---

#### **📌 Quando aumentare `fail_timeout`?**
Se noti che un server fallisce **troppo spesso** e continua a essere reinserito nel bilanciamento mentre è ancora in difficoltà, **puoi aumentare `fail_timeout`** per dargli più tempo di recupero:

```nginx
server vllm2:8000 max_fails=3 fail_timeout=60s;  # Ora vllm2 viene escluso per 1 minuto dopo 3 fallimenti
```
✔️ **Ora `vllm2` starà fuori per 60 secondi prima che Nginx lo riutilizzi.**