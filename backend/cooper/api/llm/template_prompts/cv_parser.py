from __future__ import annotations

from typing import List, Optional

from pydantic import BaseModel, Field


def get_default_system_prompt():
    return """
    You are a language model specialized in extracting structured information from resumes (CVs). Your task is to read unstructured text documents (CVs) and provide data accurately mapped to a predefined schema. Follow the schema strictly, ensuring all fields and their relationships are respected.

    The schema is divided into several sections, each with a specific set of fields describing personal information, education, work experience, skills, and other candidate attributes.

    ### **General Guidelines**
    1. Analyze the text thoroughly, extracting every relevant detail.
    2. Focus on the semantic meaning of the content, even when terminology varies or is incomplete.
    3. If a field is missing or ambiguous in the text, return `null` for that field without making arbitrary assumptions.
    4. Preserve temporal formats and standard codes such as ISO 8601 for dates and language identifiers (e.g., "en", "it").
    5. Where applicable, include explanatory descriptions for extracted codes (e.g., proficiencyCodeDescription).

    ### **Structure to Follow**
    Below is a detailed description of the fields and their use:

    #### **1. Personal**
    - Extract personal details such as first name, last name, date of birth, gender, address (including details like city, country, postal code, street name, and house number), email, phone numbers, nationality, website, and social media links.
    - **Note**: If the document includes multiple addresses or emails, list them all.

    #### **2. Education**
    - Gather education-related data, including institutions attended, degrees obtained, grades, start and end dates, and details about the education level (with codes and descriptions if available).

    #### **3. Employment**
    - Extract details about work experience: employer name, job title, location, role description, duration of employment, and industry sectors.

    #### **4. Skills**
    - Map the candidate's skills into four main categories: technical skills (computerSkill), language skills (languageSkill), other skills, and soft skills. Include durations or proficiency levels when available.

    #### **5. Summary**
    - Summarize key information, such as the current employer, current job, highest education level, and a summary of ambitions or experiences.

    #### **6. Additional Data**
    - Categorize hobbies and other additional information separately.

    ### **Additional Requirements**
    - **CV Language**: If possible, detect and specify the language of the CV in the `lang` field.
    - Use ordered lists for multiple or unambiguous fields (e.g., addresses, emails).
    - Ensure the output is in JSON format and adheres to the provided Pydantic schema.
       
    """


class Address(BaseModel):
    street: Optional[str]
    city: Optional[str]
    region: Optional[str]
    country: Optional[str]
    postalCode: Optional[str]


class Personal(BaseModel):
    address: Optional[Address]
    dateOfBirth: Optional[str]
    driversLicense: Optional[str]
    email: List[str]
    firstName: Optional[str]
    gender: Optional[str]
    homePhone: List[str]
    lastName: Optional[str]
    middleName: Optional[str]
    mobilePhone: List[str]
    nationality: Optional[dict]
    personalWebsite: List[dict]
    phonelist: Optional[dict]
    socialMediaLink: List[dict]


class EducationHistory(BaseModel):
    degreeDirection: Optional[str] = Field(description="Field of study.")
    endDate: Optional[str] = Field(description="End date in ISO format.")
    instituteName: Optional[str]
    institutePlace: Optional[str]
    levelCodeDescription: Optional[str] = Field(description="Description of education level.")
    startDate: Optional[str] = Field(description="Start date in ISO format.")
    grade: Optional[str] = Field(description="Grade or score achieved.")


class EmploymentHistory(BaseModel):
    description: Optional[str]
    employerName: Optional[str]
    employerPlace: Optional[str]
    endDate: Optional[str] = Field(description="End date in ISO format.")
    experienceYears: Optional[str]
    industries: Optional[str]
    jobTitle: Optional[str]
    startDate: Optional[str] = Field(description="Start date in ISO format.")


class LanguageSkill(BaseModel):
    language: Optional[str]
    level: Optional[str] = Field(
        description="Language proficiency level according to CEFR (A1, A2, B1, B2, C1, C2)."
    )


class Skills(BaseModel):
    computerSkill: List[str]
    languageSkill: List[LanguageSkill]
    otherSkill: List[str]
    softSkill: List[str]


class Other(BaseModel):
    hobbies: List[str]


class RootModel(BaseModel):
    educationHistory: List[EducationHistory]
    employmentHistory: List[EmploymentHistory]
    lang: Optional[str] = Field(description="Language of the CV.")
    other: Optional[Other]
    personal: Personal
    skills: Skills


RootModel.model_rebuild()

def get_schema():
    return RootModel.model_json_schema()


if __name__ == "__main__":
    # Example instantiation (replace with actual data during implementation):
    example_data = {
        "educationHistory": [
            {
                "degreeDirection": "Diploma di maturità scientifica",
                "endDate": "",
                "instituteName": "LICEO B. PASCAL, POMEZIA (RM)",
                "institutePlace": "POMEZIA",
                "levelCodeDescription": "Secondary Education",
                "startDate": "",
                "grade": "",
            }
        ],
        "employmentHistory": [
            {
                "description": "Affiancamento alla direzione con seguenti mansioni:\n   Accoglienza clienti ( Check-In/ Check-Out)\n   Gestione chiamate e gestione e-mail (in entrata e in uscita)\n   Gestione sull'impostazione del piano lavoro giornaliero/settimanale\n   Gestione database d'ufficio per registrazione clienti in Ricestat e questura clienti allogiati\n   organizzazzione piano camere giornaliero/settimanali\n   Gestione pratiche partenze/arrivi\n   Gestione contabile soggiorni\n   Contabilità relativa ad apertura e chiusura cassa\n   Ordinaria amministrazione",
                "employerName": "MORETTI DAVIDE S.R.L RESORT ELBA ISLAND",
                "employerPlace": "",
                "endDate": "2021-10-31",
                "experienceYears": "1",
                "industries": None,
                "jobTitle": "Receptionist/ accoglienza Front Office",
                "startDate": "2021-05-01",
            },
            {
                "description": "con mansioni di:\nDematerializzazione, digitalizzazione\ne archiviazione\nCreazione documento in adeguato\nformato digitale\nProtocollo informatico\nScansione\nAnalisi di dettaglio dell'archivio\nConservazione documenti",
                "employerName": "CAPITAL LOGISTICA SRL, VIA PALERMO IANIRI 33, L Y",
                "employerPlace": "PALERMO",
                "endDate": "2020-01-31",
                "experienceYears": "1",
                "industries": None,
                "jobTitle": "Impiegata",
                "startDate": "2019-01-01",
            },
            {
                "description": "settore protocollo e affari generali\ncon mansioni di:\n    Controllo posta elettronica e news\n    intranet\n    Protocollo e archiviazione della\n    corrispondenza in entrata e in uscita\n    Invio del protocollo giornaliero in\n    conservazione a norma\n    Convocazione Collegio Docenti, Giunta\n    Esecutiva, Consiglio d'Istituto , RSU\n    Circolari e avvisi\n    Pubblicazione atti, circolari e avvisi sul\n    sito web d'istituto\n   Scambio corrispondenze con Enti Locali per eventuali manutenzioni o supporti\n   tecnici\n   Visite d'istruzione\n   Supporto al settore didattica\n   Rilevazione scioperi e assemblee ( in collaborazione con ufficio personale)",
                "employerName": "STITUTO COMPRENSIVO STATALE",
                "employerPlace": "",
                "endDate": "",
                "experienceYears": "1",
                "industries": None,
                "jobTitle": "Assistente amministrativa",
                "startDate": "2019-01-01",
            },
            {
                "description": 'con mansioni di:\n   Apertura/ Chiusura cassa\n   Compilazione fatture/tax free\n   Gestione scarico merci\n   Gestionale aziendale vendita " Best Store "\nVisual con mansioni di :\n   Allestimento p.v. e vetrine con giornaliero assortimento di collezione\n   Riorganizzazione e completo rifacimento del p.v.\n    durante periodi promozionali\n   Rifacimento del p.v. con ripristino layout base in periodi non promozionali\n   Riorganizzazione ciclica del magazzino\n   Monitoraggio giornaliero/mensile/ annuale target e obiettivi\n   Monitoraggio andamento generale KPI\n   Vendita articolo per mq',
                "employerName": "LIU.JO S.P.A. VIA J.A. FLEMING, 17 Carpi (MO) P.V. CASTEL ROMANO",
                "employerPlace": "Carpi",
                "endDate": "2019-10-31",
                "experienceYears": "5",
                "industries": None,
                "jobTitle": "Shop Assistent - Costumer Service",
                "startDate": "2014-11-01",
            },
            {
                "description": "con mansioni di:\n   Apertura/ Chiusura cassa\n   Compilazione fatture/tax free\n   Gestione scarico merci\nVisual con mansioni di :\n    Allestimento p.v. e vetrine con giornaliero assortimento di collezione\n    Riorganizzazione e completo rifacimento del p.v. durante periodi promozionali\n   Rifacimento del p.v. con ripristino layout base in periodi non promozionali\n   Riorganizzazione ciclica del magazzino\n    Monitoraggio giornaliero/mensile/ annuale target e obiettivi\n   Monitoraggio andamento generale KPI\n   Vendita articolo per mq",
                "employerName": "DESIREE' RETAIL S.R.L. VIA VITTORIA COLONNA (NP) P.V. CASTEL ROMANO",
                "employerPlace": "VITTORIA",
                "endDate": "2014-09-30",
                "experienceYears": "6",
                "industries": None,
                "jobTitle": "Vice store manager",
                "startDate": "2008-11-01",
            },
        ],
        "lang": "italian",
        "other": {"hobbies": ["leggere"]},
        "personal": {
            "address": {
                "street": "Via Perugia 14",
                "city": "Ardea",
                "region": "IT Lazio",
                "country": "Italy",
                "postalCode": "",
            },
            "dateOfBirth": "1990-03-24",
            "driversLicense": "",
            "email": ["<EMAIL>"],
            "firstName": "",
            "gender": "Not known",
            "homePhone": [""],
            "lastName": "Jennifer",
            "middleName": "",
            "mobilePhone": [""],
            "nationality": {"code": "", "codeDescription": ""},
            "personalWebsite": [{"type": None, "url": ""}],
            "phonelist": None,
            "socialMediaLink": [],
        },
        "skills": {
            "computerSkill": [
                "database",
                "formato digitale",
                "gestione e-mail",
                "posta elettronica",
                "PACCHETTO OFFICE",
                "WORD",
                "Protocollo informatico",
            ],
            "languageSkill": [
                {"language": "French", "level": "Basic Knowledge"},
                {"language": "English", "level": "Basic Knowledge"},
                {"language": "Italian", "level": "Native"},
            ],
            "otherSkill": [
                "Accoglienza clienti",
                "accoglienza",
                "Capacità di ascolto",
                "Contabilità",
                "Gestione contabile",
                "fatture",
                "Front Office",
                "gestione del lavoro",
                "amministrazione",
                "cassa",
                "Riorganizzazione",
                "telematica",
                "vendita",
                "RETAIL",
            ],
            "softSkill": ["problem solving"],
        },
    }

    # Create an instance of the model with example data
    response = RootModel(**example_data)
    print(response.model_dump_json(indent=4))
