# backend/cooper/api/helpers.py
from __future__ import annotations

from fastapi.responses import JSONResponse, StreamingResponse


def send_response(response_generator, stream, content_type="text/event-stream"):
    # Se vogliamo inviare dati in streaming mode
    if stream:
        return StreamingResponse(response_generator, media_type=content_type)

    # Altrimenti se vogliamo inviare il dato tutto in una volta:
    # Verifica se `response_generator` è una stringa statica o un generatore
    if isinstance(response_generator, str):
        # Se è una stringa, restituiscila direttamente
        return JSONResponse(response_generator)

    # Aggrega tutti i chunk per una risposta completa
    content = "".join(response_generator)
    return JSONResponse(content)
