# Direct LLM Requests Script

This documentation provides an overview of a Python script designed to send multiple **asynchronous** requests to an LLM (Large Language Model) server. The script allows you to:

- Read configuration parameters from YAML files.
- Dynamically load templates that define system prompts and JSON schemas.
- Construct and send requests in parallel using asyncio to control concurrency.
- Save individual responses and a summary of all requests to JSON files.

Below, you will find details on how each part of the script works, how to configure it, and how to run it.

---

## 📌 Important Notes:
- **optimal settings*** 
   ```bash
   # Working Setting (with good performance):
   vllm==0.6.6.post1
   xgrammar==0.1.11
   transformers==4.49.0
   ```
- **vllm==0.7.3 V1** currently **does not properly enforce** xGrammar==0.1.11 constraints, likely due to changes in the tokenizer/decoding pipeline introduced in recent vLLM releases.
- While **vllm==0.7.3 (V0)** works with xgrammar==0.1.11, it suffers from a severe performance degradation when complex templates are involved, due to a **fallback to outlines**, which makes the entire system extremely slow and inefficient for high-volume processing. Observed performance drop:
   -  ~512 CVs processed with batch size 256:
      - Expected time (with **optimal settings***): < 1 second
      - Observed time (with fallback to outlines): > 4 seconds



## Overview

1. **Script Name**: `document_parser_docker.py` (the main code shown).
2. **Core Functionality**:
   - Reads a configuration file (`config.yml`).
   - Uses a second configuration file (`config_engine_llm.yml`) to load the LLM engine settings (e.g., `model_name`).
   - Loads documents from a Parquet file (using [Polars](https://www.pola.rs/)).
   - Optionally pre-processes data (combining columns, creating JSON structures, etc.).
   - Sends multiple asynchronous requests to an LLM endpoint (through `AsyncOpenAI`).
   - Saves individual responses and a summary of performance metrics.

3. **Asynchronous Requests**:
   - Each request is handled by the function `make_generate_request`.
   - A semaphore (`asyncio.Semaphore`) is used to limit the maximum number of concurrent requests.
   - Responses are stored in individual JSON files, and a summary is created at the end.

4. **Template Module**:
   - The script dynamically loads a custom template module (specified in `config.yml` under `template_module_path`).
   - The template module must define at least:
     - `get_default_system_prompt()` – returns a system-level prompt.
     - `get_schema()` (optional) – returns a JSON schema used for guided decoding.

5. **Guided Decoding (Optional)**:
   - You can pass a `guided_decoding_backend` parameter in `config.yml`.
   - If provided, the script includes an `extra_body` in the request (e.g., `guided_json`) that the LLM endpoint can use to produce structured outputs.

---

## Configuration Files

### 1. `config_engine_llm.yml`

Example:
```yaml
ports_gpus:
  8000: 0
  8001: 1
  8002: 2
  8003: 3
show_metrics: true
model_name: "Qwen/Qwen2.5-32B-Instruct-GPTQ-Int4"
```

- **model_name**: The LLM model name (e.g., `Qwen/Qwen2.5-32B-Instruct-GPTQ-Int4`).
- Other parameters may be used in your environment (e.g., GPU ports, metrics), but the key parameter for this script is `model_name`.

### 2. `config.yml`

Example:
```yaml
config_engine_llm: /path/to/config_engine_llm.yml
template_module_path: /path/to/work_experiences.py
document_input_file: /data/datascience/cv_extract_info/datasets/tk_cv_sample.parquet
guided_decoding_backend: xgrammar
results_dir: /path/to/benchmark/assets/work_experiences
server_log: /path/to/benchmark/.log
max_concurrent_requests: 1000
batch_size: 4
use_stream: false
load_balancer_url: http://localhost:5000/v1/chat/completions
text_columns:
  - CVI_CANDIDATE_CV_CLEANED
id_columns:
  - DATABASE
  - EMPLOYEE_ID
n_rows: 4
is_benchmark: true
```

**Key Parameters**:

- `config_engine_llm`: Path to the **engine** configuration file (e.g., `config_engine_llm.yml`).
- `template_module_path`: Path to a Python module that defines:
  - `get_default_system_prompt()`
  - `get_schema()`
- `document_input_file`: Path to a Parquet file containing the input documents.
- `guided_decoding_backend`: A string identifier (e.g., `xgrammar`) to enable structured/guided decoding.
- `results_dir`: A directory where the script saves results and summaries.
- `max_concurrent_requests`: Maximum number of requests to run **in parallel**.
- `use_stream`: (Boolean) If `true`, use streaming responses from the LLM; otherwise, make a standard request.
- `text_columns`: List of column names in your Parquet file that contain the text to be sent to the LLM.
- `id_columns`: List of column names to uniquely identify each document (these columns will be concatenated into `full_id`).
- `n_rows`: Number of rows to load from the Parquet file (optional). If omitted or set to `None`, the entire file is loaded.
- `is_benchmark`: (Boolean) If `true`, the script will store results in a `.benchmark` folder and copy the config files there for reproducibility.

---

## Script Explanation

Below is a brief explanation of each function and how the script flows.

### 1. `load_template_module_from_path(path: str)`

```python
def load_template_module_from_path(path: str):
    """
    Dynamically loads a Python module from a given filesystem path.
    """
    spec = importlib.util.spec_from_file_location("template_module", path)
    template_module = importlib.util.module_from_spec(spec)
    spec.loader.exec_module(template_module)
    return template_module
```

- Dynamically imports a Python file at runtime.
- Returns a module object (e.g., your custom prompt and schema definitions).

### 2. `make_generate_request(...)`

```python
async def make_generate_request(
    semaphore: asyncio.Semaphore,
    client: AsyncOpenAI,
    request_id: int,
    chat_history: list | None,
    document_text: str,
    document_id: str,
    model_name: str,
    system_content: str,
    use_stream: bool,
    extra_body: dict | None,
    results_dir: Path,
):
    ...
```

- **Purpose**: Sends a single asynchronous request to the LLM server using `AsyncOpenAI`.
- **Parameters**:
  - `semaphore`: Limits concurrency (ensures we do not exceed the `max_concurrent_requests`).
  - `client`: An instance of the custom `AsyncOpenAI` client.
  - `request_id`: A numerical ID for the request (0-based index).
  - `chat_history`: Optionally includes role-user pairs (list of messages).  
  - `document_text`: The main text prompt to send to the LLM.
  - `document_id`: A unique identifier (constructed from `id_columns`).
  - `model_name`: The LLM model name to use.
  - `system_content`: The system prompt content (returned by `get_default_system_prompt()`).
  - `use_stream`: Whether to use streaming or not.
  - `extra_body`: Additional parameters for guided decoding (if any).
  - `results_dir`: Directory where individual response files will be stored.

- **Behavior**:
  1. Acquires the semaphore (ensuring concurrency limits).
  2. Prepares the `messages` array for the LLM request (including `system_content` + `chat_history` + user content).
  3. Sends the request to `client.chat.completions.create(...)`.
  4. If `extra_body` is set, attempts to parse the response as JSON (via `safe_json_loads` and `clean_json_keys`).
  5. Saves the result (including elapsed time) in a JSON file named `response_{request_id}.json`.

### 3. `simulate_requests(...)`

```python
async def simulate_requests(
    df_documents: pl.DataFrame,
    model_name: str,
    base_url: str,
    api_key: str,
    max_concurrent_requests: int,
    use_stream: bool,
    system_content: str,
    extra_body: dict | None,
    results_dir: Path,
):
    ...
```

- **Purpose**: Orchestrates the creation and execution of **multiple** asynchronous requests.
- **Steps**:
  1. Initializes `AsyncOpenAI` client (`client = AsyncOpenAI(base_url=base_url, api_key=api_key, max_retries=1)`).
  2. Creates a semaphore to limit concurrency (`asyncio.Semaphore(max_concurrent_requests)`).
  3. For each row in the `df_documents`:
     - Extracts the document text (and optional chat history).
     - Creates a task pointing to `make_generate_request(...)`.
  4. Uses `asyncio.gather` to launch all tasks concurrently.
  5. Collects each task’s results to compute aggregate statistics:
     - `n_requests` (total requests).
     - `max_concurrent_requests`.
     - `total_time` (time to run all requests).
     - `avg_time_per_batch` (average of individual request times).
     - `time_per_request` (total_time / number_of_requests).

- **Return Value**: A dictionary containing summary metrics.

### 4. `run(config_file: str)`

```python
def run(config_file: str):
    """
    Main function that reads the configuration, loads data,
    and starts the execution of requests in parallel.
    """
    ...
```

- **Purpose**: Entry point for programmatic usage (if you call `run` from other code or from `main`).
- **Flow**:
  1. Reads parameters from `config_file` (`config.yml`).
  2. Reads engine parameters from `parameters["config_engine_llm"]` (i.e., `config_engine_llm.yml`).
  3. Extracts critical settings: concurrency, streaming, text columns, ID columns, number of rows, etc.
  4. Constructs file paths (e.g., `document_input_file`, `results_dir`).
  5. Dynamically loads the template module and retrieves:
     - `system_content` (from `get_default_system_prompt()`).
     - `extra_body` and `guided_decoding_backend` if required.
  6. Creates or updates the `results_dir` path (including a timestamp if `is_benchmark`).
  7. Loads your Parquet data into a Polars DataFrame (`df_documents`).
  8. Optionally merges columns or structures data for chat history (depending on `role_column`, `order_column`, etc.).
  9. Invokes `simulate_requests(...)` (an async function) using `asyncio.run()`.
  10. Saves summary results to JSON and copies relevant config files to the results directory for reproducibility.

### 5. `main()`

```python
def main():
    """
    Parses command-line arguments
    and calls the run function with the configuration file.
    """
    parser = argparse.ArgumentParser(description="Launch direct LLM requests script.")
    parser.add_argument(
        "--config",
        type=str,
        required=True,
        help="Path to the configuration file.",
    )
    args = parser.parse_args()

    logger.info("RUN")
    run(config_file=args.config)
```

- **Purpose**: Provides a CLI interface for the script.
- **Usage**:
  ```bash
  python document_parser_docker.py --config /path/to/config.yml
  ```

---

## Typical Usage

1. **Prepare Your Configuration Files**  
   - Ensure `config.yml` points to the correct `config_engine_llm.yml`, `document_input_file`, `template_module_path`, etc.
   - Adjust `max_concurrent_requests`, `text_columns`, `id_columns`, etc., as needed.

2. **Prepare Your Template Module**  
   - Must have at least:
     ```python
     def get_default_system_prompt():
         return "Your system-level instructions or context here..."

     def get_schema():
         return {
             # JSON schema for guided decoding, if needed
         }
     ```
   - Save it as a `.py` file (e.g., `work_experiences.py`).

3. **Install Dependencies**  
   - Make sure your environment has the necessary Python packages.

4. **Run the Script**  
   ```bash
   python document_parser_docker.py --config /path/to/config.yml
   ```

5. **Check Outputs**  
   - **Individual Responses**: Inside `<results_dir>/<end_path>` (or `.benchmark/<timestamp>`) you will find files named like `response_0.json`, `response_1.json`, etc.
   - **Summary File**: A `summary_struct_{guided_decoding_backend}.json` file will be in the parent directory of `<end_path>` (or inside the `.benchmark` folder if `is_benchmark` is set to `true`).  

6. **Reproducibility**  
   - If `is_benchmark = true`, both the config (`config.yml`) and engine config (`config_engine_llm.yml`) files are copied into the `.benchmark` results directory.  

---

## Example

Consider the following structure:

```
.
├─ document_parser_docker.py           # The main script
├─ config.yml                          # Points to config_engine_llm.yml, template module, etc.
├─ config_engine_llm.yml
├─ my_custom_template.py
└─ data
   └─ sample.parquet                   # Input file
```

An example run command:

```bash
python document_parser_docker.py \
  --config config.yml
```

**What Happens**:
1. The script loads `config.yml`.
2. Finds `config_engine_llm.yml` for LLM settings (`model_name`).
3. Reads the template from `my_custom_template.py` (which defines your system prompt and schema).
4. Loads up to `n_rows` from `data/sample.parquet`.
5. Spawns asynchronous requests to the LLM endpoint at `base_url` (`http://localhost:8081/v1` in the code).
6. Saves each response in `<results_dir>/<end_path>/response_{request_id}.json`.
7. Creates a summary JSON file with performance metrics.

---

## Customizing

- **Concurrency**: Adjust `max_concurrent_requests` in `config.yml`.
- **Streaming**: Toggle `use_stream` in `config.yml`.
- **Batch Size**: Not directly used in the script’s code, but you could integrate it for chunked requests if desired.
- **Schema / Prompt**: Modify your template module if you need a different system prompt or JSON schema.
- **Load Balancer**: The script uses `base_url = "http://localhost:8081/v1"` directly. You could replace or configure this in the script or with an additional parameter if you prefer.

---

## Logging and Error Handling

- Uses a custom `get_logger` from `cooper.utils` for logging.
- Each request logs errors if exceptions occur (e.g., parsing failures).
- If a request fails, it returns `(float("inf"), str(e), request_id)` so the summary can skip invalid times.

---

## Notes on Parallel Execution

- The `asyncio.Semaphore` enforces a maximum of `max_concurrent_requests` concurrent tasks.
- `asyncio.gather` is used to run tasks in parallel. This helps to stress-test or measure throughput against the LLM server.

---

## Contributing

- **Extending**: If you want to add new metrics or handle data differently, update `simulate_requests` or the post-processing sections in `run`.
- **Bug Reports**: Check logs in the output directory to see any exceptions from the LLM requests.
- **Testing**: You can run partial tests by setting `n_rows` to a small number in `config.yml` to limit requests while developing.

---

## Conclusion

This script offers a robust framework to **batch-process** requests to a Large Language Model, handle concurrency gracefully, and collect both individual responses and global metrics. By adjusting the YAML configuration, you can tailor it to a variety of use cases, from small local tests to large-scale benchmarks.

For additional details, review and modify the script’s source code, or place your template and data in the appropriate paths specified by the configuration files. If you have more complex needs (e.g., advanced data transformations, chaining LLM requests), consider building on top of the existing functions or adding new stages in the pipeline.