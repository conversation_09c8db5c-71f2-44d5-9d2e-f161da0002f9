config_engine_llm: /home/<USER>/workarea/ai_playground/backend/cooper/api/llm/benchmark/config_engine_llm.yml
template_module_path: /home/<USER>/workarea/ai_playground/backend/cooper/api/llm/template_prompts/evaluation_resume_extraction.py
document_input_file: /data/datascience/cv_extract_info/datasets/evaluation_tk_work_work_experiences.parquet
guided_decoding_backend: xgrammar
results_dir: /home/<USER>/workarea/ai_playground/backend/cooper/api/llm/benchmark/assets/evaluation_cv_work_experiences_parser_tk
server_log: /home/<USER>/workarea/ai_playground/backend/cooper/api/llm/benchmark/.log
max_concurrent_requests: 1000  # Numero massimo di richieste attive contemporaneamente ## NEW
batch_size: 4
use_stream: false
load_balancer_url: http://localhost:5000/v1/chat/completions
text_columns: 
  - TEMPLATE
id_columns:
  - DATABASE
  - EMPLOYEE_ID
n_rows: 4
is_benchmark: true # if not you'll skip all files already parsed