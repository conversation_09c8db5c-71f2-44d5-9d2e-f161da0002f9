
# Mistral3ForConditionalGeneration has no vLLM implementation and
# the Transformers implementation is not compatible with vLLM.
# Try setting VLLM_USE_V1=0.
# https://huggingface.co/mistralai/Mistral-Small-3.1-24B-Instruct-2503/discussions/16#67d9a33be16d500188516878
export VLLM_USE_V1=0

docker run -d \
    --rm \
    --runtime nvidia \
    --gpus ${GPU_VLLM} \
    -v "${VLLM_CACHE}:${VLLM_CACHE}" \
    -p "${VLLM_PORT}:${VLLM_PORT}" \
    -e HF_HUB_DOWNLOAD_TIMEOUT=120 \
    --env "HUGGING_FACE_HUB_TOKEN=${HUGGINGFACE_TOKEN}" \
    --name "${CONTAINER_VLLM}" \
    vllm/vllm-openai:v0.8.3.full \
    --model "${VLLM_DISTRIBUTION}" \
    --port "${VLLM_PORT}" \
    --gpu_memory_utilization 0.9 \
    --enable-prefix-caching \
    --max_model_len 30720 \
    --tensor-parallel-size 1 \
    --api-key token-abc123 \
    --download-dir "${VLLM_CACHE}" \
    --guided-decoding-backend xgrammar \
    --tokenizer_mode mistral \
    --config_format mistral \
    --load_format mistral \
    --tool-call-parser mistral \
    --enable-auto-tool-choice \
    --limit_mm_per_prompt 'image=3' \
    --trust-remote-code
