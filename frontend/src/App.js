import React from "react";
import { BrowserRouter as Router, Route, Routes, Navigate } from "react-router-dom";
import { AuthProvider, useAuth } from "./services/AuthContext";
import Login from "./services/Login";
import Playground from "./components/Playground";
import './styles/styles.css';

// Componente per le route protette
function PrivateRoute({ children }) {
  const { token } = useAuth();
  return token ? children : <Navigate to="/login" />;
}

function App() {
  return (
    <AuthProvider>
      <Router>
        <Routes>
          {/* Route pubblica per il login */}
          <Route path="/login" element={<Login />} />
          {/* Route protetta: se non autenticato, redirige al login */}
          <Route
            path="/"
            element={
              <PrivateRoute>
                <Playground />
              </PrivateRoute>
            }
          />
          {/* Altre route protette se necessario */}
        </Routes>
      </Router>
    </AuthProvider>
  );
}

export default App;