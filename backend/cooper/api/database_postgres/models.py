from __future__ import annotations

from cooper.api.database_postgres.database import Base
from sqlalchemy import (
    Column,
    Integer,
    String,
)


class User(Base):
    __tablename__ = "user"
    id = Column(Integer, primary_key=True, autoincrement=True)
    username = Column(String, unique=True, nullable=False)
    hashed_password = Column(String, nullable=False)
    role = Column(String, default="user")
    country = Column(String, nullable=True)
    brand = Column(String, nullable=True)


class Vacancy(Base):
    __tablename__ = "vacancy"
    id = Column(Integer, primary_key=True, autoincrement=False)
    database = Column(String, primary_key=True, nullable=False)
    brand = Column(String, nullable=True)
