from __future__ import annotations

import asyncio
import contextlib
import itertools
import json
from typing import AsyncIterator

import httpx
import uvicorn
import yaml
from fastapi import FastAPI, HTTPException, Request
from fastapi.responses import StreamingResponse
from openai import AsyncOpenAI

# Configurazione
CONCURRENCY_LIMIT = 3000
UPSTREAM_TIMEOUT = 300

with open(
    "/home/<USER>/workarea/ai_playground/backend/cooper/api/llm/benchmark/config_engine_llm.yml"
) as f:
    config_engine_llm = yaml.safe_load(f)

ports_gpus = config_engine_llm["ports_gpus"]
engine = config_engine_llm["engine"]


@contextlib.asynccontextmanager
async def lifespan(app: FastAPI) -> AsyncIterator[None]:
    # Inizializzazione client HTTP
    app.state.http_clients = {
        port: httpx.AsyncClient(timeout=UPSTREAM_TIMEOUT) for port in ports_gpus
    }

    # Inizializzazione client OpenAI
    app.state.openai_clients = {
        port: AsyncOpenAI(
            base_url=f"http://localhost:{port}/v1",
            api_key="token-abc123",
            timeout=UPSTREAM_TIMEOUT,
        )
        for port in ports_gpus
    }

    yield  # Fine startup, inizio runtime

    # Cleanup allo shutdown
    for client in app.state.http_clients.values():
        await client.aclose()

    # Chiudi tutti i client OpenAI
    for client in app.state.openai_clients.values():
        await client.close()


app = FastAPI(lifespan=lifespan)


class SafeRoundRobin:
    def __init__(self, items):
        self.items = itertools.cycle(items)
        self.lock = asyncio.Lock()

    async def next(self):
        async with self.lock:
            return next(self.items)


ports_rr = SafeRoundRobin(ports_gpus)
semaphore = asyncio.Semaphore(CONCURRENCY_LIMIT)


@app.post("/v1/chat/completions")
async def forward_request(request: Request, payload: dict):
    try:
        async with semaphore:
            port = await ports_rr.next()
            client = request.app.state.openai_clients[port]

            if "messages" not in payload or "model" not in payload:
                raise HTTPException(400, "Missing 'messages' or 'model'")

            stream = payload.get("stream", False)

            if engine == "vllm":
                upstream_request = client.chat.completions.create(
                    model=payload["model"],
                    messages=payload["messages"],
                    stream=stream,
                    extra_body=payload.get("extra_body", {}),
                    seed=42,
                )

            if engine == "sglang":
                upstream_request = client.chat.completions.create(
                    model=payload["model"],
                    messages=payload["messages"],
                    response_format=payload.get("response_format", {}),
                    seed=42,
                )

            if stream:
                return StreamingResponse(
                    stream_generator(upstream_request, port),
                    media_type="text/event-stream",
                )

            response = await upstream_request
            return format_response(response, port)

    except httpx.ReadTimeout:
        raise HTTPException(504, f"Upstream timeout on port {port}")
    except Exception as e:
        raise HTTPException(500, f"Error on port {port}: {str(e)}")


async def stream_generator(response, port):
    try:
        buffer = []
        async for chunk in response:
            content = chunk.choices[0].delta.content or ""
            buffer.append(content)
            yield (
                json.dumps({"text": content, "port": port, "model": chunk.model}) + "\n"
            )

        yield (
            json.dumps(
                {"complete_text": "".join(buffer), "port": port, "status": "completed"}
            )
            + "\n"
        )
    except Exception as e:
        yield json.dumps({"error": str(e), "port": port, "status": "error"}) + "\n"


def format_response(response, port):
    return {
        "text": response.choices[0].message.content,
        "port": port,
        "model": response.model,
        "usage": response.usage.dict() if response.usage else None,
    }


@app.get("/v1/models")
async def get_models(request: Request):
    try:
        tasks = [
            get_models_for_port(port, request.app.state.http_clients[port])
            for port in ports_gpus
        ]
        results = await asyncio.gather(*tasks)
        return {"data": results, "object": "list"}
    except Exception as e:
        raise HTTPException(500, f"Model discovery failed: {str(e)}")


async def get_models_for_port(port: int, client: httpx.AsyncClient):
    try:
        response = await client.get(
            f"http://localhost:{port}/v1/models",
            headers={"Authorization": "Bearer token-abc123"},
        )
        return (
            response.json() if response.status_code == 200 else {"error": response.text}
        )
    except Exception as e:
        return {"error": str(e)}


# Sostituisci la parte finale con:
if __name__ == "__main__":
    # Utilizza direttamente il modulo per Uvicorn
    uvicorn.run(
        "load_balancer:app",  # Importa l'app dal modulo corrente
        host="0.0.0.0",
        port=5000,
        workers=4,
        timeout_keep_alive=600,
        log_config=None,  # Disabilita i log di Uvicorn se necessario
    )
