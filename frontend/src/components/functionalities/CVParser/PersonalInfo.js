import React from 'react';
import AddressDisplay from './AddressDisplay';

const PersonalInfo = ({ personal }) => (
    <div className="personal-info-card">
        <p><strong>Name:</strong> {personal.firstName || 'N/D'}</p>
        <p><strong>Middlename:</strong> {personal.middleName || 'N/D'}</p>
        <p><strong>Surname:</strong> {personal.lastName || 'N/D'}</p>
        <p><strong>Date of Birth:</strong> {personal.dateOfBirth || 'N/D'}</p>
        <p><strong>Gender:</strong> {personal.gender || 'N/D'}</p>
        <p><strong>Email:</strong> {personal.email && personal.email.join(', ')}</p>
        <p><strong>Home Phone:</strong> {personal.homePhone && personal.homePhone.join(', ')}</p>
        <p><strong>Mobile Phone:</strong> {personal.mobilePhone && personal.mobilePhone.join(', ')}</p>
        {personal.address && (
            <section className="cv-section">
                <h1>Address</h1>
                <AddressDisplay address={personal.address} />
            </section>
        )}
    </div>
);

export default PersonalInfo;
