// frontend/src/components/Logout.js
import { useNavigate } from "react-router-dom";
import { useAuth } from "./AuthContext";

function Logout() {
    const { setToken, setUserId, setUser, setRole, setCountry, setBrand } = useAuth();
    const navigate = useNavigate();

    const handleLogout = () => {

        // Remove token and additional fields from localStorage
        localStorage.removeItem("token");
        localStorage.removeItem("user_id");
        localStorage.removeItem("user");
        localStorage.removeItem("role");
        localStorage.removeItem("country");
        localStorage.removeItem("brand");

        // Clear the authentication state in the context
        setToken(null);
        setUserId(null);
        setUser(null);
        setRole(null);
        setCountry(null);
        setBrand(null);

        navigate("/", { replace: true });
    };

    return (
        <button onClick={handleLogout} className="playground-header-link">
            Logout
        </button>
    );
}

export default Logout;
