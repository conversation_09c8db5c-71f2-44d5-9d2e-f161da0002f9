/* SmartMatching.css */

.smart-matching-container {
    background-color: #f0f4f8;
    padding: 30px;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

.smart-matching-header h2 {
    margin: 0;
    color: #333;
    font-size: 2em;
    font-weight: 600;
}


.vacancy-section {
    margin: 30px;
    list-style-type: disc;
}

.smart-matching-header p {
    color: #666;
    font-size: 1em;
    margin-top: 5px;
}

.smart-matching-form {
    padding-top: 30px;
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 20px;
}

.filter-blocks {
    display: flex;
    gap: 10px;
    padding-top: 10px;
}

.filter-button {
    padding: 10px 20px;
    background-color: #e9ecef;
    border: 1px solid #ccc;
    cursor: pointer;
    font-size: 1rem;
    transition: background-color 0.3s ease, color 0.3s ease;
}

.filter-button.active {
    background-color: #007bff;
    color: #fff;
    border-color: #0056b3;
}

.filter-button:hover:not(.active) {
    background-color: #ddd;
}

.filter-set-values {
    display: flex;
    width: 25%;
    flex-direction: column;
    background-color: #fff;
    padding: 10px 20px;
}

.form-group {
    display: flex;
    flex-direction: column;
}

.form-group label {
    font-weight: 600;
    margin-bottom: 10px;
    color: #333;
}

.form-group input,
.form-group select {
    padding: 10px;
    border: 1px solid #ccc;
    font-size: 1em;
}

.form-group input:focus,
.form-group select:focus {
    border-color: #007bff;
    outline: none;
}

.filter-apply-button {
    display: flex;
    justify-content: center;
    padding: 5px;
}

.filter-group {
    display: flex;
    border: 0;
    padding-bottom: 20px;
    gap: 20px;
}

.filter-group label {
    width: 100%;
    font-weight: 600;
}

.filter-group input,
.filter-group select {
    text-align: right;
    font-size: 1em;
    width: 100%;
    border: 0;
}

.checkbox-group {
    grid-column: span 2;
    display: flex;
    align-items: center;
}

.checkbox-group input {
    margin-right: 10px;
}

.checkbox-group label {
    margin: 0;
    font-weight: 600;
    color: #333;
}

.submit-button {
    padding: 15px;
    background-color: #007bff;
    color: #fff;
    font-size: 1em;
    border: none;
    cursor: pointer;
    font-weight: 600;
    transition: background-color 0.3s ease;
    margin-top: 20px;
    width: 100%;
}

.submit-button:hover {
    background-color: #0056b3;
}

.submit-button svg {
    margin-right: 10px;
}

.response-container {
    margin-top: 40px;
}

.vacancy-section {
    margin: 30px 0;
    padding: 20px;
    background-color: #ffffff;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    font-size: 0.9rem;
    line-height: 1.6;
}

.vacancy-section h3 {
    font-size: 1.8em;
    color: #333;
    margin-bottom: 20px;
    border-bottom: 2px solid #007bff;
    padding-bottom: 10px;
}

.vacancy-table {
    width: 100%;
    border-collapse: collapse;
    table-layout: auto;
}

.vacancy-table tbody tr:nth-child(odd) {
    background-color: #f9f9f9;
}

.vacancy-table tbody tr:nth-child(even) {
    background-color: #ffffff;
}

.vacancy-table td {
    padding: 15px;
    border: 1px solid #ddd;
    text-align: left;
    vertical-align: top;
    font-size: 0.9rem;
}

.key-column {
    font-weight: bold;
    color: #007bff;
    width: 30%;
}

.value-column {
    color: #555;
    width: 70%;
}

.vacancy-table tbody tr:hover {
    background-color: #f1f1f1;
    cursor: default;
}

.candidate-details {
    background-color: #ffffff;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    padding: 20px;
    margin-top: 20px;
    font-size: 0.9rem;
    line-height: 1.6;
}

.candidate-details h3 {
    font-size: 1.5em;
    color: #333;
    margin-bottom: 20px;
    border-bottom: 2px solid #007bff;
    padding-bottom: 10px;
}

.response-table {
    width: 100%;
    border-collapse: collapse;
    margin-top: 20px;
    font-family: 'Open Sans', sans-serif;
}

.response-table th,
.response-table td {
    padding: 15px;
    border: 1px solid #ddd;
    text-align: left;
    font-size: 0.9em;
}

.response-table th {
    background-color: #007bff;
    color: #fff;
    text-transform: uppercase;
    letter-spacing: 0.05em;
}

.response-table tr:nth-child(even) {
    background-color: #f9f9f9;
}

.response-table tr:hover {
    background-color: #f1f1f1;
}

.response-table a {
    color: #007bff;
    text-decoration: none;
    font-weight: 600;
}

.response-table a:hover {
    text-decoration: underline;
}

.back-button {
    display: flex;
    align-items: end;
    justify-content: flex-end;
}

.text-employee {
    max-width: 250px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

.pagination-controls {
    margin-top: 30px;
    display: flex;
    align-items: center;
    gap: 20px;
}

.pagination-input {
    display: flex;
    align-items: center;
    gap: 10px;
}

.pagination-input label {
    font-weight: 600;
    color: #333;
}

.pagination-input input {
    width: 60px;
    padding: 8px;
    border: 1px solid #ccc;
}

.update-button {
    padding: 10px 20px;
    background-color: #28a745;
    color: #fff;
    font-size: 1em;
    border: none;
    cursor: pointer;
    font-weight: 600;
    display: flex;
    align-items: center;
    transition: background-color 0.3s ease;
}

.update-button:hover {
    background-color: #218838;
}

.update-button svg {
    margin-right: 8px;
}

.icon-button {
    background: none;
    border: none;
    cursor: pointer;
    font-size: 1.25rem;
    color: #555;
    margin: 0 5px;
    padding: 5px;
    border-radius: 50%;
    transition: background-color 0.3s, color 0.3s;
}

.icon-button:hover {
    background-color: #f0f0f0;
    color: #000;
}

.icon-button:focus {
    outline: none;
    /* Rimuove il cerchio blu */
    background-color: #e0e0e0;
    /* Sfondo più scuro su focus */
}

@media (max-width: 768px) {
    .smart-matching-form {
        grid-template-columns: 1fr;
    }

    .checkbox-group {
        grid-column: span 1;
    }

    .submit-button {
        grid-column: span 1;
    }

    .pagination-controls {
        flex-direction: column;
        align-items: flex-start;
    }

    .pagination-input {
        width: 100%;
    }
}