import React, { useState, useEffect } from 'react';
import { FaSearch, FaSyncAlt, FaInfoCircle, FaExternalLinkAlt } from 'react-icons/fa';
import { TbArrowBackUp } from "react-icons/tb";
import './SmartMatchingTest.css';
import { useAuth } from '../../../services/AuthContext';

const SmartMatchingTest = ({ card, externalResponse, handleSubmit, onFileUpload, files, externalInput, onResponse }) => {
    const [internalInput, setInternalInput] = useState(51713);
    const [isLoading, setIsLoading] = useState(false);
    const [selectedDatabase, setSelectedDatabase] = useState('POLAND');
    const [limit, setLimit] = useState(15);
    const [offset, setOffset] = useState(1);
    const [expandedRow, setExpandedRow] = useState(null);

    // Stato per la vacancy corrente per l'utente loggato
    const [vacancyIndex, setVacancyIndex] = useState(0);
    const [totalVacancies, setTotalVacancies] = useState(0);

    // token per l'autenticazione nelle chiamate
    const { token } = useAuth();

    const databaseLinks = {
        ITALY: "https://itgigroupspinner.gigroup.local/#/employee/",
        BRAZIL: "https://brspinner.gigroup.local/#/employee/",
        POLAND: "https://plspinner.gigroup.local/#/employee/",
        PORTUGAL: "https://ptspinner.gigroup.local/#/employee/",
        SPAIN_GIG: "https://esgigroupspinner.gigroup.local/#/employee/",
        SPAIN_WYSER: "https://esspinner.gigroup.local//#/employee/",
    };

    const [vacancyData, setVacancyData] = useState(null);
    const [candidatesData, setCandidatesData] = useState(null);

    const VACANCY_KEYS_ITALIAN_TO_ENGLISH = {
        "titolo del lavoro": "Job Title",
        "tipo di lavoro": "Job Type",
        "luogo di lavoro": "Workplace",
        "provincia": "Province",
        "nazione del luogo di lavoro": "Country",
        "contratto di lavoro": "Employment Contract",
        "tipo di contratto": "Contract Type",
        "laurea": "Degree",
        "macro settore": "Industry Macro",
        "è cateogoria protetta?": "Protected Category?",
        "annuncio della posizione (jobad)": "Job Advertisement",
        "orario lavoro": "Working Hours",
        "titolo dell'annuncio (jobad)": "Job Ad Title",
        "brand": "Brand",
        "contratto di lavoro (strutturato)": "Structured Employment Contract",
        "nome del core profile": "Core Profile Name",
        "descrizione del core profile": "Core Profile Description",
        "attività core profile": "Core Profile Activities",
        "experienza core profile": "Core Profile Experience",
        "extra epecializzazione del core profile": "Extra Core Profile Specialization",
        "seniority core profile": "Core Profile Seniority",
        "livello del core profile": "Core Profile Level",
        "attività opzionali core profile": "Optional Core Profile Activities",
        "specializzazione del core profile": "Core Profile Specialization",
        "titolo dell'annuncio (jobad) (strutturato)": "Structured Job Ad Title",
        "note sulla vacancy": "Vacancy Notes",
        "quante persone da assumere?": "Number of People to Hire",
        "core profile del candidato": "Candidate Core Profile",
        "laurea (strutturato)": "Structured Degree",
        "topic della laurea": "Degree Topic",
        "il gruppo job type del candidato": "Candidate Job Type Group",
        "hard skills del candidato": "Candidate Hard Skills",
        "settore": "Industry",
        "job type del candidato": "Candidate Job Type",
        "lingue richieste": "Languages Required",
        "luogo di lavoro (strutturato)": "Structured Workplace",
        "soft skills del candidato": "Candidate Soft Skills",
        "la vacancy è stata aperta il": "Vacancy Opened On",
    };

    // Definizione della mappatura per i campi candidato
    const CANDIDATE_KEYS_ITALIAN_TO_ENGLISH = {
        "il candidato potrebbe aver maturato i seguenti nuovi job title": "New Job Titles",
        "il candidato potrebbe aver maturato le seguenti nuove skills": "New Skills",
        "domicilio del candidato": "Candidate Residence",
        "orario lavoro": "Working Hours",
        "titolo del lavoro": "Job Title",
        "settore": "Industry",
        "settore del lavoro": "Job Sector",
        "nome del core profile": "Core Profile Name",
        "hard skills del candidato": "Candidate Hard Skills",
        "soft skills del candidato": "Candidate Soft Skills",
        "lingue conosciute": "Languages Known",
        "disponibile a viaggiare?": "Willing to Travel?",
        "disponibile a viaggiare all'estero?": "Willing to Travel Abroad?",
        "patente": "Driver's License",
        "data di nascita": "Birth Date",
        "luogo di nascita": "Birth Place",
        "il candidato è creato il": "Candidate Created On",
        "il candidato è qualificabile?": "Candidate Employability",
        "curriculum vitae": "Curriculum Vitae",
    };


    // Funzione per recuperare la vacancy assegnata in base all'offset
    const fetchUserVacancy = async (offset) => {
        setIsLoading(true);
        try {
            const response = await fetch(`/db/user_vacancy?offset=${offset}`, {
                headers: {
                    Authorization: `Bearer ${token}`,
                },
            });
            if (!response.ok) {
                throw new Error("Errore nel recupero della vacancy");
            }
            const data = await response.json();

            // Set form fields based on vacancy data. Both Vacancy ID and Database are read-only.
            setInternalInput(data.vacancy.id);
            setSelectedDatabase(data.vacancy.database);
            setTotalVacancies(data.total);
            setVacancyIndex(data.offset);

            return data;
        } catch (error) {
            console.error("Errore:", error.message);
            onResponse("Errore nel recupero della vacancy", error.message);
            return null;
        } finally {
            setIsLoading(false);
        }
    };

    const loadVacancyAndShow = async (offset) => {
        // Function to load a vacancy and then perform a search query based on the fetched vacancy.

        setCandidatesData(null);
        setVacancyData(null);

        const data = await fetchUserVacancy(offset);
        if (data) {

            // Now that we have the correct vacancy ID and database from the API response, 
            // call sm_search_query with the real values.
            sm_search_query({
                selectedDatabase: data.vacancy.database,
                vacancyId: data.vacancy.id,
            });
        }
    };

    const handlePreviousVacancy = () => {
        if (vacancyIndex > 0) {
            loadVacancyAndShow(vacancyIndex - 1);
        }
    };

    const handleNextVacancy = () => {
        if (vacancyIndex < totalVacancies - 1) {
            loadVacancyAndShow(vacancyIndex + 1);
        }
    };

    // Al primo render, carica la vacancy con offset 0
    useEffect(() => {
        loadVacancyAndShow(0);
    }, []);


    // Le funzioni sm_search_query e sm_search_documents rimangono invariate,
    // utilizzando internalInput e selectedDatabase che ora vengono aggiornati automaticamente
    const sm_search_query = async ({ selectedDatabase, vacancyId }) => {

        setIsLoading(true);

        try {

            const apiEndpoint = `/api/sm_search_query`;
            const formData = new FormData();
            formData.append("selectedDatabase", selectedDatabase);
            formData.append("vacancyId", vacancyId);
            formData.append('with_payload', true);
            formData.append('with_vectors', false);

            let response = await fetch(apiEndpoint, {
                method: 'POST',
                body: formData,
            });

            const data = await response.json();
            setVacancyData(data)

        } catch (error) {
            console.error("Error during API call", error.message);
            onResponse("Error during API call", error.message);
        } finally {
            setIsLoading(false);
        }
    };


    // Questa è la sm_search_documents che viene chiamata quando clicchi su "Search Candidates"
    const sm_search_documents = async () => {
        setIsLoading(true);

        try {
            const apiEndpoint = `/api/sm_search_documents`;

            const formData = new FormData();
            formData.append("selectedDatabase", selectedDatabase);
            formData.append("vacancyId", internalInput);
            formData.append("useGeoFilter", false);
            formData.append("radius", 0);
            formData.append("limit", limit);
            formData.append("offset", offset);

            let response = await fetch(apiEndpoint, {
                method: 'POST',
                body: formData,
            });

            const data = await response.json();
            setCandidatesData(data.candidates);
            onResponse(data);

        } catch (error) {
            console.error("Error during API call", error.message);
            onResponse("Error during API call", error.message);
        } finally {
            setIsLoading(false);
        }
    };

    return (
        <div className="functionality">

            <form
                onSubmit={(e) => {
                    e.preventDefault();
                    sm_search_documents();
                }}
            >
                <div className="smart-matching-form">

                    {/* Vacancy ID Input (read-only) */}
                    <div className="form-group">
                        <label>Vacancy ID</label>
                        <input
                            type="number"
                            value={internalInput}
                            readOnly
                        />
                    </div>

                    {/* Database Field (now read-only, no longer selectable) */}
                    <div className="form-group">
                        <label>Database</label>
                        <input
                            type="text"
                            value={selectedDatabase}
                            readOnly
                        />
                    </div>
                </div>

                <div className="vacancy-navigation">
                    {/* Pulsante Previous */}
                    <button
                        type="button"
                        className="nav-button"
                        onClick={handlePreviousVacancy}
                        disabled={isLoading || vacancyIndex === 0}
                    >
                        &laquo; Previous
                    </button>

                    {/* Contenitore centrale con info e progress bar */}
                    <div className="progress-container">
                        <span>
                            Vacancy {vacancyIndex + 1} of {totalVacancies}
                        </span>
                        <progress
                            className="progress-bar"
                            value={vacancyIndex + 1}
                            max={totalVacancies}
                        />
                    </div>

                    {/* Pulsante Next */}
                    <button
                        type="button"
                        className="nav-button"
                        onClick={handleNextVacancy}
                        disabled={isLoading || vacancyIndex === totalVacancies - 1}
                    >
                        Next &raquo;
                    </button>
                </div>

                {vacancyData && (
                    <div className="vacancy-section">
                        <h3>Vacancy Details</h3>
                        <table className="vacancy-table">
                            <tbody>
                                {(() => {
                                    // Primo livello di parsing
                                    const parsedText = JSON.parse(vacancyData["vacancy_payload"].text || "{}");
                                    // Secondo livello di parsing: otteniamo il JSON contenente i dettagli
                                    const textVacancy = parsedText.TEXT_VACANCY || "{}";
                                    const vacancyDetails = JSON.parse(textVacancy);

                                    return Object.entries(vacancyDetails).map(([key, value]) => {
                                        // Normalizziamo la chiave in minuscolo per la ricerca (puoi anche fare altre normalizzazioni se serve)
                                        const lookupKey = key.toLowerCase();
                                        const englishKey = VACANCY_KEYS_ITALIAN_TO_ENGLISH[lookupKey] || key;
                                        return (
                                            <tr key={key}>
                                                <td className="key-column"><strong>{englishKey}:</strong></td>
                                                <td className="value-column">{value}</td>
                                            </tr>
                                        );
                                    });
                                })()}
                            </tbody>
                        </table>
                    </div>
                )}



                {/* Bottone per cercare i candidati */}
                <button
                    type="submit"
                    className="submit-button"
                    disabled={isLoading}
                >
                    {isLoading ? 'Waiting...' : <>
                        <FaSearch /> Search Candidates
                    </>}
                </button>
            </form >

            {/* Mostra il valore di externalResponse */}
            {externalResponse && externalResponse.candidates && externalResponse.candidates.length > 0 && (
                <div className="response-container">
                    {/* Mostra la tabella dei candidati solo se expandedRow è null */}
                    {expandedRow === null ? (
                        <>
                            <table className="response-table">
                                <thead>
                                    <tr>
                                        <th>DATABASE</th>
                                        <th>EMPLOYEE ID</th>
                                        <th>TEXT EMPLOYEE</th>
                                        <th>LAT</th>
                                        <th>LON</th>
                                        <th>Actions</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    {externalResponse.candidates.map((candidate, index) => {
                                        const parsedText = JSON.parse(candidate.text || "{}");
                                        const database = parsedText.DATABASE || "N/A";
                                        const employeeId = parsedText.EMPLOYEE_ID || "N/A";
                                        const textEmployee = parsedText.TEXT_EMPLOYEE || "N/A";
                                        const lat = parsedText.lat || "N/A";
                                        const lon = parsedText.lon || "N/A";

                                        // Recupera il link dal dizionario
                                        const baseLink = databaseLinks[database];
                                        const link = `${baseLink}${employeeId}`;

                                        return (
                                            <tr key={index}>
                                                <td>{database}</td>
                                                <td>{employeeId}</td>
                                                <td className="text-employee">{textEmployee}</td>
                                                <td>{lat}</td>
                                                <td>{lon}</td>
                                                <td>
                                                    <button
                                                        type="button"
                                                        className="icon-button"
                                                        onClick={() => setExpandedRow(index)}
                                                        title="View Details"
                                                    >
                                                        <FaInfoCircle /> {/* Icona per visualizzare */}
                                                    </button>

                                                    {/* Bottone per aprire il link esternamente */}
                                                    <button
                                                        type="button"
                                                        className="icon-button"
                                                        onClick={() => window.open(link, 'external-link', 'noopener,noreferrer')}
                                                        title="Open External Link"
                                                    >
                                                        <FaExternalLinkAlt /> {/* Icona per link esterno */}
                                                    </button>
                                                </td>
                                            </tr>
                                        );
                                    })}
                                </tbody>
                            </table>

                            {/* Controlli per la paginazione */}
                            <div className="pagination-controls">
                                <div className="pagination-input">
                                    <label>Limit per page:</label>
                                    <input
                                        type="number"
                                        value={limit}
                                        onChange={(e) => setLimit(Number(e.target.value))}
                                    />
                                </div>
                                <div className="pagination-input">
                                    <label>Page number:</label>
                                    <input
                                        type="number"
                                        value={offset}
                                        onChange={(e) => setOffset(Number(e.target.value))}
                                    />
                                </div>
                                <button
                                    type="button"
                                    className="update-button"
                                    onClick={(e) => {
                                        e.preventDefault();
                                        sm_search_documents();
                                    }}
                                >
                                    <FaSyncAlt /> Update results
                                </button>
                            </div>
                        </>
                    ) : (
                        <>
                            <div className='back-button'><button
                                type="button"
                                className="close-button"
                                onClick={() => setExpandedRow(null)}
                                style={{
                                    border: 'none',
                                    cursor: 'pointer',
                                }}
                            >
                                <TbArrowBackUp /> Back to Candidates
                            </button>
                            </div>
                            <div className="candidate-details">
                                <h3>Candidate Details</h3>
                                <table className="vacancy-table">
                                    <tbody>
                                        {Object.entries(
                                            JSON.parse(
                                                JSON.parse(externalResponse.candidates[expandedRow].text).TEXT_EMPLOYEE
                                            ) || {}
                                        )
                                            .map(([key, value]) => {
                                                // Normalizza la chiave per cercarla nella mappa: minuscola e senza spazi in eccesso
                                                const lookupKey = key.toLowerCase().trim();
                                                // Se troviamo una traduzione, la usiamo; altrimenti usiamo la chiave originale
                                                const englishKey = CANDIDATE_KEYS_ITALIAN_TO_ENGLISH[lookupKey] || key;
                                                return (
                                                    <tr key={key}>
                                                        <td className="key-column"><strong>{englishKey}:</strong></td>
                                                        <td className="value-column">{value}</td>
                                                    </tr>
                                                );
                                            })}
                                    </tbody>
                                </table>
                            </div>

                        </>
                    )}


                </div>
            )}
        </div >
    );

};

export default SmartMatchingTest;