
docker run -d \
    --rm \
    --runtime nvidia \
    --gpus ${GPU_VLLM} \
    -v "${VLLM_CACHE}:${VLLM_CACHE}" \
    -p "${VLLM_PORT}:${VLLM_PORT}" \
    -e HF_HUB_DOWNLOAD_TIMEOUT=120 \
    --env "HUGGING_FACE_HUB_TOKEN=${HUGGINGFACE_TOKEN}" \
    --name "${CONTAINER_VLLM}" \
    vllm/vllm-openai:v0.9.full \
    --model "${VLLM_DISTRIBUTION}" \
    --port "${VLLM_PORT}" \
    --gpu_memory_utilization 0.9 \
    --enable-prefix-caching \
    --max_model_len 16360 \
    --tensor-parallel-size 1 \
    --api-key token-abc123 \
    --download-dir "${VLLM_CACHE}" \
    --guided-decoding-backend xgrammar \
    --enable-auto-tool-choice \
    --tool-call-parser llama3_json \
    --chat-template examples/tool_chat_template_llama3.1_json.jinja \
    --trust-remote-code