import { useState } from "react";
import axios from "axios";
import { useAuth } from "./AuthContext";
import { useNavigate } from "react-router-dom";

export default function Login() {
    const [username, setUsername] = useState("");
    const [password, setPassword] = useState("");
    const { setToken, setUserId, setUser, setRole, setCountry, setBrand } = useAuth();
    const navigate = useNavigate();

    const handleLogin = async () => {
        const form = new URLSearchParams();
        form.append("username", username);
        form.append("password", password);

        try {
            const res = await axios.post("/auth/token", form, {
                headers: { "Content-Type": "application/x-www-form-urlencoded" },
            });
            setToken(res.data.access_token);
            setUserId(res.data.user_id)
            setUser(res.data.username)
            setRole(res.data.role);
            setCountry(res.data.country)
            setBrand(res.data.brand)

            navigate("/"); // dopo il login redirige alla home (Playground)
        } catch (err) {
            alert("Login failed");
        }
    };

    return (
        <div style={{ padding: "2rem" }}>
            <h2>Login</h2>
            <input
                placeholder="Username"
                onChange={(e) => setUsername(e.target.value)}
            />
            <br />
            <input
                type="password"
                placeholder="Password"
                onChange={(e) => setPassword(e.target.value)}
            />
            <br />
            <button onClick={handleLogin}>Login</button>
        </div>
    );
}
