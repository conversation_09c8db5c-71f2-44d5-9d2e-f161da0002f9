from __future__ import annotations

import argparse
import asyncio
import importlib.util
import json
import shutil
import time
from datetime import datetime
from pathlib import Path

import httpx
import polars as pl
import yaml
from cooper.api.llm.benchmark.collect_env import get_pretty_env_info
from cooper.api.llm.benchmark.json_utils import clean_json_keys, safe_json_loads

# Adjust the import paths according to your project
from cooper.utils import get_logger, parse_yaml

# Logging configuration
logger = get_logger(__name__)


def load_template_module_from_path(path: str):
    """
    Dynamically loads a Python module from a given file system path.
    """
    spec = importlib.util.spec_from_file_location("template_module", path)
    template_module = importlib.util.module_from_spec(spec)
    spec.loader.exec_module(template_module)
    return template_module


async def make_generate_request(
    request_id: int,
    chat_history: list | None,
    document_text: str,
    document_id: str,
    model_name: str,
    load_balancer_url: str,
    system_content: str,
    use_stream: bool,
    extra_body: dict | None,
    response_format: dict | None,
    results_dir: Path,
):
    """
    Executes a single generation request to the server.
    """
    start_time = time.perf_counter()

    try:
        messages = [
            {"role": "system", "content": system_content},
            *chat_history,
            {"role": "user", "content": document_text},
        ]

        # Payload for the request
        payload = {
            "model": model_name,
            "messages": messages,
            "stream": use_stream,
            "extra_body": extra_body,
            "response_format": response_format,
        }

        # Make the request to the load balancer
        async with httpx.AsyncClient() as client:
            response = await client.post(
                load_balancer_url,
                json=payload,
                timeout=None,  # Disable timeout for long requests
            )

            if response.status_code == 200:
                response_data = response.json()
                generated_text = response_data["text"]
            else:
                msg = f"Error in response: {response.status_code} - {response.text}"
                raise Exception(msg)

    except Exception as e:
        logger.error("Error in request %s: %s", request_id, e)
        return float("inf"), str(e)

    # Attempt to parse the returned text as JSON
    if extra_body or response_format:
        try:
            generated_text = safe_json_loads(clean_json_keys(generated_text))
        except Exception as e:
            logger.error("Error in JSON parsing: %s", e)
            return float("inf"), str(e)

    end_time = time.perf_counter()
    elapsed_time = end_time - start_time

    # Save the complete response in a JSON file
    response_filename = results_dir / f"response_{request_id}.json"
    with open(response_filename, "w", encoding="utf-8") as f:
        json.dump(
            {
                "request_id": request_id,
                "document_id": document_id,
                "response": generated_text,
                "elapsed_time": elapsed_time,
            },
            f,
            indent=4,
            ensure_ascii=False,
        )

    return elapsed_time, generated_text, request_id


async def simulate_requests(
    df_documents: pl.DataFrame,
    model_name: str,
    load_balancer_url: str,
    use_stream: bool,
    system_content: str,
    extra_body: dict,
    response_format: dict,
    results_dir: Path,
    batch_size: int,
):
    """
    Simulates multiple generation requests in parallel.
    """
    n_requests = len(df_documents)
    logger.info("Starting %s requests with batch_size=%s", n_requests, batch_size)

    start_time = time.perf_counter()  # Overall start time
    responses = []
    chat_history = []

    for i in range(0, n_requests, batch_size):
        tasks = []
        # Prepare requests for the current batch
        for j in range(i, min(i + batch_size, n_requests)):
            document_text = df_documents["content"][j]
            if type(document_text) != str and len(document_text) > 1:
                chat_history = document_text[:-1].to_list()
                document_text = document_text[-1]["content"]
            document_id = df_documents["full_id"][j]

            tasks.append(
                make_generate_request(
                    request_id=j,
                    chat_history=chat_history,
                    document_text=document_text,
                    document_id=document_id,
                    model_name=model_name,
                    load_balancer_url=load_balancer_url,
                    use_stream=use_stream,
                    system_content=system_content,
                    extra_body=extra_body,
                    response_format=response_format,
                    results_dir=results_dir,
                )
            )

        batch_responses = await asyncio.gather(*tasks)
        responses.extend(batch_responses)

    end_time = time.perf_counter()
    total_time = end_time - start_time

    # Extract request times (excluding any that had float("inf"))
    requests_time = [resp[0] for resp in responses if resp and resp[0] != float("inf")]

    # Calculate average time
    avg_time = sum(requests_time) / len(requests_time) if requests_time else 0

    return {
        "n_requests": n_requests,
        "batch_size": batch_size,
        "total_time": total_time,
        "avg_time_per_batch": avg_time,
        "n_requests_executed": len(requests_time),
        "time_per_request": total_time / n_requests if n_requests else 0,
        "requests_time": requests_time,
    }


def run(config_file: str, repeats: int):
    """
    Main function that reads configuration, loads data, and executes requests.
    """
    logger.info("Loading configuration from: %s", config_file)
    parameters = parse_yaml(config_file)

    # Read the config_engine_llm parameters
    with Path(parameters["config_engine_llm"]).open() as f:
        parameters_engine_llm = yaml.safe_load(f)
    model_name = parameters_engine_llm["model_name"]
    engine = parameters_engine_llm["engine"]

    batch_size = parameters["batch_size"]
    use_stream = parameters["use_stream"]
    load_balancer_url = parameters["load_balancer_url"]
    text_columns = parameters["text_columns"]
    id_columns = parameters["id_columns"]  # Example: ["DATABASE", "EMPLOYEE_ID"]
    n_rows = parameters["n_rows"]

    template_module_path = parameters["template_module_path"]
    template_module = load_template_module_from_path(template_module_path)

    # Prepara system prompt e schema da template
    system_content = template_module.get_default_system_prompt()

    # Parametri base dal file di config
    document_input_file = Path(parameters["document_input_file"])
    guided_decoding_backend = parameters["guided_decoding_backend"]

    end_path = "results"
    extra_body = None
    response_format = None

    if engine == "sglang" and guided_decoding_backend:
        end_path = end_path + f"_struct_{guided_decoding_backend}"
        response_format = {
            "type": "json_schema",
            "json_schema": {"name": "foo", "schema": template_module.get_schema()},
        }
    elif engine == "vllm" and guided_decoding_backend:
        end_path = end_path + f"_struct_{guided_decoding_backend}"
        extra_body = {
            "guided_decoding_backend": guided_decoding_backend,
            "guided_json": template_module.get_schema(),
        }

    # Load the document DataFrame
    logger.info("Loading documents from: %s", document_input_file)
    if n_rows:
        df_documents = pl.scan_parquet(document_input_file).head(n_rows).collect()
    else:
        df_documents = pl.read_parquet(document_input_file)

    # Estraggo le colonne dal YAML (con fallback a None per sicurezza)
    role_column = parameters.get("role_column", None)
    order_column = parameters.get("order_column", None)

    if len(text_columns) > 1:
        # Mantiene i nomi e converte in stringa JSON
        df_documents = df_documents.with_columns(
            pl.struct(text_columns)  # Crea la struct con le colonne
            .map_elements(lambda s: json.dumps({col: s[col] for col in text_columns}))
            .alias("content")
        )
    else:
        df_documents = df_documents.rename({text_columns[0]: "content"})

    if role_column:
        df_documents = (
            df_documents.sort([*id_columns, order_column])
            .rename({role_column: "role"})
            .group_by(id_columns)
            .agg([pl.struct(["role", "content"]).alias("content")])
        )

    df_documents = df_documents.with_columns(
        pl.concat_str(id_columns, separator="_").alias("full_id")
    )

    # Run asynchronous requests
    for i in range(repeats):
        if parameters["is_benchmark"]:
            timestamp = datetime.now().strftime("%Y-%m-%d_%H-%M-%S")
            results_dir = ".benchmark/" + timestamp + "/" + end_path
        results_dir = Path(parameters["results_dir"]) / results_dir
        results_dir.mkdir(parents=True, exist_ok=True)

        logger.info("Execution %s/%s", i + 1, repeats)
        logger.info("Starting asynchronous request simulation")
        result = asyncio.run(
            simulate_requests(
                df_documents=df_documents,
                model_name=model_name,
                load_balancer_url=load_balancer_url,
                use_stream=use_stream,
                system_content=system_content,
                extra_body=extra_body,
                response_format=response_format,
                results_dir=results_dir,
                batch_size=batch_size,
            )
        )

        logger.info("Simulation finished. Saving summary.")
        logger.info(result)

        # Save the summary results
        summary_filename = (
            results_dir.parent / f".summary_struct_{guided_decoding_backend}.json"
        )
        with Path(summary_filename).open("w", encoding="utf-8") as f:
            json.dump(result, f, indent=4, ensure_ascii=False)

        # Copia il file di configurazione originale nella cartella dei risultati
        if parameters["is_benchmark"]:
            # Save config file
            filename = results_dir.parent / "config.yml"
            shutil.copy(config_file, filename)
            logger.info("Configuration file saved to: %s", filename)

            # Save config_engine_llm
            filename = results_dir.parent / "config_engine_llm.yml"
            shutil.copy(parameters["config_engine_llm"], filename)
            logger.info("Configuration engine llm file saved to: %s", filename)

            # Save server log information
            pathname = results_dir.parent / "logs"
            shutil.copytree(Path(parameters["server_log"]), pathname)
            logger.info("Log directory copied to %s", pathname)

            # Save environment information
            logger.info("Collecting environment information...")
            output = get_pretty_env_info()
            filename = results_dir.parent / "env_info.txt"
            with filename.open("w", encoding="utf-8") as f:
                f.write(output)
            logger.info("Environment info saved to %s", filename)

        logger.info("Finished execution %s/%s", i + 1, repeats)


def main():
    """
    Parses command line arguments and runs the script.
    """

    ## CV_PARSER
    # python /home/<USER>/workarea/ai_playground/backend/cooper/api/llm/benchmark/document_parser.py \
    # --config /home/<USER>/workarea/ai_playground/backend/cooper/api/llm/benchmark/assets/cv_parser/config.yml
    # --repeats 3

    ## EVALUATION_CV_PARSER_QWQ
    # python /home/<USER>/workarea/ai_playground/backend/cooper/api/llm/benchmark/document_parser.py \
    # --config /home/<USER>/workarea/ai_playground/backend/cooper/api/llm/benchmark/assets/evaluation_cv_parser_qwq/config.yml \
    # --repeats 3

    ## EVALUATION_CV_PARSER_TK
    # python /home/<USER>/workarea/ai_playground/backend/cooper/api/llm/benchmark/document_parser.py \
    # --config /home/<USER>/workarea/ai_playground/backend/cooper/api/llm/benchmark/assets/evaluation_cv_parser_tk/config.yml \
    # --repeats 3

    ## EVALUATION_CV_WORK_EXPERIENCES_PARSER_GPT35TURBO
    # python /home/<USER>/workarea/ai_playground/backend/cooper/api/llm/benchmark/document_parser.py \
    # --config /home/<USER>/workarea/ai_playground/backend/cooper/api/llm/benchmark/assets/evaluation_cv_work_experiences_parser_gpt35turbo/config.yml \
    # --repeats 3

    ## EVALUATION_CV_WORK_EXPERIENCES_PARSER_QWQ
    # python /home/<USER>/workarea/ai_playground/backend/cooper/api/llm/benchmark/document_parser.py \
    # --config /home/<USER>/workarea/ai_playground/backend/cooper/api/llm/benchmark/assets/evaluation_cv_work_experiences_parser_qwq/config.yml \
    # --repeats 3

    ## EVALUATION_CV_WORK_EXPERIENCES_PARSER_TK
    # python /home/<USER>/workarea/ai_playground/backend/cooper/api/llm/benchmark/document_parser.py \
    # --config /home/<USER>/workarea/ai_playground/backend/cooper/api/llm/benchmark/assets/evaluation_cv_work_experiences_parser_tk/config.yml \
    # --repeats 3

    ## NEXT_JOB_RECOMMENDER
    # python /home/<USER>/workarea/ai_playground/backend/cooper/api/llm/benchmark/document_parser.py \
    # --config /home/<USER>/workarea/ai_playground/backend/cooper/api/llm/benchmark/assets/next_job_recommender/config.yml \
    # --repeats 3

    ## WORK_EXPERIENCES
    # python /home/<USER>/workarea/ai_playground/backend/cooper/api/llm/benchmark/document_parser.py \
    # --config /home/<USER>/workarea/ai_playground/backend/cooper/api/llm/benchmark/assets/work_experiences/config.yml \
    # --repeats 3

    parser = argparse.ArgumentParser(
        description="Launch Document Parser with configuration file."
    )
    parser.add_argument(
        "--config",
        type=str,
        default="/home/<USER>/workarea/ai_playground/backend/cooper/api/llm/benchmark/assets/next_job_recommender/config.yml",
        help="Path to the configuration file.",
    )
    parser.add_argument(
        "--repeats",
        type=int,
        default=1,  # Default: esegue una sola volta
        help="Number of times to repeat the experiment.",
    )
    args = parser.parse_args()

    run(config_file=args.config, repeats=args.repeats)


if __name__ == "__main__":
    main()
