#!/bin/bash

# fail fast on any error, treat unset variables as errors, and
#catch failures in all stages of pipelines for safer, more predictable scripts
set -euo pipefail

# --- PARSING ARGOMENTI ---
CONFIG_PATH=""

while [[ $# -gt 0 ]]; do
    case "$1" in
    --config_engine_llm)
        CONFIG_PATH="$2"
        shift 2
        ;;
    *)
        echo "Unknown argument: $1"
        echo "Usage: $0 --config_engine_llm /path/to/config_engine_llm.yml"
        exit 1
        ;;
    esac
done

# --- VALIDAZIONE ARGOMENTI ---
if [ -z "$CONFIG_PATH" ]; then
    echo "Usage: $0 --config_engine_llm /path/to/config_engine_llm.yml"
    exit 1
fi

if [ ! -f "$CONFIG_PATH" ]; then
    echo "Error: Configuration file '$CONFIG_PATH' not found."
    exit 1
fi

# Calcola il percorso base (parent directory del file YAML)
BASE_PATH=$(dirname "$CONFIG_PATH")

# --- LEGGE engine, model_name e ports_gpus dal YAML ---
engine=$(python3 -c "import yaml; data=yaml.safe_load(open('$CONFIG_PATH')); print(data.get('engine',''))")

if [[ "$engine" != "vllm" && "$engine" != "sglang" ]]; then
    echo "Error: 'engine' in config must be either 'vllm' or 'sglang'. Found: '$engine'"
    exit 1
fi

# Leggi model_name
model_name=$(python3 -c "import yaml; data=yaml.safe_load(open('$CONFIG_PATH')); print(data['model_name'])")

# Popola l'array associativo per le porte e GPU
declare -A ports_gpus
while IFS='=' read -r key value; do
    ports_gpus[$key]=$value
done < <(python3 -c "
import yaml
import sys
data = yaml.safe_load(sys.stdin.read())['ports_gpus']
print('\n'.join([f'{k}={v}' for k, v in data.items()]))
" <"$CONFIG_PATH")

# File per salvare i PID dei processi
PID_FILE="$BASE_PATH/vllm_pids.txt"
echo "" >"$PID_FILE" # Pulisce il file prima di iniziare

# Crea la directory per i log se non esiste
LOG_DIR="$BASE_PATH/.log"
mkdir -p "$LOG_DIR"

echo "Using engine: $engine"
echo "Using configuration: $CONFIG_PATH"
echo "Base path: $BASE_PATH"
echo "Model: $model_name"
echo ""

# Assicuriamoci che VLLM_CACHE sia sempre definito, con fallback alla cartella
# definita, così da evitare errori di "unbound variable"
VLLM_CACHE=${VLLM_CACHE:-"/data/datascience/.shared/.transformers_cache"}
mkdir -p "$VLLM_CACHE"
export VLLM_CACHE

# --- AVVIA I SERVER ---

if [ "$engine" == "vllm" ]; then
    # Important note:
    # By default, vLLM internally reads the "VLLM_PORT" environment variable to manage internal communication ports.
    # Since we are setting the port explicitly with "--port", but we also define a "VLLM_PORT" for other components in our .env file,
    # this was causing a conflict: vLLM was picking up "VLLM_PORT" from the environment instead of using "--port",
    # leading to port collisions and runtime errors like "address already in use".
    #
    # To avoid this conflict, we "unset" VLLM_PORT **only for this command** (without affecting other parts of the script or environment).
    # This ensures vLLM only uses the port we specify via "--port", without interference from environment variables.
    unset VLLM_PORT

    # Use vLLM v1 engine
    # ⚠️ Limitation: vLLM (v1) Incompatibility with xGrammar
    # As of now, vLLM v1.x (tested up to version vllm==0.7.3) is not compatible with xgrammar==0.1.11.
    # This issue occurs when trying to use xGrammar in combination with vLLM to enforce structured outputs.
    # During testing, the system returns empty or unstructured outputs, even when a grammar is properly provided.
    export VLLM_USE_V1=0
fi

# Launch servers on each port specified in the YAML file
for port in "${!ports_gpus[@]}"; do
    gpu_id=${ports_gpus[$port]}
    echo "Launching $engine server on port $port (GPU $gpu_id) with model $model_name"

    if [ "$engine" == "vllm" ]; then
        # Start the server in background
        CUDA_VISIBLE_DEVICES=$gpu_id nohup vllm serve "$model_name" \
            --port $port \
            --gpu-memory-utilization 0.95 \
            --enable-prefix-caching \
            --disable-log-requests \
            --api-key token-abc123 \
            --max_model_len 6144 \
            --download-dir "${VLLM_CACHE}" \
            --guided-decoding-backend xgrammar \
            >"$LOG_DIR/server_$port.log" 2>&1 &

    elif [ "$engine" == "sglang" ]; then
        # Comando per sglang (vedi: https://docs.sglang.ai/backend/server_arguments.html)
        CUDA_VISIBLE_DEVICES=$gpu_id nohup python -m sglang.launch_server \
            --port $port \
            --model-path "$model_name" \
            --context-length 6144 \
            --mem-fraction-static 0.3 \
            --max-running-requests 256 \
            --download-dir "${VLLM_CACHE}" \
            --trust-remote-code \
            >"$LOG_DIR/server_$port.log" 2>&1 &
    fi

    # Save correct PID
    echo $! >>"$PID_FILE"
done

echo "✅ All $engine servers started! PIDs written to $PID_FILE"
