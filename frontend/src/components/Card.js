import React, { useState, useRef } from 'react';
import { FaRegStar, FaStar, FaArrowRight, FaFolder } from 'react-icons/fa';
import AudioRecorder from './AudioRecorder';

const Card = ({
    card,
    onExpand,
    isExpanded,
    onFileUpload,
    onResponse,
    resetResponse,
    handleSubmit,
    files,
    onExternalInput,
    toggleFavorite,
    isMicActive,
    setIsMicActive,
}) => {
    const [input, setInput] = useState('');
    const [isLoading, setIsLoading] = useState(false);
    const fileInputRef = useRef(null);
    const [isRecording, setIsRecording] = useState(false);

    const submit = async () => {
        onExpand(card.id);
        onExternalInput(input);
        setInput('');
    };

    const handleFileUpload = (event) => {
        onExpand(card.id);
        onFileUpload(event);
    };

    const handleMicTranscript = (transcript) => {
        onExpand(card.id);
        onExternalInput(transcript);
    };

    const onCardClick = () => {
        // open card if just click, but not call any API!
        onExpand(card.id);
    };

    const handleFavoriteClick = (e) => {
        e.stopPropagation(); // Previene il triggering dell'onClick del card container
        toggleFavorite(card.id);
    };

    const submitButton = (
        <button onClick={(e) => {
            e.stopPropagation();
            submit();
        }}
            disabled={isLoading}
        >
            {isLoading ? 'Waiting...' : <FaArrowRight />}
        </button>
    );

    const handleDragOver = (e) => {
        if (!isLoading) {
            e.preventDefault();
            e.stopPropagation();
            e.currentTarget.classList.add('drag-over');
        }
    };

    const handleDragLeave = (e) => {
        if (!isLoading) {
            e.preventDefault();
            e.stopPropagation();
            e.currentTarget.classList.remove('drag-over');
        }
    };

    const handleDrop = (e) => {
        if (!isLoading) {
            e.preventDefault();
            e.stopPropagation();
            e.currentTarget.classList.remove('drag-over');
            handleFileUpload(e);
        }
    };

    return (
        <div
            className={`card ${isExpanded ? 'hidden' : ''}`}
            style={{ backgroundColor: card.color }}
            onClick={onCardClick}
        >
            <div className="card-content">
                <div className="card-header">
                    <div className="title-group">
                        {card.icon && <div style={{ fontSize: '1.5em' }}>{card.icon}</div>}
                        <h3>{card.title}</h3>
                    </div>
                    <div className="card-actions">
                        <div
                            onClick={handleFavoriteClick}
                            style={{ cursor: 'pointer' }}
                        >
                            {card.isFavorite ? (
                                <FaStar
                                    style={{
                                        color: 'yellow',
                                        fontSize: '1.5em',
                                        // stroke: 'black',
                                        // strokeWidth: 15,
                                    }}
                                />
                            ) : (
                                <FaRegStar
                                    style={{
                                        fontSize: '1.4em',
                                    }}
                                />
                            )}
                        </div>
                    </div>
                </div>
                <p>{card.description}</p>
            </div>

            {(card.hasTextInput || card.hasUpload) && (
                <div className="input-container">
                    {card.hasTextInput ? (
                        <div
                            className={`input-area ${card.hasUpload ? 'border-upload' : ''}`}
                            onClick={(e) => e.stopPropagation()}
                            onDragOver={card.hasUpload ? handleDragOver : undefined}
                            onDragLeave={card.hasUpload ? handleDragLeave : undefined}
                            onDrop={card.hasUpload ? handleDrop : undefined}
                        >
                            {card.hasUpload && (
                                <input
                                    type="file"
                                    id={`file-upload-${card.id}`}
                                    accept=".pdf,.doc,.docx"
                                    onChange={handleFileUpload}
                                    style={{ display: 'none' }}
                                    ref={fileInputRef}
                                    disabled={isLoading}
                                />
                            )}
                            <input
                                type="text"
                                value={input}
                                onChange={(e) => setInput(e.target.value)}
                                placeholder={
                                    card.hasUpload
                                        ? 'Start writing here or drag a file...'
                                        : 'Start writing here...'
                                }
                                onKeyDown={(e) => {
                                    if (e.key === 'Enter') {
                                        e.preventDefault();
                                        submit(true);
                                    }
                                }}
                            />
                            <AudioRecorder
                                onTranscriptReceived={(transcript) => handleMicTranscript(transcript)}
                                isMicActive={isMicActive}
                                setIsMicActive={setIsMicActive}
                                isRecording={isRecording}
                                setIsRecording={setIsRecording}
                                classStyleFaMicrophone="mic-icon"
                            />
                            {submitButton}
                        </div>
                    ) : (
                        card.hasUpload && (
                            <div
                                className="upload-area"
                                onClick={(e) => {
                                    if (!isLoading) {
                                        e.stopPropagation();
                                        fileInputRef.current.click();
                                    }
                                }}
                                onDragOver={handleDragOver}
                                onDragLeave={handleDragLeave}
                                onDrop={handleDrop}
                            >
                                <input
                                    type="file"
                                    id={`file-upload-${card.id}`}
                                    accept=".pdf,.doc,.docx"
                                    onChange={handleFileUpload}
                                    style={{ display: 'none' }}
                                    ref={fileInputRef}
                                    disabled={isLoading}
                                />
                                <div>
                                    <FaFolder /> Upload your document here
                                </div>
                            </div>
                        )
                    )}
                </div>
            )}

        </div>
    );
};

export default Card;