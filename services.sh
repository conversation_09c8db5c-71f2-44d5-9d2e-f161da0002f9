#!/usr/bin/env bash
#
# Usage:
#   chmod +x /home/<USER>/workarea/ai_playground/services.sh
#   ./services.sh start    # Avvia tutti i servizi
#   ./services.sh stop     # Ferma e rimuove i container (se --rm era assente)
#   ./services.sh restart  # Esegue uno stop e poi uno start

########################################
# Esporta automaticamente tutte le variabili
########################################
set -a
if [ -f .env ]; then
    . .env
fi
set +a # Disattiva l'esportazione automatica

########################################
# Parametri personalizzabili
########################################

# GPU(s) da usare (lasciare vuoto se non serve)
GPU_SMART_MATCHING_EMBEDDER="\"device=2\""
GPU_EMBEDDER_RAG="\"device=2\""
GPU_VLLM="\"device=0,1\""
GPU_TRANSCRIPTION="\"device=3\""

# Nome dei container Docker
CONTAINER_RAG_EMBEDDER="${EMBEDDER_RAG_MODEL##*/}"
CONTAINER_VLLM="${VLLM_DISTRIBUTION##*/}"
CONTAINER_WHISPER="${WHISPER_MODEL##*/}"

########################################
# Funzioni interne
########################################

start_services() {
    echo ">>> Avvio dei servizi..."

    # Avvia container Postgres tramite docker-compose
    echo ">>> [Postgres] Avvio container..."
    docker compose -f "${WORKDIR}/.dockerfiles/docker-compose-postgres.yml" up -d

    # Avvia container Qdrant Multimodal RAG
    echo ">>> [Qdrant Multimodal RAG] Avvio del container..."
    docker run -d \
        --rm \
        --name "$CONTAINER_RAG_QDRANT" \
        -p "$MULTIMODAL_RAG_QDRANT_PORT":6333 \
        -v "$MULTIMODAL_RAG_QDRANT":/qdrant/storage \
        qdrant/qdrant:v1.12.1

    # Avvia container Qdrant Smart Matching
    echo ">>> [Qdrant Smart Matching] Avvio del container..."
    docker run -d \
        --rm \
        --name "$CONTAINER_SMART_MATCHING_QDRANT" \
        -v "$SMART_MATCHING_QDRANT":/qdrant/storage \
        -p "$SMART_MATCHING_QDRANT_PORT":6333 \
        qdrant/qdrant:v1.12.1

    # Avvia container VLLM Model
    echo ">>> [VLLM Model] Avvio container..."

    # Define the path of the template specific to the model
    # We use a standard file name like 'run_config.sh' in the template folder
    MODEL_TEMPLATE_DIR="${WORKDIR}/.vllm_model_templates/${VLLM_DISTRIBUTION}"
    MODEL_TEMPLATE_FILE="${MODEL_TEMPLATE_DIR}/run_config.sh"

    if [ -f "${MODEL_TEMPLATE_FILE}" ]; then
        echo ">>> Found specific VLLM configuration template for '${VLLM_DISTRIBUTION}' in ${MODEL_TEMPLATE_FILE}. I'm using it."
        source "${MODEL_TEMPLATE_FILE}"

        # Check if the source command was successful (optional but recommended)
        if [ $? -ne 0 ]; then
            echo "Error: Unable to run the VLLM configuration script from ${MODEL_TEMPLATE_FILE}"
            # exit 1 # Exits in case of an error in the template
        fi
    else
        echo ">>> No specific template found for '${VLLM_DISTRIBUTION}'. I use the default VLLM configuration."

        # Default configuration VLLM
        export VLLM_USE_V1=1

        docker run -d \
            --rm \
            --runtime nvidia \
            --gpus ${GPU_VLLM} \
            -v "${VLLM_CACHE}:${VLLM_CACHE}" \
            -p "${VLLM_PORT}:${VLLM_PORT}" \
            -e HF_HUB_DOWNLOAD_TIMEOUT=120 \
            --env "HUGGING_FACE_HUB_TOKEN=${HUGGINGFACE_TOKEN}" \
            --name "${CONTAINER_VLLM}" \
            vllm/vllm-openai:v0.8.3.full \
            --model "${VLLM_DISTRIBUTION}" \
            --port "${VLLM_PORT}" \
            --gpu_memory_utilization 0.9 \
            --enable-prefix-caching \
            --max_model_len 30720 \
            --tensor-parallel-size 2 \
            --api-key token-abc123 \
            --download-dir "${VLLM_CACHE}" \
            --guided-decoding-backend xgrammar \
            --trust-remote-code
    fi

    # Avvia container VLLM embedder for RAG
    echo ">>> [VLLM Embedder for RAG] Avvio container..."
    docker run -d \
        --rm \
        --gpus ${GPU_EMBEDDER_RAG} \
        -v "${TRANSFORMERS_CACHE}:${TRANSFORMERS_CACHE}" \
        -p "${EMBEDDER_RAG_PORT}:${EMBEDDER_RAG_PORT}" \
        -e HF_HUB_DOWNLOAD_TIMEOUT=120 \
        --env "HUGGING_FACE_HUB_TOKEN=${HUGGINGFACE_TOKEN}" \
        --name "${CONTAINER_RAG_EMBEDDER}" \
        vllm/vllm-openai:latest \
        --model "${EMBEDDER_RAG_MODEL}" \
        --port "${EMBEDDER_RAG_PORT}" \
        --dtype float32 \
        --tensor-parallel-size 1 \
        --max-model-len 512 \
        --task embed \
        --download-dir "${TRANSFORMERS_CACHE}"

    # Avvia container VLLM embedder Smart Matching
    echo ">>> [VLLM Embedder Smart Matching] Avvio container..."
    docker run -d \
        --rm \
        --gpus ${GPU_SMART_MATCHING_EMBEDDER} \
        -v "${EMBEDDER_SMART_MATCHING_MODEL}:${EMBEDDER_SMART_MATCHING_MODEL}" \
        -p "${EMBEDDER_SMART_MATCHING_PORT}:${EMBEDDER_SMART_MATCHING_PORT}" \
        --name "${CONTAINER_SMART_MATCHING_EMBEDDER}" \
        -e HF_HUB_DOWNLOAD_TIMEOUT=120 \
        --env "HUGGING_FACE_HUB_TOKEN=${HUGGINGFACE_TOKEN}" \
        vllm/vllm-openai:v0.9.full \
        --model "${EMBEDDER_SMART_MATCHING_MODEL}" \
        --port "${EMBEDDER_SMART_MATCHING_PORT}" \
        --dtype float32 \
        --tensor-parallel-size 1 \
        --max-model-len 512 \
        --task embed

    # Avvia container VLLM transcription
    echo ">>> [VLLM Transcription] Avvio container..."
    docker run -d \
        --rm \
        --gpus ${GPU_TRANSCRIPTION} \
        -v "${TRANSFORMERS_CACHE}:${TRANSFORMERS_CACHE}" \
        -p "${WHISPER_PORT}:${WHISPER_PORT}" \
        --name "${CONTAINER_WHISPER}" \
        -e HF_HUB_DOWNLOAD_TIMEOUT=120 \
        --env "HUGGING_FACE_HUB_TOKEN=${HUGGINGFACE_TOKEN}" \
        vllm/vllm-openai:v0.8.2.full \
        --model "${WHISPER_MODEL}" \
        --port "${WHISPER_PORT}" \
        --dtype float16 \
        --tensor-parallel-size 1 \
        --task transcription \
        --download-dir "${TRANSFORMERS_CACHE}"

    # Avvia mkdocs in background (non in Docker)
    echo ">>> [MkDocs] Avvio server..."
    tmux new-session -d -s mkdocs_server "mkdocs serve --dev-addr=127.0.0.1:${MKDOCS_PORT}"

    # 1) Build del frontend e avvio del backend Python in background
    echo ">>> [Frontend] Build in corso..."
    npm --prefix frontend run build

    echo ">>> [Backend] Avvio backend cooper..."
    # Esempio di esecuzione "in chiaro" (non in Docker)
    # Se preferisci incapsulare anche questo in Docker, vedi sotto.
    python backend/cooper/app.py --mode production --port "${BACKEND_PORT}"

    echo ">>> Tutti i servizi sono stati avviati."
}

stop_services() {
    echo ">>> Arresto dei servizi..."

    echo ">>> [Postgres] Stop container..."
    docker compose -f "${WORKDIR}/.dockerfiles/docker-compose-postgres.yml" down

    # Se hai avviato il backend e MkDocs fuori da Docker, kill i processi.
    echo ">>> [MkDocs] Stop..."
    tmux kill-session -t mkdocs_server

    # Container Docker da fermare
    echo ">>> [Qdrant Smart Matching] Stop..."
    docker stop "${CONTAINER_SMART_MATCHING_QDRANT}" || true

    echo ">>> [VLLM Embedder Smart Matching] Stop..."
    docker stop "${CONTAINER_SMART_MATCHING_EMBEDDER}" || true

    echo ">>> [Qdrant Multimodal RAG] Stop..."
    docker stop "${CONTAINER_RAG_QDRANT}" || true

    echo ">>> [VLLM Embedder for RAG] Stop..."
    docker stop "${CONTAINER_RAG_EMBEDDER}" || true

    echo ">>> [VLLM Model] Stop..."
    docker stop "${CONTAINER_VLLM}" || true

    echo ">>> [VLLM Transcription] Stop..."
    docker stop "${CONTAINER_WHISPER}" || true

    echo ">>> Tutti i servizi sono stati fermati."
}

case "$1" in
start)
    start_services
    ;;
stop)
    stop_services
    ;;
restart)
    stop_services
    start_services
    ;;
*)
    echo "Usage: $0 {start|stop|restart}"
    exit 1
    ;;
esac
