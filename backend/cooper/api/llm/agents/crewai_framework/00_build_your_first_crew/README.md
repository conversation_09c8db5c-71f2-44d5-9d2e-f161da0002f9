
## Come far partire lo script
Da qui fai partire l'esecuzione (starting point)

/home/<USER>/workarea/ai_playground/backend/cooper/api/llm/agents/crewai_framework/00_build_your_first_crew/main.py

## Definizione agenti e tasks
dentro /home/<USER>/workarea/ai_playground/backend/cooper/api/llm/agents/crewai_framework/00_build_your_first_crew/config definisci comportamenti degli agenti e i task di ciascuno

## Come usare vllm

A quanto pare dietro crewai c'è LiteLLM che fa la richiesta a vllm. Nota che quindi dobbiamo aggiungere al modello la stringa "hosted_vllm/" come mostrato di seguito:

```bash
llm = LLM(
    provider="openai",
    model="hosted_vllm/" + os.getenv("VLLM_DISTRIBUTION"),
    base_url=os.getenv("VLLM_ENDPOINT"),
    api_key=os.getenv("VLLM_API_KEY")
)
```