from __future__ import annotations

import os

from dotenv import load_dotenv
from smolagents import CodeAgent, DuckDuckGoSearchTool, OpenAIServerModel

load_dotenv()

############################################
## 1) Inspecting runs with OpenTelemetry:

## Uncomment to enable OpenTelemetry:
# from openinference.instrumentation.smolagents import SmolagentsInstrumentor
# from phoenix.otel import register
# register()
# SmolagentsInstrumentor().instrument()

## conda activate test_agents && python -m phoenix.server.main serve

# 2) Run this script
# conda activate test_agents && python /home/<USER>/workarea/ai_playground/backend/cooper/api/llm/agents/main.py
############################################

model = OpenAIServerModel(
    model_id=os.getenv("VLLM_DISTRIBUTION"),
    api_base=os.getenv("VLLM_ENDPOINT"),  # Leave this blank to query OpenAI servers.
    api_key=os.getenv("VLLM_API_KEY"),
)
agent = CodeAgent(tools=[DuckDuckGoSearchTool()], model=model)

agent.run(
    # task="Fammi un breve riassunto delle condizioni del papa negli ultimi giorni",
    task="Spiegami i referendum del 7-8 giugno 2025 in italia",
    stream=False,
)
