@import url('https://fonts.googleapis.com/css2?family=Open+Sans:wght@400;600&display=swap');


/* ----------------------------------------------------------- */
body {
    font-family: 'Open Sans', sans-serif;
    margin: 0;
}

p {
    color: rgb(102, 102, 102);
    font-size: 15px;
}

h3 {
    font-size: medium;
}

h1, h2, h3, h4, ul, li {
    color: #666;
}

h1, h2, h3, h4 {
    font-family: ui-sans-serif, -apple-system, system-ui, Segoe UI, Helvetica, Apple Color Emoji, Arial, sans-serif, Segoe UI Emoji, Segoe UI Symbol;
}

h1.typewriter.playground-title {
    color: black;
}

/* ----------------------------------------------------------- */



.playground-header {
    text-align: left;
    font-weight: bold;
    display: flex;
    justify-content: space-between;
    padding: 10px 10px 0;
}

.playground-title {
    margin: 0;
    font-size: xxx-large;
    transition: font-size 0.3s ease-in-out;
}

.playground-title.expanded-title {
    font-size: x-large;
}

.playground-subtitle {
    color: grey;
    font-size: small;
    margin-top: 0;
    transition: font-size 0.3s ease-in-out;
}

.playground-subtitle.expanded-title {
    font-size: xx-small;
}

.playground-menu {
    background-color: rgb(195, 222, 214);
    margin-bottom: 5px;
    padding: 10px;
    display: flex;
    justify-content: space-between;
}

.category-selector {
    display: flex;
    gap: 1rem;
}

.category-button {
    padding: 0.5rem 1rem;
    background-color: #f5f5f5;
    color: #333;
    font-size: 1rem;
    cursor: pointer;
    transition: background-color 0.3s ease, color 0.3s ease;
    border: 0px;
}

.category-button.for-recruiters {
    background-color: #c3ded6;
}

.category-button.for-candidates {
    background-color: #cee741;
}

.category-button.for-clients {
    background-color: #12cdd4;
}

.category-button.all.active {
    background-color: #4CAF50;
}

.category-button[class*="active"] {
    color: white;
}

.category-button:hover:not(.active) {
    background-color: #ddd;
}


.card-container {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
    gap: 10px;
    /* Add vertical spacing */
    width: 100%;
    /* overflow: hidden; */
}

.card {
    padding: 20px;
    transition: transform 0.3s ease, box-shadow 0.3s ease;
    display: flex;
    flex-direction: column;
    height: auto;
    min-height: 200px;
    justify-content: space-between;
}

.card:hover {
    transform: translateY(-5px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
}

.card h3 {
    color: #333;
    font-size: 1.3em;
    margin: 0 0 10px;
}

.card.hidden {
    display: none;
}

.card-header {
    display: flex;
    /* Allinea verticalmente al centro */
    /* Allinea gli elementi ai lati opposti */
    justify-content: space-between;
    align-items: flex-start;
    width: 100%;
}

.title-group {
    display: flex;
    align-items: center;
    gap: 10px;
    /* Distanza tra l'icona e il titolo */
}

.card-actions {
    display: flex;
    align-items: center;
    gap: 10px;
    /* Distanza tra le icone */
}

.card-actions .star {
    font-size: 24px;
    /* Regola la dimensione delle icone qui */
    color: gray;
    /* Colore di default della stella vuota */
}

.card-actions .star:hover {
    color: yellow;
    /* Colore quando il mouse è sopra */
}

.input-area {
    display: flex;
    align-items: center;
    padding: 10px 0px 10px 5px;
}

.input-area.border-upload {
    border: 2px dashed #ccc;
}

/* Durante il drag over */
.input-area.drag-over {
    border: 2px dashed #007bff;
    background-color: #f0f8ff;
}

.input-area input {
    flex-grow: 1;
    padding: 5px;
    margin-right: 10px;
    font-family: 'Open Sans', sans-serif;
}

.input-area button {
    background-color: transparent;
    /* Sfondo trasparente */
    height: 36px;
}

.input-area button svg {
    font-size: 20px;
    /* Aumenta la dimensione dell'icona */
}

.upload-area {
    border: 2px dashed #ccc;
    padding: 17px;
    text-align: center;
    cursor: pointer;
}

.upload-area.drag-over {
    border: 2px dashed #007bff;
    background-color: #f0f8ff;
}

.upload-area label {
    cursor: pointer;
}

.textarea-style {
    width: 100%;
    min-height: 90px;
    margin-right: 10px;
    padding: 10px;
    /* Aggiungi padding per un aspetto più pulito */
    resize: vertical;
    font-family: 'Open Sans', sans-serif;
    font-size: 0.8rem;
    /* Dimensione del font */
    transition: border-color 0.3s ease, box-shadow 0.3s ease;
    /* Transizioni per un effetto più fluido */
}

.textarea-style:focus {
    border-color: #4CAF50;
    /* Colore del bordo al focus */
    box-shadow: 0 0 5px rgba(76, 175, 80, 0.5);
    /* Ombra al focus */
    outline: none;
    /* Rimuove il bordo di default */
}

.loading-message {
    color: #4CAF50;
    /* Colore del testo */
    font-weight: bold;
    margin-top: 10px;
    text-align: center;
}

.card button {
    border: none;
    cursor: pointer;
}

.card button i {
    font-size: 16px;
    /* Dimensione dell'icona */
}

@media (max-width: 768px) {
    .card-container {
        grid-template-columns: 1fr;
    }
}

.playground.expanded-layout {
    display: flex;
    flex-direction: column;
}

.content-area {
    display: flex;
    width: 100%;
    justify-content: center;
}

.card-container.sidebar {
    overflow-y: auto;
    transition: width 0.3s ease;
    position: relative;
}

.card-container.sidebar.closed {
    width: 50px;
}

.toggle-sidebar {
    background: #f0f0f0;
    font-size: 20px;
    cursor: pointer;
}

.close-button {
    font-size: 20px;
    cursor: pointer;
}

.header-buttons {
    display: flex;
    gap: 5px;
    justify-content: flex-end;
    align-items: center;
}

.playground-logo {
    display: flex;
    align-items: center;
    gap: 5px;
}

.typewriter {
    overflow: hidden;
    white-space: nowrap;
    border-right: 4px solid black;
    font-family: monospace;
    width: 0;
    animation: typing 2s steps(30, end) 1s alternate forwards, blink-caret-hide 4s step-end forwards;
}

/* Scrive e poi cancella */
@keyframes typing {
    from {
        width: 0;
    }

    to {
        width: 100%;
    }
}

/* Lampeggia cursore e poi sparisce */
@keyframes blink-caret-hide {

    0%,
    90% {
        border-color: black;
    }

    95%,
    100% {
        border-color: transparent;
    }
}

.playground-logo-image {
    height: 30px;
}

.playground-expanded-card {
    flex-grow: 1;
}

.playground-expanded-card-title {
    display: flex;
    align-items: center;
    gap: 10px;
}

.playground-expanded-card-title h3 {
    font-size: 2.3em;
    color: #333;
    margin: 0;
}

.expanded-card {
    flex-grow: 1;
    width: 90%;
    padding: 20px;
    transition: margin 0.3s ease;
    /* max-height: 550px; */
    overflow-y: auto;
}

.expanded-card.margin-right {
    margin: 0 5px 0 0;
    border-left: none;
}

.expanded-card.no-margin-right {
    margin: 0 0 0 0;
    /* Nessun margine quando sidebar è chiusa */
}

.expanded-card-title {
    display: flex;
    align-items: center;
    gap: 10px;
}

.expanded-card-title h3 {
    font-size: 2.3em;
    color: #333;
    margin: 0;
}

@media (max-width: 768px) {
    .content-area {
        flex-direction: column;
    }

    .card-container.sidebar {
        width: 100%;
        max-height: none;
    }

    .playground-expanded-card {
        width: unset;
    }

    .expanded-card {
        width: unset;
    }

    .expanded-card.no-margin-right {
        margin: 0 0 20px 0;
    }

    .expanded-card.margin-right {
        margin: 0 0 20px 0;
    }

    .toggle-sidebar {
        display: none;
        /* Nasconde il toggle-sidebar nella versione mobile */
    }
}

.response-area {
    margin-top: 20px;
    padding: 10px;
    background-color: rgba(255, 255, 255, 0.1);
    border-radius: 5px;
}

.response-area h4 {
    margin-top: 0;
}

.code-language-container {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.theme-toggle-button {
    background: none;
    border: none;
    cursor: pointer;
}

.code-copy-button {
    background: none;
    border: none;
    cursor: pointer;
    display: flex;
    align-items: center;
}

.playground.expanded-layout .content-area {
    display: flex;
    width: 100%;
    justify-content: space-between;
}

.playground.expanded-layout .card-container.sidebar {
    width: 30%;
    overflow-y: auto;
    max-height: calc(100vh);
    transition: width 0.3s ease;
}

@media (max-width: 768px) {
    .playground.expanded-layout .card-container.sidebar {
        width: 100%;
        /* Imposta la larghezza al 100% solo per mobile */
        max-height: none;
        /* Rimuove il max-height solo per mobile */
    }
}

.playground.expanded-layout .card-container.sidebar.closed {
    width: 0;
}

.playground-header-actions {
    display: flex;
    gap: 20px;
    align-items: center;
}

.playground-header-dropdown {
    position: relative;
}

.playground-header-dropdown-button {
    background: none;
    border: none;
    cursor: pointer;
    font-size: 1rem;
    font-weight: bold;
    color: #333;
    padding: 8px 12px;
    transition: background-color 0.3s ease;
}

.playground-header-dropdown-button:hover {
    background-color: #f0f0f0;
}

/* Animazione della freccia */
.playground-header-dropdown-button svg {
    transition: transform 0.3s ease-in-out;
}

/* Animazioni */
@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(-5px);
    }

    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.playground-header-dropdown-menu {
    position: absolute;
    top: 100%;
    right: 0;
    background-color: white;
    border: 1px solid #ddd;
    box-shadow: 0px 4px 6px rgba(0, 0, 0, 0.1);
    min-width: 200px;
    z-index: 1000;
    padding: 5px 0;
    animation: fadeIn 0.3s ease-in-out;
}

.playground-header-dropdown-item {
    padding: 10px 20px;
    color: #333;
    cursor: pointer;
    font-size: 0.9rem;
    transition: background-color 0.3s ease;
    text-align: left;
    display: flex;
    gap: 2px;
}

.playground-header-dropdown-item:hover {
    background-color: #f5f5f5;
}

.playground-header-links {
    display: flex;
    gap: 10px;
    justify-content: flex-end;
    align-items: center;
    margin-top: -30px;
}

.playground-header-link {
    text-decoration: none;
    padding: 8px 12px;
    background-color: #4caf50;
    color: #fff;
    font-size: 0.9rem;
    transition: background-color 0.3s ease;
}

.playground-header-link:hover {
    background-color: #45a049;
}

/* SVG Icon Rotation */
.arrow-icon {
    transform: rotate(0deg);
    transition: transform 0.3s ease-in-out;
}

.arrow-icon.rotate-180 {
    transform: rotate(180deg);
}