from __future__ import annotations

import asyncio
import os
import sys
from typing import List
from xml.etree import ElementTree

import psutil
import requests
from crawl4ai import AsyncWebCrawler, BrowserConfig, CacheMode, CrawlerRunConfig

__location__ = os.path.dirname(os.path.abspath(__file__))
__output__ = os.path.join(__location__, "output")

# Append parent directory to system path
parent_dir = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
sys.path.append(parent_dir)


async def crawl_parallel(urls: List[str], max_concurrent: int = 3) -> List[str]:
    """
    Crawls multiple URLs in parallel and returns the content of successfully crawled pages.

    Args:
        urls (List[str]): List of URLs to crawl.
        max_concurrent (int): Number of concurrent crawls.

    Returns:
        List[str]: List of page contents for successfully crawled URLs.
    """

    print("\n=== Parallel Crawling with Browser Reuse + Memory Check ===")

    # We'll keep track of peak memory usage across all tasks
    peak_memory = 0
    process = psutil.Process(os.getpid())

    def log_memory(prefix: str = ""):
        nonlocal peak_memory
        current_mem = process.memory_info().rss  # in bytes
        if current_mem > peak_memory:
            peak_memory = current_mem
        print(
            f"{prefix} Current Memory: {current_mem // (1024 * 1024)} MB, Peak: {peak_memory // (1024 * 1024)} MB"
        )

    # Minimal browser config
    browser_config = BrowserConfig(
        headless=True,
        verbose=False,
        extra_args=["--disable-gpu", "--disable-dev-shm-usage", "--no-sandbox"],
    )
    crawl_config = CrawlerRunConfig(cache_mode=CacheMode.BYPASS)

    # Create the crawler instance
    crawler = AsyncWebCrawler(config=browser_config)
    await crawler.start()

    contents = []
    try:
        # We'll chunk the URLs in batches of 'max_concurrent'
        success_count, fail_count = 0, 0
        for i in range(0, len(urls), max_concurrent):
            batch = urls[i : i + max_concurrent]
            tasks = []

            for j, url in enumerate(batch):
                # Unique session_id per concurrent sub-task
                session_id = f"parallel_session_{i + j}"
                task = crawler.arun(
                    url=url,
                    config=crawl_config,
                    session_id=session_id,
                )
                tasks.append(task)

            # Check memory usage prior to launching tasks
            log_memory(prefix=f"Before batch {i//max_concurrent + 1}: ")

            # Gather results
            results = await asyncio.gather(*tasks, return_exceptions=True)

            # Check memory usage after tasks complete
            log_memory(prefix=f"After batch {i//max_concurrent + 1}: ")

            # Evaluate results
            for url, result in zip(batch, results):
                if isinstance(result, Exception):
                    print(f"Error crawling {url}: {result}")
                    fail_count += 1
                    contents.append("")
                elif result.success:
                    success_count += 1
                    contents.append(result.markdown)
                else:
                    print(f"Failed to crawl {url}")
                    fail_count += 1
                    contents.append("")

        print("\nSummary:")
        print(f"\t- Successfully crawled: {success_count}")
        print(f"\t- Failed: {fail_count}")

    finally:
        print("\nClosing crawler...")
        await crawler.close()

        # Final memory log
        log_memory(prefix="Final: ")
        print(f"\nPeak memory usage (MB): {peak_memory // (1024 * 1024)}")

    return contents        


def get_pydantic_ai_docs_urls() -> List[str]:
    """Get URLs from Pydantic AI docs sitemap."""
    sitemap_url = "https://ai.pydantic.dev/sitemap.xml"
    try:
        response = requests.get(sitemap_url)
        response.raise_for_status()

        # Parse the XML
        root = ElementTree.fromstring(response.content)

        # Extract all URLs from the sitemap
        namespace = {"ns": "http://www.sitemaps.org/schemas/sitemap/0.9"}
        urls = [loc.text for loc in root.findall(".//ns:loc", namespace)]

        return urls
    except Exception as e:
        print(f"Error fetching sitemap: {e}")
        return []


async def main():
    # Get URLs from Pydantic AI docs
    urls = get_pydantic_ai_docs_urls()
    if not urls:
        print("No URLs found to crawl")
        return

    print(f"Found {len(urls)} URLs to crawl")
    
    contents = await crawl_parallel(urls, max_concurrent=20)

    if contents:
        print(f"\nCrawling completed. Retrieved {len(contents)} pages.")
        print("\n=== Sample Contents (first 500 characters of each) ===")
        for i, content in enumerate(contents):
            print(f"\nPage {i + 1} (first 500 chars):")
            print(content[:500])
            print("...")
    else:
        print("Crawling completed, but no content was retrieved.")


if __name__ == "__main__":
    asyncio.run(main())
