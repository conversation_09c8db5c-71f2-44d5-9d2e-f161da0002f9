/* ChatBot.css */

.chatbot-functionality {
    display: flex;
    flex-direction: column;
    padding-left: 6%;
    padding-right: 10%;
    transition: width 0.3s ease;
}

.chatbot-functionality.sidebar.closed {
    padding-left: 20%;
    padding-right: 20%;
}

.chatbot-messages-area {
    flex: 1;
    overflow-y: auto;
    margin-bottom: 12%;
    padding: 10px;
}

.chatbot-message {
    display: flex;
    margin-bottom: 15px;
    position: relative;
    overflow: visible;
}

.chatbot-message.bot {
    flex-direction: row;
}

.chatbot-message.user {
    flex-direction: row-reverse;
}

.chatbot-message.bot.stats {
    text-align: right;
}

.chatbot-bot-icon {
    font-size: 24px;
    color: #616161;
}

.chatbot-user-icon {
    margin: 0 10px;
    font-size: 24px;
    color: #007bff;
}

.chatbot-message-content {
    padding: 10px 15px;
    border-radius: 5px;
    flex: 1;
}

.chatbot-message.bot .chatbot-message-content {
    background-color: #e1f5fe;
    width: 90%;
}

.chatbot-message.user .chatbot-message-content {
    background-color: #c8e6c9;
    width: 85%;
}

.chatbot-user-message {
    display: flex;
    align-items: baseline;
    flex-direction: column;
    position: fixed;
    width: 57%;
    left: 11%;
    transition: width 0.3s ease;
    bottom: 30%;
}

.chatbot-user-message.bottom-position {
    bottom: 5px;
}

.chatbot-user-message.sidebar.closed {
    width: 68%;
    left: 18%;
}

.chatbot-input-icons {
    display: flex;
    align-items: center;
    padding: 10px;
}

.chatbot-input-icons-advanced {
    display: flex;
    align-items: center;
    padding: 0 10px 10px 10px;
    font-size: 0.9em;
}

.chatbot-upload-icon,
.chatbot-mic-icon,
.chatbot-speech-icon {
    cursor: pointer;
    margin-right: 10px;
    font-size: 1.2em;
    color: #555;
}

.chatbot-upload-icon:hover,
.chatbot-mic-icon:hover,
.chatbot-speech-icon:hover,
.chatbot-user-message button svg:hover {
    color: #000;
}

.chatbot-speech-icon {
    transition: color 0.3s ease;
    /* Effetto di transizione */
}

.chatbot-speech-icon.active {
    color: #28a745;
    /* Verde per lo stato attivo */
    animation: pulse 1s infinite;
}

.chatbot-textarea-style {
    flex-grow: 1;
    resize: none;
    padding: 5px;
    border: 1px solid #ccc;
    font-family: 'Open Sans', sans-serif;
    font-size: 14px;
    margin: 10px 0px 10px 10px;
    min-height: 40px;
    max-height: 100px;
    overflow-y: auto;
    transition: height 0.3s ease-in-out;
}

.chatbot-textarea-style:focus {
    height: 200px;
    transition: height 0.3s ease-in-out;
}

.chatbot-textarea-style:not(:focus) {
    height: 40px;
}

.chatbot-user-message button {
    background-color: transparent;
    /* Sfondo trasparente */
    border: none;
    cursor: pointer;
    height: 36px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 20px;
    /* Aumenta la dimensione dell'icona */
}

.chatbot-user-message button:disabled {
    cursor: not-allowed;
    opacity: 0.6;
}

.chatbot-user-message button svg {
    color: #555;
    cursor: pointer;
}

.chatbot-input-essentials {
    align-items: center;
    display: flex;
}

.chatbot-input-area {
    background-color: #fff;
    border-radius: 5px;
    border-top: 1px solid #ddd;
    width: 100%;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1), 0 8px 16px rgba(0, 0, 0, 0.2);
}

.chatbot-input-area.drag-over {
    background-color: #f0f8ff;
    border: 2px dashed #007bff;
}

.chatbot-uploaded-file {
    display: flex;
    align-items: center;
    padding: 10px;
    border: 1px solid #ccc;
    border-radius: 5px;
    margin-top: 10px;
    background-color: #f9f9f9;
}

.chatbot-uploaded-file-icon {
    margin-right: 10px;
    color: #555;
    font-size: 1.2em;
}

.chatbot-uploaded-file-name {
    flex-grow: 1;
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;
    font-size: 0.95em;
    color: #333;
}

.chatbot-delete-file-icon {
    margin-left: 10px;
    color: #888;
    cursor: pointer;
    font-size: 1.1em;
}

.chatbot-delete-file-icon:hover {
    color: #ff4d4d;
}

.chatbot-code-language-container {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 5px;
}

.chatbot-code-copy-button,
.chatbot-theme-toggle-button {
    background-color: transparent;
    border: none;
    cursor: pointer;
    margin-left: 5px;
    display: flex;
    align-items: center;
    font-size: 14px;
}

.chatbot-code-copy-button:hover,
.chatbot-theme-toggle-button:hover {
    color: #007bff;
}

.chatbot-code-block {
    margin-top: 0;
    margin-bottom: 10px;
}

.custom-dropdown {
    width: 100%;
    /* Larghezza completa per adattarsi bene al layout */
}

.dropdown-header {
    padding: 12px;
    /* Aumenta il padding per un aspetto più gradevole */
    background-color: #f5f5f5;
    /* Colore di sfondo */
    border-radius: 5px;
    /* Angoli arrotondati */
    transition: background-color 0.3s ease;
    /* Transizione morbida */
}

/* Altre regole CSS come in precedenza... */


@media (max-width: 600px) {
    .chatbot-message-content {
        max-width: 70%;
    }

    .chatbot-textarea-style {
        font-size: 12px;
    }

    .chatbot-bot-icon {
        font-size: 20px;
    }

    .chatbot-user-icon {
        font-size: 20px;
    }
}

/* Contenitore principale del thinking */
.chatbot-think-container {
    margin: 10px 0;
    border-left: 3px solid #2196F3;
    background: linear-gradient(to right, rgba(33, 150, 243, 0.05), transparent);
    border-radius: 0 8px 8px 0;
    transition: all 0.3s ease;
}

/* Stili per il componente details */
.chatbot-think-details {
    position: relative;
    padding: 0.5rem;
}

/* Stili per il summary (header) */
.chatbot-think-summary {
    cursor: pointer;
    padding: 8px 12px;
    color: #2196F3;
    font-size: 0.9em;
    user-select: none;
    transition: color 0.3s ease;
    display: flex;
    align-items: center;
}

/* Icona personalizzata per il toggle */
.chatbot-think-summary::before {
    content: "⚡";
    margin-right: 8px;
    font-size: 1.1em;
    transition: transform 0.3s ease;
}

/* Rotazione dell'icona quando il details è aperto */
.chatbot-think-details[open] .chatbot-think-summary::before {
    transform: rotate(90deg);
}

/* Hover effect sul summary */
.chatbot-think-summary:hover {
    color: #1565C0;
    background-color: rgba(33, 150, 243, 0.1);
    border-radius: 4px;
}

/* Contenitore del testo di thinking */
.chatbot-think-text {
    padding: 12px 16px;
    margin-top: 8px;
    background-color: rgba(255, 255, 255, 0.7);
    border-radius: 6px;
    font-size: 0.95em;
    line-height: 1.5;
    color: #37474F;

    /* Animazione di apertura */
    animation: slideDown 0.3s ease-out;
    transform-origin: top;
    box-shadow: inset 0 2px 4px rgba(0, 0, 0, 0.05);
}

/* Stili per evidenziare parti importanti nel testo */
.chatbot-think-text strong {
    color: #1976D2;
    font-weight: 600;
}

.chatbot-think-text code {
    background-color: rgba(33, 150, 243, 0.1);
    padding: 2px 4px;
    border-radius: 3px;
    font-family: monospace;
    font-size: 0.9em;
}

/* Animazione di apertura */
@keyframes slideDown {
    from {
        opacity: 0;
        transform: translateY(-10px);
    }

    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Effetto hover sul contenitore principale */
.chatbot-think-container:hover {
    border-left-color: #1565C0;
    box-shadow: 0 2px 8px rgba(33, 150, 243, 0.15);
}

/* Media queries per dispositivi mobili */
@media (max-width: 600px) {
    .chatbot-think-container {
        margin: 8px 0;
    }

    .chatbot-think-summary {
        font-size: 0.85em;
    }

    .chatbot-think-text {
        padding: 10px 12px;
        font-size: 0.9em;
    }
}

.chatbot-progressive-thinking {
    font-size: 0.9em;
    transition: opacity 0.3s ease-in-out;
    display: flex;
    align-items: center;
    gap: 10px;
    position: relative;
    padding: 10px;
    background: rgba(255, 152, 0, 0.1);
    border-left: 3px solid #ff9800;
    white-space: nowrap;
    /* Evita che l'icona vada a capo */
}

.chatbot-progressive-thinking .thinking-icon {
    font-weight: bold;
    color: #ff9800;
    flex-shrink: 0;
    align-self: flex-start;
    margin: 15px;
}

.chatbot-progressive-thinking .thought-content {
    margin: 0;
    color: #333;
    font-style: italic;
    white-space: pre-wrap;
    flex-grow: 1;
    overflow: hidden;
    text-overflow: ellipsis;
    margin-right: 10px;
}

.chatbot-mistake-message {
    font-size: x-small;
    margin: 2px;
    text-align: center;
    width: 100%;
}

/* NUOVO */

/* Colonna per la timeline (marker e linea) */
.timeline-column {
    width: 24px;
    position: relative;
    margin-right: 8px;
}

/* La linea verticale (dashed) che si estende lungo la colonna */
.vertical-line {
    position: absolute;
    left: 12px;
    top: 25px;
    bottom: -15px;
    border-left: 2px dashed #979797;
}

/* Il marker (pallino) */
.marker {
    position: absolute;
    left: 4px;
    top: 0;
    width: 16px;
    height: 16px;
    background-color: #ddd;
    border-radius: 50%;
    border: 2px solid #979797;
}

.stats-box {
    font-size: 0.8em;
    color: #888;
    background: #fafafa;
    border: 1px solid #ddd;
    padding: 5px 8px;
    border-radius: 4px;
    display: inline-block;
}

.rocket-icon {
    margin-right: 5px;
    font-size: 1em;
    display: inline-flex;
    align-items: center;
}