from __future__ import annotations

from typing import List, Optional

from pydantic import BaseModel, Field


def get_default_system_prompt():
    return """\
    You are an experienced json builder working for Linkedin, designed to detect the work experiences in a curriculum vitae. 
    Make an effort to find the elements in the curriculum vitae. Answer with null only if you are truly unsure of what to say. 
    Whenever possibile use ESCO (European Skills, Competences, Qualifications and Occupations) is the European multilingual classification of Skills, Competences, Qualifications and Occupations. 

    Your goal is to identify and categorize hard and soft skills of each experiences to provide a comprehensive overview of a candidate's qualifications. 
    Hard skills refer to specific technical abilities or knowledge, while soft skills encompass interpersonal and personal attributes. 
    """


class WorkExperience(BaseModel):
    company_name: Optional[str] = Field(
        title="company_name",
        description="The name of the company",
        max_length=64,
        default=None,
    )

    job_title: Optional[str] = Field(
        title="job_title",
        description="Based on the skills and experiences of this experience, please suggest a suitable job title ESCO",
        max_length=64,
        default=None,
    )

    job_description: Optional[str] = Field(
        title="job_description",
        description="Very coincise summarization of the main goals of the job, provide clarity on what is expected in terms of performance and behavior in that role.",
        default=None,
    )

    seniority: Optional[str] = Field(
        title="seniority",
        description="The seniority level of the candidate for the job, the rank or level of experience and authority that an individual holds within a particular job or organization.",
        examples=[
            "internship",
            "junior",
            "middle",
            "senior",
            "manager",
        ],
    )

    industry: Optional[str] = Field(
        title="industry",
        description="Industry sector, aka the broader sector or field in which a company operates.",
        examples=[
            "technology",
            "manifacturing",
            "horeca",
            "food and beverage",
            "telecommunications",
            "retail",
        ],
    )

    soft_skills: List[str] = Field(
        title="soft_skills",
        description="Extract the ESCO soft skills list related to the work or related to the job_description. Soft skills are interpersonal, communication, and personal attributes that enable individuals to effectively interact with others in the workplace. Infer them if not written in the curriculum vitae",
        examples=[
            "coordinamento del personale",
            "comunicazione efficace",
            "gestione dello stress",
            "autonomia organizzativa",
            "rispetto delle scadenze",
        ],
    )

    hard_skills: List[str] = Field(
        title="hard_skills",
        description="Extract the ESCO hard skills list related to the work or related to the job_description. Occupations list, they are specific, teachable abilities or knowledge that are typically quantifiable and easily measurable. Infer them if not written in the curriculum vitae",
        examples=[
            "gestione del riciclo RAEE",
            "capacità di supervisione del lavoro",
            "conoscenza dei processi di produzione",
            "elaborazione dati fiscali",
            "uso del software specifici",
        ],
    )

    location: Optional[str] = Field(
        title="geographical location of the job",
        description="The geographical location (city and address) of the job, None if not available",
        default=None,
    )

    start_year: int = Field(
        title="year of job start",
        description="In what year did this job started (0 if not found)",
        default=0,
    )

    end_year: int = Field(
        title="year of job end",
        description="In what year did this job ended (0 if not found, or -1 if still current job)",
        default=0,
    )

    start_month: int = Field(
        title="month when the job started",
        description="The starting month of this job as integer, 0 if not found",
        default=0,
    )

    end_month: int = Field(
        title="month when the job ended",
        description="The end month of this job as integer, 0 if not found, or -1 if still current job",
        default=0,
    )


class WorkExperiences(BaseModel):
    work_experiences: List[WorkExperience] = Field(
        title="work_experiences",
        description="The job experiences of the candidate",
    )


WorkExperiences.model_rebuild()


def get_schema():
    return WorkExperiences.model_json_schema()


MAIN_CLASS = WorkExperiences
