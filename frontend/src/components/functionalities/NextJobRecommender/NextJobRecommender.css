/* -------------------- Main Container -------------------- */
.next-job-recommender-container {
    position: relative;
    background-color: #dcedc8;
    /* verde chiaro */
    border-radius: 8px;
    padding: 20px;
    font-family: 'Open Sans', sans-serif;
    color: #333;
    min-height: 400px;
}

/* -------------------- Layout a Due Colonne -------------------- */
.njr-main-layout {
    display: flex;
    gap: 20px;
}

.njr-left-panel {
    flex: 1;
    background-color: #f9fbe7;
    padding: 20px;
    border-radius: 8px;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.njr-right-panel {
    flex: 2;
    background-color: #ffffff;
    padding: 20px;
    border-radius: 8px;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

/* -------------------- Back Button & Close Button -------------------- */
.es-back-button {
    display: flex;
    justify-content: flex-end;
    margin-bottom: 10px;
}

/* -------------------- Example CV Buttons -------------------- */
.example-cv-buttons {
    display: flex;
    flex-direction: column;
    gap: 10px;
}

.example-cv-buttons button {
    background-color: #aed581;
    border: none;
    border-radius: 4px;
    padding: 8px 12px;
    cursor: pointer;
    font-weight: bold;
    transition: background-color 0.3s ease;
}

.example-cv-buttons button:hover {
    background-color: #9ccc65;
}

/* -------------------- Candidate CV Display -------------------- */
.candidate-cv-display {
    margin-bottom: 20px;
    background-color: #f9f9f9;
    padding: 15px;
    border-radius: 8px;
    box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
}

/* -------------------- Horizontal Rule -------------------- */
.custom-hr {
    border: none;
    height: 2px;
    background: linear-gradient(to right, #8bc34a, #f1f8e9);
    margin: 10px 0;
    border-radius: 1px;
}

/* -------------------- Next Job Recommendations -------------------- */
.es-next-jobs {
    margin-top: 20px;
}

.es-job-option {
    padding: 15px;
    border: 1px solid #ccc;
    border-radius: 8px;
    margin-bottom: 15px;
    transition: all 0.3s ease;
    cursor: pointer;
}

.es-job-option:hover {
    background-color: #f1f8e9;
    transform: translateY(-2px);
}

.es-job-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    font-weight: bold;
}

.es-job-details {
    margin-top: 10px;
    background: #f9f9f9;
    padding: 10px;
    border-radius: 8px;
    box-shadow: inset 0 1px 2px rgba(0, 0, 0, 0.05);
}

.es-job-details h4 {
    font-size: 1.1em;
    color: #4CAF50;
}

.es-job-details ul {
    margin: 0;
    padding: 0;
    list-style: none;
}

.es-job-details li {
    margin: 5px 0;
    padding: 5px;
    border: 1px solid #ccc;
    border-radius: 4px;
    background: #ffffff;
}

/* -------------------- Skills Badges -------------------- */
.es-skills-container {
    display: flex;
    flex-wrap: wrap;
    gap: 10px;
    margin-top: 10px;
}

.es-skill-badge {
    padding: 8px 12px;
    background-color: #e0f7fa;
    color: #00695c;
    font-size: 0.9em;
    border-radius: 20px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;
    cursor: pointer;
}

.es-skill-badge:hover {
    background-color: #b2ebf2;
}

/* -------------------- Responsive -------------------- */
@media (max-width: 768px) {
    .njr-main-layout {
        flex-direction: column;
    }

    .njr-left-panel,
    .njr-right-panel {
        min-width: 100%;
    }

    .es-skills-container {
        justify-content: center;
    }
}