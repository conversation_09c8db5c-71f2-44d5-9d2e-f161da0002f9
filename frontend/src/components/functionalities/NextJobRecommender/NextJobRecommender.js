import React, { useState, useEffect, useRef } from 'react';
import { TbArrowBackUp } from "react-icons/tb";
import { FaFolder } from 'react-icons/fa';
import './NextJobRecommender.css';

const NextJobRecommender = ({
    card,
    externalResponse,
    handleSubmit,
    onResponse,
    onCloseFiles,
    onFileUpload,
    files,
    externalInput,
}) => {
    // === ESEMPI DI CV (TEST) ===
    const dataScientistCV = `
<PERSON>e
Email: <EMAIL>
Phone: 123456789
Summary: A motivated junior data scientist with 1 year of experience in data analysis, machine learning and Python programming.
Experience:
- Data Science Intern at XYZ Corp (June 2022 - August 2022): Assisted in data cleaning, exploratory analysis, and model development.
- Research Assistant at ABC University (Sept 2021 - May 2022): Worked on predictive modeling and statistical analysis.
Education:
- B.Sc. in Computer Science, University of Somewhere (2018 - 2021)
Skills:
- Python, R, SQL, Machine Learning, Data Visualization, TensorFlow, scikit-learn.
Certifications:
- Data Science Specialization by Coursera.
Languages:
- English (Fluent)
  `;

    const maintenanceTechnicianCV = `
<PERSON>: <EMAIL>
Phone: 987654321
Summary: Experienced Mechanical Maintenance Technician with 5 years of experience in maintaining industrial machinery and equipment. Skilled in troubleshooting, preventive maintenance, and repair of mechanical systems.
Experience:
- Maintenance Technician at Alfa Industries (Jan 2019 - Present): Responsible for routine and emergency maintenance of production machinery.
- Junior Technician at Beta Manufacturing (June 2017 - Dec 2018): Assisted in the repair and upkeep of mechanical systems.
Education:
- Diploma in Mechanical Engineering, Technical Institute of Milan (2013 - 2017)
Skills:
- Mechanical troubleshooting, preventive maintenance, repair, welding, hydraulic systems, pneumatic systems.
Certifications:
- Certified Maintenance & Reliability Professional (CMRP).
Languages:
- Italian (Native), English (Intermediate)
  `;

    const salesRepresentativeCV = `
Laura Bianchi
Email: <EMAIL>
Phone: 555123456
Summary: Dynamic Sales Representative with over 3 years of experience in retail sales and customer relationship management. Proven track record in achieving sales targets and providing excellent customer service.
Experience:
- Sales Representative at Gamma Retail (March 2020 - Present): Increased sales by 20% through effective customer engagement and upselling.
- Customer Service Associate at Delta Store (Jan 2018 - Feb 2020): Handled customer inquiries and resolved issues, improving customer satisfaction.
Education:
- High School Diploma, Liceo Scientifico, Rome (2014 - 2018)
Skills:
- Sales strategies, customer relationship management, communication, negotiation, product knowledge.
Certifications:
- Retail Sales Certification.
Languages:
- Italian (Native), English (Fluent)
  `;

    // === STATI ===
    const [selectedCandidate, setSelectedCandidate] = useState(""); // CV selezionato (stringa)
    const [recommendations, setRecommendations] = useState([]);
    const [isLoading, setIsLoading] = useState(false);
    const [error, setError] = useState(null);

    // Per gestire solo i messaggi nuovi relativi al candidato corrente
    const [lastResponseIndex, setLastResponseIndex] = useState(0);

    // Riferimento per input file (upload area)
    const fileInputRef = useRef(null);

    // === FUNZIONE SUBMIT ===
    const submit = async (input, files) => {
        setIsLoading(true);
        try {
            const api_address = `/next_job_recommender`;
            await handleSubmit({
                apiEndpoint: api_address,
                input: input,           // stringa del CV
                selectedOption: null,
                files: files || [],
                isStream: true,
                engine: "vllm",
            });
        } catch (err) {
            console.error("Error during API call", err.message);
            onResponse("Error during API call", err.message);
        } finally {
            if (files && files.length > 0) {
                onCloseFiles(files[0]);
            }
            setIsLoading(false);
        }
    };

    // === GESTIONE SCELTA CV DI ESEMPIO ===
    const handleSelectExampleCV = (cvString) => {
        // Registra l'indice attuale di externalResponse per ignorare i messaggi precedenti
        setLastResponseIndex(externalResponse.length);
        setSelectedCandidate(cvString);
        setRecommendations([]);
        setError(null);
    };

    // === GESTIONE UPLOAD (quando i file cambiano) ===
    useEffect(() => {
        if (files && files.length > 0) {
            setLastResponseIndex(externalResponse.length);
            setSelectedCandidate("File uploaded: " + files[0].name);
            setRecommendations([]);
            setError(null);
            submit("", files);
        }
    }, [files]);

    // === AL CAMBIO DI selectedCandidate, FAI LA CHIAMATA ===
    useEffect(() => {
        if (selectedCandidate && !files?.length) {
            submit(selectedCandidate);
        }
    }, [selectedCandidate]);

    // === PARSING DELLA RISPOSTA IN STREAMING ===
    useEffect(() => {
        // Se non c'è candidato selezionato, resetta le raccomandazioni
        if (!selectedCandidate) {
            setRecommendations([]);
            return;
        }
        // Processa solo i messaggi nuovi: quelli dopo lastResponseIndex
        const newMessages = externalResponse.slice(lastResponseIndex);
        if (newMessages.length === 0) return;

        try {
            // Filtra le risposte del bot con type "response" dai nuovi messaggi
            const botResponses = newMessages.filter(
                (msg) => msg.sender === 'bot' && msg.type === 'response'
            );
            if (botResponses.length === 0) return;
            const lastResponse = botResponses[botResponses.length - 1].content;
            if (lastResponse.length < 50) return;
            const recObj = JSON.parse(lastResponse);
            if (recObj.recommendations) {
                setRecommendations(recObj.recommendations);
            }
        } catch (parseErr) {
            console.error("Failed to parse external response", parseErr);
        }
    }, [externalResponse, selectedCandidate, lastResponseIndex]);

    // === FUNZIONE RESET ===
    const resetCandidate = () => {
        setSelectedCandidate("");
        setRecommendations([]);
        setError(null);
        // Puoi anche resettare lastResponseIndex se desiderato
        setLastResponseIndex(0);
    };

    // === HANDLER UPLOAD CLICK ===
    const handleUploadAreaClick = (e) => {
        if (!isLoading) {
            e.stopPropagation();
            fileInputRef.current.click();
        }
    };

    // === RENDERING ----------------
    return (
        <div className="next-job-recommender-container">
            {selectedCandidate && (
                <div className="es-back-button">
                    <button
                        type="button"
                        className="close-button"
                        onClick={resetCandidate}
                        style={{ border: 'none', cursor: 'pointer' }}
                    >
                        <TbArrowBackUp /> Reset Candidate
                    </button>
                </div>
            )}

            <div className="njr-main-layout">
                {/* COLONNA SINISTRA: Selezione e Upload CV */}
                {!selectedCandidate && (
                    <div className="njr-left-panel">
                        <h3>Select or Upload a CV</h3>
                        <p>Pick one of these example CVs or upload your own to get job recommendations.</p>
                        <div className="example-cv-buttons">
                            <button onClick={() => handleSelectExampleCV(dataScientistCV)}>
                                Junior Data Scientist
                            </button>
                            <button onClick={() => handleSelectExampleCV(maintenanceTechnicianCV)}>
                                Mechanical Maintenance Technician
                            </button>
                            <button onClick={() => handleSelectExampleCV(salesRepresentativeCV)}>
                                Sales Representative
                            </button>
                        </div>
                        <hr className="divider" />
                        <div className="upload-section">
                            <div className="info-box">
                                Alternatively, upload your CV here:
                            </div>
                            <div
                                className={`upload-area ${isLoading ? 'disabled' : ''}`}
                                onClick={handleUploadAreaClick}
                                onDragOver={(e) => {
                                    if (!isLoading) {
                                        e.preventDefault();
                                        e.stopPropagation();
                                        e.currentTarget.classList.add('drag-over');
                                    }
                                }}
                                onDragLeave={(e) => {
                                    if (!isLoading) {
                                        e.preventDefault();
                                        e.stopPropagation();
                                        e.currentTarget.classList.remove('drag-over');
                                    }
                                }}
                                onDrop={(e) => {
                                    if (!isLoading) {
                                        e.preventDefault();
                                        e.stopPropagation();
                                        e.currentTarget.classList.remove('drag-over');
                                        onFileUpload(e);
                                    }
                                }}
                            >
                                <input
                                    type="file"
                                    id={`file-upload-${card.id}`}
                                    accept=".pdf,.doc,.docx"
                                    onChange={onFileUpload}
                                    style={{ display: 'none' }}
                                    ref={fileInputRef}
                                    disabled={isLoading}
                                />
                                <div>
                                    <FaFolder /> Upload your document
                                </div>
                                {files && files.length > 0 && <p>File selected: {files[0].name}</p>}
                            </div>
                        </div>
                    </div>
                )}

                {/* COLONNA DESTRA: Visualizzazione CV e Raccomandazioni */}
                {selectedCandidate && (
                    <div className="njr-right-panel">
                        <div className="candidate-cv-display">
                            <h3>Candidate CV</h3>
                            <hr className="custom-hr" />
                            <pre style={{ whiteSpace: 'pre-wrap', fontFamily: 'monospace' }}>
                                {selectedCandidate}
                            </pre>
                        </div>
                        <div className="es-next-jobs">
                            <h3>Next Job Recommendations</h3>
                            <hr className="custom-hr" />
                            {isLoading && <p className="loading-message">Loading recommendations...</p>}
                            {error && <p className="error">{error}</p>}
                            {recommendations.length > 0 ? (
                                recommendations.map((rec, index) => (
                                    <div key={index} className="es-job-option">
                                        <div className="es-job-header">
                                            <p><strong>{rec.JobTitle}</strong></p>
                                            <span>{rec.AdherencePercentage.toFixed(2)}%</span>
                                        </div>
                                        <div className="es-job-details">
                                            <p><strong>Seniority:</strong> {rec.Seniority}</p>
                                            <p><strong>Industry:</strong> {rec.Industry}</p>
                                            <h4>Skills to Enhance:</h4>
                                            <ul>
                                                {rec.SkillsToEnhance.map((skill, idx) => (
                                                    <li key={idx}>{skill}</li>
                                                ))}
                                            </ul>
                                        </div>
                                    </div>
                                ))
                            ) : (
                                !isLoading && !error && <p>No recommendations available.</p>
                            )}
                        </div>
                    </div>
                )}
            </div>
        </div>
    );
};

export default NextJobRecommender;
