from __future__ import annotations

import base64
import io
import logging
import os
import re
from collections.abc import MutableMapping
from pathlib import Path

import aiofiles
import docx  # For .docx handling
import fitz  # PyMuPDF
import requests
import yaml
from fastapi import UploadFile
from rich.logging import <PERSON><PERSON><PERSON><PERSON>
from werkzeug.utils import secure_filename  # Ensure filename security


def get_logger(
    name,
    level=logging.INFO,
    is_log_on_notebook=False,
):
    logger = logging.getLogger(name)
    logger.setLevel(level)

    # Rimuove tutti i gestori esistenti per evitare duplicati
    if logger.hasHandlers():
        logger.handlers.clear()

    if not is_log_on_notebook:
        # Usa RichHandler in ambienti che lo supportano
        handler = RichHandler(level=level, rich_tracebacks=True)
    else:
        # Usa un gestore più semplice per ambienti che non gestiscono bene RichHandler
        handler = logging.StreamHandler()

    fmt = "%(message)s"
    formatter = logging.Formatter(fmt)
    handler.setFormatter(formatter)
    logger.addHandler(handler)
    return logger


logger = get_logger(__name__)


async def extract_text_from_document(filename: str, file_bytes: bytes) -> str:
    """
    Estrae il testo da un file PDF o DOCX (in memoria).
    'filename' è usato solo per logging ed eventuale check estensione.
    'file_bytes' è il contenuto già letto in memoria.

    Ritorna sempre una stringa: il testo estratto, o "" se fallisce.
    """

    text = ""

    filename = secure_filename(filename)  # Get a secure version of the filename
    logger.info("File received: %s", filename)  # Stampa il nome del file

    try:
        # Ensure the file is handled as a binary object
        file_bytes = io.BytesIO(file_bytes)

        if filename.endswith(".pdf"):
            # Read the file into memory
            pdf_document = fitz.open(
                stream=file_bytes, filetype="pdf"
            )  # Open the PDF from a bytes stream
            for page in pdf_document:
                text += page.get_text()
            pdf_document.close()  # Chiudi il documento PDF
            logger.info("Text extracted from PDF successfully.")

        elif filename.endswith(".docx"):
            # Handle .docx extraction using python-docx
            doc = docx.Document(file_bytes)  # Open the .docx file
            for paragraph in doc.paragraphs:
                text += paragraph.text + "\n"  # Add paragraph text with newlines
            logger.info("Text extracted from DOCX successfully.")

        elif filename.endswith(".doc"):
            # Handle .doc files (legacy Word files)
            # You may need an external library like `pypandoc` or `pythoncom` with pywin32 for .doc support
            msg = "DOC file extraction is not yet implemented."
            raise NotImplementedError(msg)

        else:
            msg = "Unsupported file type."
            raise ValueError(msg)

    except Exception as e:
        error_message = f"Errore nell'estrazione del testo: {e}"
        logger.info(error_message)
        text = ""  # Se c'è stato errore, restituisce stringa vuota

    return text


async def process_files_in_memory(
    files: list[UploadFile],
) -> list[tuple[str, str, bytes]]:
    # Legge e ritorna i file in memory
    out = []
    for f in files:
        file_bytes = await f.read()
        out.append((f.filename, f.content_type, file_bytes))
    return out


def flatten_dict(d, parent_key="", sep="__"):
    """
    Flatten a dictionary of key-value pairs into a flat-level where keys are appended with __
    like in sklearn parameters.

    See here
    https://stackoverflow.com/questions/6027558/flatten-nested-dictionaries-compressing-keys
    """
    items = []
    for k, v in d.items():
        new_key = parent_key + sep + k if parent_key else k
        if isinstance(v, MutableMapping):
            items.extend(flatten_dict(v, new_key, sep=sep).items())
        else:
            items.append((new_key, v))
    return dict(items)


def transform_string2env(text: str):
    if not isinstance(text, str):
        return text

    pattern = r"\$\{([^}]+)\}"
    list_env_vars = re.findall(pattern, text)

    if list_env_vars:
        for v in list_env_vars:
            return text.replace(f"${{{v}}}", os.getenv(v))
        return None

    return text


def parse_yaml(training_config_file: Path):
    """
    Parse the configuration YAML and flatten the output
    """
    with open(training_config_file) as f:
        config_file = flatten_dict(yaml.safe_load(f))

    for key, values in config_file.items():
        config_file[key] = transform_string2env(values)

    return config_file


def encode_to_base64(content: bytes) -> str:
    """Convert binary content to a Base64 encoded string."""
    return base64.b64encode(content).decode("utf-8")


def get_base64_content_from_url(content_url: str) -> str:
    """Encode a content retrieved from a remote url to base64 format."""

    with requests.get(content_url) as response:
        response.raise_for_status()
        return encode_to_base64(response.content)


async def get_base64_from_image(file):
    """Convert an image file to a Base64 encoded string asynchronously."""
    # Asynchronously read the file content
    content = await file.read()
    # Use the synchronous function to encode the content
    return encode_to_base64(content)


async def get_base64_from_image_path(image_path: str) -> str:
    """
    Convert an image specified by its file path to a Base64 encoded string.

    Args:
        image_path (str): The path to the image file.

    Returns:
        str: Base64 encoded string of the image.
    """
    # Open the file asynchronously using aiofiles
    async with aiofiles.open(image_path, "rb") as image_file:
        return await get_base64_from_image(image_file)


if __name__ == "__main__":
    import asyncio

    base64_str = asyncio.run(
        get_base64_from_image_path(
            "/home/<USER>/workarea/ai_playground/docs/images/0.png"
        )
    )
    print(base64_str)
