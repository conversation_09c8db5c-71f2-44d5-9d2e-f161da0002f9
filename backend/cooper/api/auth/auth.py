from __future__ import annotations

import os
from datetime import datetime, timedelta, timezone

from dotenv import load_dotenv
from jose import jwt
from passlib.context import CryptContext

load_dotenv()

# AUTH_SECRET_KEY
# This secret key is used to sign (and verify) JWT tokens. 
# The secret guarantees the integrity and authenticity of the token: anyone who knows the SECRET_KEY 
# can create valid tokens, so it is essential to keep it secret.
AUTH_SECRET_KEY = os.getenv("AUTH_SECRET_KEY")

# AUTH_ALGORITHM:
# Specifies the cryptographic algorithm used to sign the token (for example, "HS256" for HMAC-SHA256). 
# Instructs jose.jwt on how to perform encoding and decoding.
AUTH_ALGORITHM = os.getenv("AUTH_ALGORITHM", "HS256")

# AUTH_ACCESS_TOKEN_EXPIRE_MINUTES:
# Defines the duration (in minutes) of the access token. Beyond this range, 
# the token expires and is no longer valid to authenticate requests.
AUTH_ACCESS_TOKEN_EXPIRE_MINUTES = int(os.getenv("AUTH_ACCESS_TOKEN_EXPIRE_MINUTES", "30"))

pwd_context = CryptContext(schemes=["bcrypt"], deprecated="auto")

def verify_password(plain_password, hashed_password):
    return pwd_context.verify(plain_password, hashed_password)

def get_password_hash(password):
    return pwd_context.hash(password)

def create_access_token(data: dict, expires_delta=None):
    to_encode = data.copy()
    expire = datetime.now(timezone.utc) + timedelta(minutes=AUTH_ACCESS_TOKEN_EXPIRE_MINUTES)
    to_encode.update({"exp": expire})
    return jwt.encode(to_encode, AUTH_SECRET_KEY, algorithm=AUTH_ALGORITHM)

if __name__ == "__main__":
    # Esempio di test: genera un hash per la password "mypass"
    password_in_chiaro = "tester"
    hashed = get_password_hash(password_in_chiaro)
    print("Hashed password:", hashed)

    # Verifica la password
    print("Verifica password corretta:", verify_password(password_in_chiaro, hashed))
    print("Verifica password sbagliata:", verify_password("wrongpass", hashed))

    # Crea un token di test
    token = create_access_token({"sub": "mioUser"})
    print("Token generato:", token)