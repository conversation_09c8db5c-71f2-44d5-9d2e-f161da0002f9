// All functionalities
import AskAnswer from './functionalities/AskAnswer/AskAnswer';
import JobAdWriter from './functionalities/JobAdWriter/JobAdWriter';
import CVParser from './functionalities/CVParser/CVParser';
import ChatBot from './functionalities/ChatBot/ChatBot';
import SmartMatching from './functionalities/SmartMatching/SmartMatching';
import SmartMatchingTest from './functionalities/SmartMatchingTest/SmartMatchingTest';
import NextJobRecommender from './functionalities/NextJobRecommender/NextJobRecommender';
import SmartInterview from './functionalities/SmartInterview/SmartInterview';
import InternalJobPosting from './functionalities/InternalJobPosting/InternalJobPosting';
import ProtectedComponent, { roleLevels } from '../services/ProtectedComponent';

// Mappa i titoli delle carte ai componenti delle funzionalità
const functionalityComponents = {
    'JobAdWriter': JobAdWriter,
    'CVParser': <PERSON><PERSON>ars<PERSON>,
    'AskAnswer': AskAnswer,
    'ChatBot': ChatBot,
    'SmartMatching': SmartMatching,
    'SmartMatchingTest': SmartMatchingTest,
    'NextJobRecommender': NextJobRecommender,
    'SmartInterview': SmartInterview,
    'InternalJobPosting': InternalJobPosting,
    // Aggiungi altre mappature qui
};


const ExpandedCard = ({
    card,
    isSidebarOpen,
    externalResponse,
    handleSubmit,
    onCloseFiles,
    onFileUpload,
    files,
    externalInput,
    onResponse,
    toggleFavorite,
    isMicActive,
    setIsMicActive,
    audioRef,
}) => {
    // Ottieni il componente specifico basato sul titolo della carta
    const SpecificFunctionality = functionalityComponents[card.type];

    if (!SpecificFunctionality) {
        return (
            <div className={`expanded-card ${isSidebarOpen ? 'margin-right' : 'no-margin-right'}`} style={{ backgroundColor: card.color }}>
                <h3>{card.title}</h3>
                <p>{card.description}</p>
                <h1>Funzionalità non disponibile.</h1>
            </div>
        );
    }

    return (
        <div className={`expanded-card ${isSidebarOpen ? 'margin-right' : 'no-margin-right'}`} style={{ backgroundColor: card.color }}>
            <ProtectedComponent minLevel={card.minVisibilityLevel}>
                <SpecificFunctionality
                    card={card}
                    externalResponse={externalResponse}
                    handleSubmit={handleSubmit}
                    onCloseFiles={onCloseFiles}
                    onFileUpload={onFileUpload}
                    files={files}
                    externalInput={externalInput}
                    onResponse={onResponse}
                    isMicActive={isMicActive} // Passa lo stato globale del microfono
                    setIsMicActive={setIsMicActive} // Setta lo stato globale del microfono
                    audioRef={audioRef}
                    isSidebarOpen={isSidebarOpen}
                />
            </ProtectedComponent>
        </div>
    );
};

export default ExpandedCard;
