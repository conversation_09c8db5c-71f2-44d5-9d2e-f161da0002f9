import React, { useRef, useEffect, useMemo } from "react";

const AudioWaveVisualizer = ({ isSpeaking }) => {
    const canvasRef = useRef(null);
    const animationIdRef = useRef(null);

    const waveConfig = useMemo(() => ({
        baseFrequency: 0.5,
        amplitude: 30,
        phaseShift: 0.02,
        waveCount: 128,
        speed: 0.02
    }), []);

    useEffect(() => {
        const canvas = canvasRef.current;
        const ctx = canvas.getContext("2d");
        let time = 0;

        const draw = () => {
            const { width, height } = canvas;
            ctx.clearRect(0, 0, width, height);

            // Create smooth gradient background
            const bgGradient = ctx.createLinearGradient(0, 0, 0, height);
            bgGradient.addColorStop(0, "rgba(41, 128, 185, 0.1)");
            bgGradient.addColorStop(1, "rgba(52, 152, 219, 0.05)");
            ctx.fillStyle = bgGradient;
            ctx.fillRect(0, 0, width, height);

            // Main wave parameters
            const centerY = height / 2;
            const mainWaveGradient = ctx.createLinearGradient(0, centerY - 50, 0, centerY + 50);
            mainWaveGradient.addColorStop(0, "rgba(52, 152, 219, 0.8)");
            mainWaveGradient.addColorStop(0.5, "rgba(41, 128, 185, 0.6)");
            mainWaveGradient.addColorStop(1, "rgba(52, 152, 219, 0.8)");

            // Draw multiple waves with different phases
            for (let wave = 0; wave < 3; wave++) {
                ctx.beginPath();
                ctx.moveTo(0, centerY);

                for (let x = 0; x < width; x++) {
                    const wavePhase = time + (wave * Math.PI / 4);
                    const y = centerY +
                        Math.sin(x * waveConfig.baseFrequency * 0.01 + wavePhase) *
                        waveConfig.amplitude *
                        (isSpeaking ? (1 + Math.sin(time * 2) * 0.3) : 0.5);

                    ctx.lineTo(x, y);
                }

                ctx.strokeStyle = mainWaveGradient;
                ctx.lineWidth = 2 - wave * 0.5;
                ctx.stroke();

                // Add glow effect
                ctx.shadowColor = "rgba(52, 152, 219, 0.5)";
                ctx.shadowBlur = 10;
                ctx.strokeStyle = "rgba(52, 152, 219, 0.3)";
                ctx.stroke();
            }

            time += waveConfig.speed * (isSpeaking ? 1 : 0.5);
            animationIdRef.current = requestAnimationFrame(draw);
        };

        draw();

        return () => {
            cancelAnimationFrame(animationIdRef.current);
        };
    }, [isSpeaking, waveConfig]);

    return (
        <div className="relative w-full h-64 bg-gradient-to-b from-blue-900/10 to-blue-800/5 rounded-xl overflow-hidden shadow-lg">
            <div className="absolute inset-0 bg-blue-500/5 animate-pulse" />
            <canvas
                ref={canvasRef}
                width={460}
                height={350}
                className="w-full h-full"
            />
            {isSpeaking && (
                <div className="absolute inset-0 bg-blue-400/5 animate-pulse" />
            )}
        </div>
    );
};

export default AudioWaveVisualizer;