from __future__ import annotations

from cooper.api.llm.instructor_llm.extract_function import ExtractFunction
from pydantic import BaseModel, Field


class ExtractFunctionJobAd(ExtractFunction):
    def __init__(self):
        super().__init__(
            model=JobAd,
            few_shot_examples=example_jobad,
            system_prompt=(
                "You are a helpful assistant tasked with extracting information from "
                "job advertisements. Your responses should be formatted in JSON "
                "according to the schema provided below. Carefully extract all "
                "relevant fields, listing each as a separate entry. Include details "
                "such as job title, job description, location (city and address), "
                "education, skills and years of experience, industry and seniority "
                "level. \nEnsure that your response adheres to the JSON validation rules. \n!!!RISPONDI SEMPRE IN LINGUA ITALIANA!!!."
            ),
        )


class JobAd(BaseModel):
    job_title: str | None = Field(title="job title", description="Title of the job")
    experience_years: int | None = Field(
        title="experience_years",
        description="How many years of experiences are required",
    )
    description: str | None = Field(
        title="job_description",
        description="as short summary of this job ad",
    )

    industry: str | None = Field(
        title="industry",
        description="Industry sector, aka the broader sector or field in which a company operates. "
        "It categorizes businesses based on the products or services they offer, "
        "as well as their overall focus and operations, for example technology, "
        "manifacturing, horeca, food and beverage, telecommunications, retail, etc.",
        default=None,
    )
    seniority: str | None = Field(
        title="seniority",
        description="The seniority level of the candidate for the job, the rank or level of "
        "experience and authority that an individual holds within a particular job or "
        "organization. Choose from 'Entry Level', 'Junior', 'Intermediate', "
        "'Senior', 'Lead', 'Principal', 'Manager', 'Director', 'Executive', 'Senior Executive'",
        default=None,
    )

    skills: list[str] = Field(
        default_factory=list,
        title="skills related to the job_description",
        description="A short comma separated list of hard skills for this the job description "
        "as in ESCO list, they are specific, teachable abilities or knowledge that"
        " are typically quantifiable and easily measurable",
    )
    location: str | None = Field(
        title="geographical location of the job",
        description="geographical location (city and address) of the job, None if not available",
        default=None,
    )
    education: str | None = Field(
        title="the degree of education required with the education field or sector",
        description="required school level or education degree like Msc, Phd, Bsc, laurea",
    )


example_jobad = """
## Example 1:

```json
{
    "job_title": "Addetto salute e sicurezza"
    "experience_years": 1,
    "description": "Collaborare con lRSPP per implementazione e aggiornamento della valutazione dei rischi, del sistema di gestione della sicurezza aziendale, alla promozione e controllo del relativo programma di attuazione; - Garantire il rispetto delle normative vigenti sulla sicurezza sul lavoro, sulle procedure e regole aziendali (principale riferimento il D. Lgs. 81/08 e successive modifiche); - Supportare lRSPP nelle attivita di informazione e formazione del personale, nonche nella gestione e supervisione dei dispositivi di protezione individuali DPI; - Organizzare prove di evacuazione e monitorare costantemente il piano di emergenza; - Supportare lRSPP nella gestione ed analisi degli infortuni e incidenti senza infortuni, valutando nel dettaglio le dinamiche e interfacciandosi con le funzioni interessate; - Effettuare periodici audit interni nei reparti e di controllo sullattivita di imprese esterne; - Collabora al mantenimento dei sistemi di gestione 9001 14001 18001 SA8000. ",
    "industry": null,
    "job_title": "ADDETTO/A SALUTE, SICUREZZA",
    "location": "Calusco d'Adda (BG)",
    "seniority": "Junior e/o Neolaureato"
    "skills": ["Conoscenza della lingua inglese", "Padronanza del pacchetto Office", "capacita di 
    elaborazione dati", "Conoscenza del D. Lgs. 81/2008, metodologie e strumenti di valutazione 
    dei rischi, delle misure di prevenzione e protezione necessarie","problem solving e gestione di 
    progetti interfunzionali", "determinazione", "proattività", "autonomia", "teamwork"], 
    "education": "Laurea o diploma e attestati modulo A e B (macro settore Ateco 4) in ambito di sicurezza" 
}
```

## Example 2:
```json
{
    "job_title": "ADDETTI FINE LINEA",
    "experience_years": 2,
    "description": "Rifornire i macchinari con i materiali necessari, controllare la conformita 
    dei prodotti finiti, regolare i macchinari attraverso gli opportuni strumenti SW/HD, garantire 
    ordine e pulizia sulla linea",
    "industry": "Manufacturing - Food & Beverages",
    "location": "Trezzo sull'Adda",
    "seniority": "Junior e/o Neolaureato"
    "required skills": ["Capacita di risoluzione problemi", "Abilità di identificare e risolvere problemi di metodo o meccanici", Buona norme di produzione, "Capacita di utilizzare con dimestichezza strumenti ed attrezzature di uso comune in produzione (chiavi,pinze etc)", "Conoscenza e applicazione di semplici interventi meccanici sulla linea (primo intervento)."]
    "education": null}
```
"""
