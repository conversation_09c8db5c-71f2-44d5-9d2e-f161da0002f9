from __future__ import annotations

import instructor
from openai import AzureOpenAI, OpenAI


def make_instructor_openai_client(
    base_url: str | None = None,
    api_key: str | None = None,
) -> instructor.Instructor:
    """
    Creates an OpenAI client that could connect to either a local OpenAI like server or to
    the OpenAI API on api.openai.com.

    Parameters
    ----------
    base_url: Optional[str]
        If None, OpenAI is called, otherwise the endpoint of a local service like
        the one spawned by vLLM on http://localhost:8000/v1 is used.

    Returns
    -------
    The OpenAI client with the `instructor` patch to enable passing a Pydantic model.
    """
    return instructor.from_openai(
        client=OpenAI(
            base_url=base_url,
            api_key=api_key,
        ),
        mode=instructor.Mode.TOOLS,
    )


def make_instructor_azure_openai_client(
    base_url: str | None = None,
    api_key: str | None = None,
    api_version: str | None = None,
) -> instructor.Instructor:
    """
    Creates an OpenAI client that could connect to either a local OpenAI like server or to
    the OpenAI API on api.openai.com.

    Parameters
    ----------
    base_url: Optional[str]
        If None, OpenAI is called, otherwise the endpoint of a local service like
        the one spawned by vLLM on http://localhost:8000/v1 is used.

    Returns
    -------
    The OpenAI client with the `instructor` patch to enable passing a Pydantic model.
    """
    return instructor.from_openai(
        client=AzureOpenAI(
            azure_endpoint=base_url,
            api_key=api_key,
            api_version=api_version,
        ),
        mode=instructor.Mode.TOOLS,
    )


def make_instructor_groq_client(
    base_url: str | None = None,
    api_key: str | None = None,
) -> instructor.Instructor:
    """
    Creates an OpenAI client that could connect to either a local OpenAI like server or to
    the OpenAI API on api.openai.com.

    Parameters
    ----------
    base_url: Optional[str]
        If None, OpenAI is called, otherwise the endpoint of a local service like
        the one spawned by vLLM on http://localhost:8000/v1 is used.

    Returns
    -------
    The OpenAI client with the `instructor` patch to enable passing a Pydantic model.
    """
    from groq import Groq

    return instructor.from_groq(
        client=Groq(
            # base_url=base_url,
            api_key=api_key,
        ),
        mode=instructor.Mode.TOOLS,
    )
