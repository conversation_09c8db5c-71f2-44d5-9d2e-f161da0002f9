.animated-background {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    border-radius: 50%; /* Forma circolare */
    animation: pulse-animated-background 1.3s infinite; /* Effetto animazione */
    opacity: 0.6; /* Trasparenza */
}

@keyframes pulse-animated-background {
    0% {
        transform: translate(-50%, -50%) scale(1);
        opacity: 0.6;
    }
    50% {
        transform: translate(-50%, -50%) scale(1.2);
        opacity: 0.8;
    }
    100% {
        transform: translate(-50%, -50%) scale(1);
        opacity: 0.6;
    }
}