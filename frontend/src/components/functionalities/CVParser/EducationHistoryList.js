import React from 'react';

const EducationHistoryList = ({ educationHistory }) => (
    <div className="education-list">
        {educationHistory.map((edu, index) => (
            <div key={index} className="education-card">
                <h4>{edu.instituteName || 'Istituto sconosciuto'}</h4>
                <p><strong>Degree Direction:</strong> {edu.degreeDirection || 'N/D'}</p>
                <p>
                    <strong>Working Period:</strong> {edu.startDate || 'N/D'} - {edu.endDate || 'N/D'}
                </p>
                <p><strong>Level:</strong> {edu.levelCodeDescription || 'N/D'}</p>
            </div>
        ))}
    </div>
);

export default EducationHistoryList;
