import React from 'react';
import { useAuth } from './AuthContext';

// Mappatura dei ruoli a livelli numerici (modifica i valori in base alle tue esigenze)
const roleLevels = {
    admin: 100,
    user: 50,
    tester: 30,
};

const ProtectedComponent = ({ minLevel, children }) => {
    const { role } = useAuth();
    // Se il ruolo non è definito, assumiamo livello 0 (o puoi gestire diversamente)
    const userLevel = roleLevels[role] || 0;

    // Renderizza i children solo se il livello dell'utente è sufficiente
    return userLevel >= minLevel ? <>{children}</> : null;
};

export default ProtectedComponent;
