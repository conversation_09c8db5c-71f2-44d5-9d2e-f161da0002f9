.modal-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 999;
}

.modal-content {
    background-color: #fff;
    padding: 20px 30px;
    max-width: 400px;
    width: 90%;
    text-align: center;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.3);
    animation: slideDown 0.3s ease-out;
}

@keyframes slideDown {
    from {
        transform: translateY(-20%);
        opacity: 0;
    }

    to {
        transform: translateY(0);
        opacity: 1;
    }
}

.modal-actions {
    margin-top: 20px;
    display: flex;
    justify-content: space-around;
}

.modal-button {
    padding: 10px 20px;
    font-size: 16px;
    border: none;
    cursor: pointer;
    min-width: 100px;
}

.modal-button.confirm {
    background-color: #28a745;
    color: white;
}

.modal-button.confirm:hover {
    background-color: #218838;
}

.modal-button.cancel {
    background-color: #dc3545;
    color: white;
}

.modal-button.cancel:hover {
    background-color: #c82333;
}