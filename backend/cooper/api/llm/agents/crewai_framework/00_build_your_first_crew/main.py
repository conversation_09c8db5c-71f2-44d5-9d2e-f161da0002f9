# src/research_crew/main.py
from __future__ import annotations

from pathlib import Path

from crew import ResearchCrew

# Create output directory if it doesn't exist
Path.mkdir(Path("backend/cooper/api/llm/agents/crewai_framework/00_build_your_first_crew/output"), exist_ok=True)


def run():
    """
    Run the research crew.
    """
    inputs = {"topic": "Artificial Intelligence in Healthcare"}

    # Create and run the crew
    result = ResearchCrew().crew().kickoff(inputs=inputs)

    # Print the result
    print("\n\n=== FINAL REPORT ===\n\n")
    print(result.raw)

    print("\n\nReport has been saved to output/report.md")


if __name__ == "__main__":
    run()
