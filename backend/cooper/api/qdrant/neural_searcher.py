from __future__ import annotations

import os
import uuid

from cooper.utils import get_logger
from dotenv import load_dotenv
from openai import AsyncOpenAI
from qdrant_client import QdrantClient
from qdrant_client.models import Filter, models

load_dotenv()

logger = get_logger(__name__)


class NeuralSearcher:
    def __init__(
        self,
        collection_name,
        collection_queries: str | None = None,
        model_name_or_path: str = "intfloat/multilingual-e5-large-instruct",
        qdrant_port: int | None = None,
        embedder_port: int | None = None,
        dense_embedder_name: str | None = None,
        sparse_embedder_name: str | None = None,
        skip_dense: bool = False,
        skip_sparse: bool = True,
    ):
        # TODO: solve it!
        if skip_dense.lower()=="false" and skip_sparse.lower()=="false":
            msg = "You can't skip both dense and sparse embeddings"
            raise ValueError(msg)


        if qdrant_port is None:
            logger.info(">>> No port provided. Using default port for Qdrant: %s", 6333)
            qdrant_port = 6333

        if embedder_port is None:
            logger.info(
                ">>> No port provided. Using default port for embedder: %s", 8089
            )
            embedder_port = 8089

        self.collection_name = collection_name
        self.collection_queries = collection_queries

        # Initialize encoder model
        self.model_name_or_path = model_name_or_path

        self.model = AsyncOpenAI(
            base_url=f"http://localhost:{embedder_port}/v1",
            api_key="not-needed",  # vLLM non verifica l'API key
        )
        # initialize Qdrant client
        self.qdrant_client = QdrantClient(f"http://localhost:{qdrant_port}", timeout=60)
        self.dense_embedder_name = dense_embedder_name
        self.sparse_embedder_name = sparse_embedder_name
        self.skip_dense = skip_dense
        self.skip_sparse = skip_sparse

    async def encode(self, text: str):
        # Convert text query into vector
        return await self.model.embeddings.create(
            model=self.model_name_or_path,
            input=[text],  # Puoi anche fare batch qui, se vuoi
            encoding_format="float",  # Di solito restituisce float32
        )

    def retrieve_vector(
        self,
        id: str,
        with_payload=True,
        with_vectors=True,
        db=None,
    ):
        # Generiamo l'UUID
        uuid_id = str(uuid.uuid5(uuid.NAMESPACE_DNS, id))
        collection_name = (
            f"{db}_{self.collection_queries}" if db else self.collection_queries
        )

        # Recupera l'oggetto usando il suo UUID
        result = self.qdrant_client.retrieve(
            collection_name=collection_name,
            ids=[uuid_id],
            with_payload=with_payload,  # ritorniamo le coordinate geo
            with_vectors=with_vectors,
        )[0]

        # Stampa il risultato
        if result:
            return result.vector, result.payload

        logger.warning("Nessun risultato trovato per l'ID specificato.")
        return None

    def search(
        self,
        vector: list,
        query_filter: Filter,
        limit: int = 2000,
        offset: int = 10,
        db: str | None = None,
    ):
        collection_name = f"{db}_{self.collection_name}" if db else self.collection_name

        # Use `vector` for search for closest vectors in the collection
        prefetch = []
        if not self.skip_dense:
            prefetch.append(
                models.Prefetch(
                    query=vector[self.dense_embedder_name],
                    using=self.dense_embedder_name,
                    limit=limit,
                )
            )
        if not self.skip_sparse:
            prefetch.append(
                models.Prefetch(
                    query=vector[self.sparse_embedder_name],
                    using=self.sparse_embedder_name,
                    limit=limit,
                )
            )

        # `search_result` contains found vector ids with similarity scores along with the stored payload
        search_result = self.qdrant_client.query_points(
            collection_name=collection_name,
            query=models.FusionQuery(fusion=models.Fusion.RRF),
            prefetch=prefetch,
            query_filter=query_filter,
            limit=limit,
            with_payload=True,
            offset=offset,
        )

        # In this function you are interested in payload only
        return [hit.payload for hit in search_result.points]


async def main():
    # Crea l'istanza di NeuralSearcher
    searcher = NeuralSearcher(
        collection_name=os.getenv("SMART_MATCHING_QDRANT_COLLECTION_DOCUMENTS"),
        collection_queries=os.getenv("SMART_MATCHING_QDRANT_COLLECTION_QUERIES"),
        model_name_or_path=os.getenv("EMBEDDER_SMART_MATCHING_MODEL"),
        qdrant_port=os.getenv("SMART_MATCHING_QDRANT_PORT"),
        embedder_port=os.getenv("EMBEDDER_SMART_MATCHING_PORT"),
        dense_embedder_name=os.getenv("SMART_MATCHING_QDRANT_DENSE_EMBEDDER_NAME"),
        sparse_embedder_name=os.getenv("SMART_MATCHING_QDRANT_SPARSE_EMBEDDER_NAME"),
        skip_dense=os.getenv("SMART_MATCHING_QDRANT_SKIP_DENSE"),
        skip_sparse=os.getenv("SMART_MATCHING_QDRANT_SKIP_SPARSE"),
    )

    text_to_encode = "Hello world!"
    logger.info("Testo in input: %s", text_to_encode)

    # Esegui l'encoding in maniera asincrona
    response = await searcher.encode(text_to_encode)

    # Stampa il risultato (controlla in che formato lo restituisce vLLM)
    logger.info("Risultato encode: %s", response)


if __name__ == "__main__":
    import asyncio

    asyncio.run(main())
