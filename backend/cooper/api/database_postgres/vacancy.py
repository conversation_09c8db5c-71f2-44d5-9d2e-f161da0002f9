from __future__ import annotations

import pandas as pd
from cooper.api.database_postgres.database import Base, SessionLocal, engine
from cooper.api.database_postgres.models import Vacancy
from cooper.utils import get_logger

logger = get_logger(__name__)

# Creazione delle tabelle nel database
Base.metadata.create_all(bind=engine)


def load_vacancies_to_db(combined_vacancies: pd.DataFrame):
    """
    Inserisce le vacancy nel database.
    Il DataFrame combined_vacancies deve contenere almeno la colonna 'DATABASE'.
    """
    session = SessionLocal()
    try:
        # Creazione degli oggetti Vacancy per ciascuna riga
        vacancy_objects = []
        for _, row in combined_vacancies.iterrows():
            vac = Vacancy(
                id=row["RECRUITMENT_ID"], database=row["DATABASE"], brand=row["brand"]
            )
            vacancy_objects.append(vac)

        session.bulk_save_objects(vacancy_objects)
        session.commit()
        logger.info("Vacancy caricate nel database.")
    except Exception as e:
        session.rollback()
        logger.error("Errore durante il caricamento delle vacancy: %e", e)
    finally:
        session.close()


# Funzione per eliminare le tabelle
def drop_all_tables():
    """
    Elimina le tabelle 'vacancy' e 'user_vacancy' dal database.
    """
    # Utilizza checkfirst=True per evitare errori se la tabella non esiste
    Vacancy.__table__.drop(bind=engine, checkfirst=True)
    logger.info("Tabelle eliminate con successo.")


if __name__ == "__main__":
    # Esempio di chiamata per eliminare le tabelle.
    # Se vuoi eliminare le tabelle, scommenta la seguente riga:
    # drop_all_tables()
    # import sys
    # sys.exit()

    # Esempio: Caricamento del DataFrame combined_vacancies
    # Supponiamo di avere un CSV con le vacancy, che contiene almeno la colonna 'DATABASE'
    # In alternativa, se già possiedi il DataFrame, salta questo passaggio.
    try:
        combined_vacancies = pd.read_parquet(
            "/home/<USER>/workarea/ai_playground/backend/cooper/api/qdrant/combined_vacancies.parquet"
        )
        # Visualizza le prime righe per controllo
        logger.info(combined_vacancies.head())

        # Carica le vacancy nel database
        load_vacancies_to_db(combined_vacancies)

    except Exception as e:
        logger.error("Error %s", e)
