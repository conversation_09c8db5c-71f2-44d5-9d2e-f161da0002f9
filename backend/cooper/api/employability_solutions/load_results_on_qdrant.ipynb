{"cells": [{"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [], "source": ["from __future__ import annotations\n", "\n", "import uuid\n", "\n", "import numpy as np\n", "import polars as pl\n", "\n", "# Import client library\n", "from qdrant_client import QdrantClient\n", "from qdrant_client.models import Distance, VectorParams\n", "\n", "client = QdrantClient(\"http://localhost:6333\", timeout=60.0)"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [], "source": ["next_job_recommender = (\n", "    pl.read_parquet(\n", "        \"/data/datascience/employability_solutions/results/empl_sol_output/cvs/cvs_processed.parquet\"\n", "    )\n", "    .with_columns(\n", "        pl.col(\"EMPLOYEE_ID\").str.extract(r\"^([^_]+)\", group_index=1).alias(\"DATABASE\"),\n", "        pl.col(\"EMPLOYEE_ID\")\n", "        .str.extract(r\"_([^_]+)$\", group_index=1)\n", "        .cast(pl.Int64)\n", "        .alias(\"EMPLOYEE_ID\"),\n", "    )\n", "    .select(\n", "        pl.col(\n", "            [\n", "                \"EMPLOYEE_ID\",\n", "                \"DATABASE\",\n", "                \"esco_job\",\n", "                \"seniority\",\n", "                \"skills\",\n", "                \"start_year\",\n", "                \"start_month\",\n", "                \"end_year\",\n", "                \"end_month\",\n", "                \"location\",\n", "            ]\n", "        )\n", "    )\n", "    .sort([\"end_year\", \"end_month\"], descending=True)\n", "    .group_by([\"DATABASE\", \"EMPLOYEE_ID\"])\n", "    .agg(pl.all().first())\n", ")\n", "\n", "\n", "occupations_it = pl.read_csv(\n", "    \"/data/datascience/employability_solutions/dataset/external_data/ESCO/ESCO dataset - v1.1.1 - classification - it - csv/occupations_it.csv\"\n", "    # \"/data/datascience/employability_solutions/dataset/external_data/ESCO/ESCO dataset - v1.1.1 - classification - en - csv/occupations_en.csv\"\n", ").rename({\"description\": \"esco_job_description\", \"preferredLabel\": \"esco_job\"})[\n", "    [\"esco_job\", \"esco_job_description\"]\n", "]"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [{"data": {"text/html": ["<div><style>\n", ".dataframe > thead > tr,\n", ".dataframe > tbody > tr {\n", "  text-align: right;\n", "  white-space: pre-wrap;\n", "}\n", "</style>\n", "<small>shape: (1, 11)</small><table border=\"1\" class=\"dataframe\"><thead><tr><th>DATABASE</th><th>EMPLOYEE_ID</th><th>ESCO_JOB</th><th>SENIORITY</th><th>SKILLS</th><th>START_YEAR</th><th>START_MONTH</th><th>END_YEAR</th><th>END_MONTH</th><th>LOCATION</th><th>ES_PREDICTIONS</th></tr><tr><td>str</td><td>i64</td><td>str</td><td>str</td><td>list[str]</td><td>i64</td><td>i64</td><td>i64</td><td>i64</td><td>str</td><td>list[struct[4]]</td></tr></thead><tbody><tr><td>&quot;ITALIA&quot;</td><td>7307389</td><td>&quot;funzionario addetto alla polit…</td><td>&quot;junior&quot;</td><td>[&quot;precisione tecnica&quot;, &quot;rispettare le scadenze&quot;, … &quot;fatturazione elettronica&quot;]</td><td>2021</td><td>5</td><td>2022</td><td>11</td><td>&quot;palermo&quot;</td><td>[{&quot;(senior) funzionario addetto alla politica tributaria/funzionaria addetta alla politica tributaria&quot;,&quot;I funzionari addetti alla politica tributaria sono impegnati nella ricerca, nell’analisi e nello sviluppo di politiche in materia di fiscalità e spesa pubblica nei settori di intervento pubblico, e attuano tali politiche per migliorare la regolamentazione esistente in questo settore. Collaborano strettamente con i partner, le organizzazioni esterne o altre parti interessate e forniscono loro aggiornamenti periodici.&nbsp;&nbsp; &quot;,[&quot;comunicazioni efficace&quot;, &quot;collaborazione di team&quot;, … &quot;gestione del tempo&quot;],0.333724}, {&quot;(senior) addetto alle operazioni fiscali/addetta alle operazioni fiscali&quot;,&quot;Gli addetti alle operazioni fiscali raccolgono informazioni finanziarie per predisporre documenti contabili e fiscali. Inoltre svolgono mansioni amministrative.&quot;,[&quot;elaborazione della documentazione&quot;, &quot;comunicazioni efficace&quot;, … &quot;collaborazione di team&quot;],0.254261}, {&quot;(middle) ispettore fiscale/ispettrice fiscale&quot;,&quot;Gli ispettori fiscali sono preposti al calcolo delle imposte e pagamento tempestivo delle stesse da parte di privati e organizzazioni. Essi forniscono informazioni e orientamenti in materia fiscale, oltre a esaminare conti e documenti finanziari per assicurare il rispetto della normativa. Inoltre, esaminano i dati disponibili per indagare sui casi di frode.&quot;,[&quot;elaborazione della documentazione&quot;, &quot;utilizzare software specifici di analisi dei dati&quot;, … &quot;liquidazione iva&quot;],0.334675}]</td></tr></tbody></table></div>"], "text/plain": ["shape: (1, 11)\n", "┌──────────┬────────────┬────────────┬───────────┬───┬──────────┬───────────┬──────────┬───────────┐\n", "│ DATABASE ┆ EMPLOYEE_I ┆ ESCO_JOB   ┆ SENIORITY ┆ … ┆ END_YEAR ┆ END_MONTH ┆ LOCATION ┆ ES_PREDIC │\n", "│ ---      ┆ D          ┆ ---        ┆ ---       ┆   ┆ ---      ┆ ---       ┆ ---      ┆ TIONS     │\n", "│ str      ┆ ---        ┆ str        ┆ str       ┆   ┆ i64      ┆ i64       ┆ str      ┆ ---       │\n", "│          ┆ i64        ┆            ┆           ┆   ┆          ┆           ┆          ┆ list[stru │\n", "│          ┆            ┆            ┆           ┆   ┆          ┆           ┆          ┆ ct[4]]    │\n", "╞══════════╪════════════╪════════════╪═══════════╪═══╪══════════╪═══════════╪══════════╪═══════════╡\n", "│ ITALIA   ┆ 7307389    ┆ funzionari ┆ junior    ┆ … ┆ 2022     ┆ 11        ┆ palermo  ┆ [{\"(senio │\n", "│          ┆            ┆ o addetto  ┆           ┆   ┆          ┆           ┆          ┆ r) funzio │\n", "│          ┆            ┆ alla       ┆           ┆   ┆          ┆           ┆          ┆ nario     │\n", "│          ┆            ┆ polit…     ┆           ┆   ┆          ┆           ┆          ┆ addett…   │\n", "└──────────┴────────────┴────────────┴───────────┴───┴──────────┴───────────┴──────────┴───────────┘"]}, "execution_count": 3, "metadata": {}, "output_type": "execute_result"}], "source": ["es_predictions = (\n", "    pl.read_parquet(\"/data/datascience/ai_playground/predictions.parquet\")\n", "    .with_columns(\n", "        pl.col(\"job\").str.extract(r\"^(.*)_\", group_index=1).alias(\"esco_job\"),\n", "        pl.col(\"job\").str.extract(r\"_([^_]+)$\", group_index=1).alias(\"seniority\"),\n", "        pl.col(\"missing_skills\").list.head(5),\n", "        pl.col(\"EMPLOYEE_ID\").str.extract(r\"^([^_]+)\", group_index=1).alias(\"DATABASE\"),\n", "        pl.col(\"EMPLOYEE_ID\")\n", "        .str.extract(r\"_([^_]+)$\", group_index=1)\n", "        .cast(pl.Int64)\n", "        .alias(\"EMPLOYEE_ID\"),\n", "    )\n", "    .with_columns(\n", "        (\"(\" + pl.col(\"seniority\") + \") \" + pl.col(\"esco_job\")).alias(\"job\"),\n", "    )\n", ")\n", "\n", "es_predictions = (\n", "    es_predictions.join(occupations_it, on=[\"esco_job\"])\n", "    .select(\n", "        pl.col(\n", "            [\n", "                \"DATABASE\",\n", "                \"EMPLOYEE_ID\",\n", "                \"job\",\n", "                \"esco_job_description\",\n", "                \"missing_skills\",\n", "                \"score\",\n", "            ]\n", "        )\n", "    )\n", "    .with_columns(\n", "        pl.struct(\n", "            [\"job\", \"esco_job_description\", \"missing_skills\", \"score\"]\n", "        ).alias(\"details\")\n", "    )\n", "    .group_by([\"DATABASE\", \"EMPLOYEE_ID\"])\n", "    .agg(pl.col(\"details\").alias(\"es_predictions\"))\n", ")\n", "\n", "\n", "next_job_recommender = next_job_recommender.join(\n", "    es_predictions, on=[\"DATABASE\", \"EMPLOYEE_ID\"]\n", ")\n", "\n", "next_job_recommender = next_job_recommender.rename(\n", "    {col: col.upper() for col in next_job_recommender.columns}\n", ")\n", "\n", "next_job_recommender"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [{"data": {"text/plain": ["{'job': '(senior) funzionario addetto alla politica tributaria/funzionaria addetta alla politica tributaria',\n", " 'esco_job_description': 'I funzionari addetti alla politica tributaria sono impegnati nella ricerca, nell’analisi e nello sviluppo di politiche in materia di fiscalità e spesa pubblica nei settori di intervento pubblico, e attuano tali politiche per migliorare la regolamentazione esistente in questo settore. Collaborano strettamente con i partner, le organizzazioni esterne o altre parti interessate e forniscono loro aggiornamenti periodici.  \\xa0',\n", " 'missing_skills': ['comunicazioni efficace',\n", "  'collaborazione di team',\n", "  'elaborazione della documentazione',\n", "  'problemsolving',\n", "  'gestione del tempo'],\n", " 'score': 0.3337236760789496}"]}, "execution_count": 4, "metadata": {}, "output_type": "execute_result"}], "source": ["next_job_recommender[\"ES_PREDICTIONS\"][0][0]"]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["['ITALIA-7307389']\n", "['cf863b13-16c5-534b-952f-f989f82f9b50']\n"]}], "source": ["next_job_recommender_ids = (\n", "    next_job_recommender.select(\n", "        (pl.col(\"DATABASE\") + \"-\" + pl.col(\"EMPLOYEE_ID\").cast(str)).alias(\"ids\")\n", "    )\n", "    .to_series()\n", "    .to_list()\n", ")\n", "\n", "# Genera UUID per ogni id\n", "next_job_recommender_uuid_ids = [\n", "    str(uuid.uuid5(uuid.NAMESPACE_DNS, next_job_recommender_id))\n", "    for next_job_recommender_id in next_job_recommender_ids\n", "]\n", "\n", "# Stampa della lista ids\n", "print(next_job_recommender_ids[:3])\n", "print(next_job_recommender_uuid_ids[:3])\n"]}, {"cell_type": "code", "execution_count": 18, "metadata": {}, "outputs": [], "source": ["collection_name = \"next_job_recommender\"\n", "\n", "# Se la collezione esiste, eliminala\n", "if client.collection_exists(collection_name):\n", "    client.delete_collection(collection_name)\n", "\n", "# Crea una nuova collezione\n", "client.create_collection(\n", "    collection_name=collection_name,\n", "    vectors_config=VectorParams(\n", "        size=1, distance=Distance.COSINE\n", "    ),\n", ")\n", "\n", "client.upload_collection(\n", "    collection_name=collection_name,\n", "    vectors=np.zeros([len(next_job_recommender_ids), 1]),\n", "    payload=next_job_recommender.to_dicts(),\n", "    ids=next_job_recommender_uuid_ids,  # Vector ids will be assigned automatically\n", "    batch_size=2**10,  # How many vectors will be uploaded in a single request?\n", ")\n"]}, {"cell_type": "code", "execution_count": 19, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["ID: cf863b13-16c5-534b-952f-f989f82f9b50\n"]}], "source": ["# Util<PERSON>za il metodo scroll per ottenere gli oggetti\n", "\n", "scroll_result, next_page_offset = client.scroll(collection_name=collection_name, limit=3, offset=1) # <PERSON>ita a 10 oggetti per esempio\n", "\n", "# Stampa gli ID degli oggetti\n", "for hit in scroll_result:\n", "    print(f\"ID: {hit.id}\") # Stampa ID\n", "    # print(f\"Payload: {hit.payload}\") # Payload se necessario"]}, {"cell_type": "code", "execution_count": 20, "metadata": {}, "outputs": [{"data": {"text/plain": ["([Record(id='cf863b13-16c5-534b-952f-f989f82f9b50', payload={'DATABASE': 'ITALIA', 'EMPLOYEE_ID': 7307389, 'ESCO_JOB': 'funzionario addetto alla politica tributaria/funzionaria addetta alla politica tributaria', 'SENIORITY': 'junior', 'SKILLS': ['precisione tecnica', 'rispettare le scadenze', 'gestione autonoma del proprio lavoro', 'fatturazione elettronica'], 'START_YEAR': 2021, 'START_MONTH': 5, 'END_YEAR': 2022, 'END_MONTH': 11, 'LOCATION': 'palermo', 'ES_PREDICTIONS': [{'job': '(senior) funzionario addetto alla politica tributaria/funzionaria addetta alla politica tributaria', 'esco_job_description': 'I funzionari addetti alla politica tributaria sono impegnati nella ricerca, nell’analisi e nello sviluppo di politiche in materia di fiscalità e spesa pubblica nei settori di intervento pubblico, e attuano tali politiche per migliorare la regolamentazione esistente in questo settore. Collaborano strettamente con i partner, le organizzazioni esterne o altre parti interessate e forniscono loro aggiornamenti periodici.  \\xa0', 'missing_skills': ['comunicazioni efficace', 'collaborazione di team', 'elaborazione della documentazione', 'problemsolving', 'gestione del tempo'], 'score': 0.3337236760789496}, {'job': '(senior) addetto alle operazioni fiscali/addetta alle operazioni fiscali', 'esco_job_description': 'Gli addetti alle operazioni fiscali raccolgono informazioni finanziarie per predisporre documenti contabili e fiscali. Inoltre svolgono mansioni amministrative.', 'missing_skills': ['elaborazione della documentazione', 'comunicazioni efficace', 'utilizzare software specifici di analisi dei dati', 'organizzazione del lavoro', 'collaborazione di team'], 'score': 0.25426137021103706}, {'job': '(middle) ispettore fiscale/ispettrice fiscale', 'esco_job_description': 'Gli ispettori fiscali sono preposti al calcolo delle imposte e pagamento tempestivo delle stesse da parte di privati e organizzazioni. Essi forniscono informazioni e orientamenti in materia fiscale, oltre a esaminare conti e documenti finanziari per assicurare il rispetto della normativa. Inoltre, esaminano i dati disponibili per indagare sui casi di frode.', 'missing_skills': ['elaborazione della documentazione', 'utilizzare software specifici di analisi dei dati', 'conoscenza della normativa fiscal', 'comunicazioni efficace', 'liquidazione iva'], 'score': 0.3346745900929742}]}, vector=None, shard_key=None, order_value=None)],\n", " None)"]}, "execution_count": 20, "metadata": {}, "output_type": "execute_result"}], "source": ["client.scroll(collection_name=collection_name, limit=3, offset=next_page_offset)"]}], "metadata": {"kernelspec": {"display_name": "ai_playground_2", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.10.16"}}, "nbformat": 4, "nbformat_minor": 2}