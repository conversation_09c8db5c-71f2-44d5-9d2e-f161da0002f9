from __future__ import annotations

from cooper.api.llm.instructor_llm.extract_function import ExtractFunction
from pydantic import BaseModel, Field


class ExtractFunctionCvParser(ExtractFunction):
    def __init__(self):
        super().__init__(
            model=RootModel,
            few_shot_examples=None,
            system_prompt="""
                You are a language model specialized in extracting structured information from resumes (CVs). Your task is to read unstructured text documents (CVs) and provide data accurately mapped to a predefined schema. Follow the schema strictly, ensuring all fields and their relationships are respected.

                The schema is divided into several sections, each with a specific set of fields describing personal information, education, work experience, skills, and other candidate attributes.

                ### **General Guidelines**
                1. Analyze the text thoroughly, extracting every relevant detail.
                2. Focus on the semantic meaning of the content, even when terminology varies or is incomplete.
                3. If a field is missing or ambiguous in the text, return `null` for that field without making arbitrary assumptions.
                4. Preserve temporal formats and standard codes such as ISO 8601 for dates and language identifiers (e.g., "en", "it").
                5. Where applicable, include explanatory descriptions for extracted codes (e.g., proficiencyCodeDescription).

                ### **Structure to Follow**
                Below is a detailed description of the fields and their use:

                #### **1. Personal**
                - Extract personal details such as first name, last name, date of birth, gender, address (including details like city, country, postal code, street name, and house number), email, phone numbers, nationality, website, and social media links.
                - **Note**: If the document includes multiple addresses or emails, list them all.

                #### **2. Education**
                - Gather education-related data, including institutions attended, degrees obtained, grades, start and end dates, and details about the education level (with codes and descriptions if available).

                #### **3. Employment**
                - Extract details about work experience: employer name, job title, location, role description, duration of employment, and industry sectors.

                #### **4. Skills**
                - Map the candidate's skills into four main categories: technical skills (computerSkill), language skills (languageSkill), other skills, and soft skills. Include durations or proficiency levels when available.

                #### **5. Summary**
                - Summarize key information, such as the current employer, current job, highest education level, and a summary of ambitions or experiences.

                #### **6. Additional Data**
                - Categorize hobbies and other additional information separately.

                ### **Additional Requirements**
                - **CV Language**: If possible, detect and specify the language of the CV in the `lang` field.
                - Use ordered lists for multiple or unambiguous fields (e.g., addresses, emails).
                - Ensure the output is in JSON format and adheres to the provided Pydantic schema.
            """,
        )


class Address(BaseModel):
    street: str | None
    city: str | None
    region: str | None
    country: str | None
    postalCode: str | None


class Personal(BaseModel):
    address: Address | None
    dateOfBirth: str | None
    driversLicense: str | None
    email: list[str]
    firstName: str | None
    gender: str | None
    homePhone: list[str]
    lastName: str | None
    middleName: str | None
    mobilePhone: list[str]
    nationality: dict | None
    personalWebsite: list[dict]
    phonelist: dict | None
    socialMediaLink: list[dict]


class EducationHistory(BaseModel):
    degreeDirection: str | None = Field(description="Field of study.")
    endDate: str | None = Field(description="End date in ISO format.")
    instituteName: str | None
    institutePlace: str | None
    levelCodeDescription: str | None = Field(
        description="Description of education level."
    )
    startDate: str | None = Field(description="Start date in ISO format.")
    grade: str | None = Field(description="Grade or score achieved.")


class EmploymentHistory(BaseModel):
    description: str | None
    employerName: str | None
    employerPlace: str | None
    endDate: str | None = Field(description="End date in ISO format.")
    experienceYears: str | None
    industries: str | None
    jobTitle: str | None
    startDate: str | None = Field(description="Start date in ISO format.")


class LanguageSkill(BaseModel):
    language: str | None
    level: str | None = Field(
        description="Language proficiency level according to CEFR (A1, A2, B1, B2, C1, C2)."
    )


class Skills(BaseModel):
    computerSkill: list[str]
    languageSkill: list[LanguageSkill]
    otherSkill: list[str]
    softSkill: list[str]


class Other(BaseModel):
    hobbies: list[str]


class RootModel(BaseModel):
    educationHistory: list[EducationHistory]
    employmentHistory: list[EmploymentHistory]
    lang: str | None = Field(description="Language of the CV.")
    other: Other | None
    personal: Personal
    skills: Skills
