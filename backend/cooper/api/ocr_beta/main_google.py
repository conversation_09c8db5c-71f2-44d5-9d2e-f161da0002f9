from __future__ import annotations

import json
import os

from google import genai
from google.genai import types
from PIL import Image


def extract_structured_data_from_image(image_path: str):
    """
    Estrae dati strutturati da una busta paga utilizzando Google Gemini.
    """
    # Imposta la chiave API
    api_key = os.getenv("GOOGLE_API_KEY")
    if not api_key:
        print("Errore: la variabile d'ambiente GOOGLE_API_KEY non è impostata.")
        return None
    client = genai.Client(api_key=api_key)

    # 1. Definisci lo schema JSON per l'output
    payslip_schema = types.Schema(
        type=types.Type.OBJECT,
        properties={
            "azienda": types.Schema(
                type=types.Type.OBJECT,
                description="Dati relativi all'azienda.",
                properties={
                    # AZI
                    "denominazione": types.Schema(
                        type=types.Type.STRING,
                        description="Ragione sociale dell'azienda.",
                    ),
                    "codice_fiscale_azienda": types.Schema(
                        type=types.Type.STRING,
                        description="Codice fiscale o partita IVA dell'azienda.",
                    ),
                    "posizione_inail": types.Schema(
                        type=types.Type.STRING, description="Numero di posizione INAIL."
                    ),
                },
                required=["denominazione"],
            ),
            "dipendente": types.Schema(
                type=types.Type.OBJECT,
                description="Dati relativi al dipendente.",
                properties={
                    # DIP
                    "codice_dipendente": types.Schema(
                        type=types.Type.INTEGER,
                        description="Codice dipendente (es:. 1340)",
                    ),
                    # 001
                    "cognome_nome": types.Schema(
                        type=types.Type.STRING,
                        description="Cognome e nome del dipendente.",
                    ),
                    # 003
                    "codice_fiscale_dipendente": types.Schema(
                        type=types.Type.STRING,
                        description="Codice fiscale del dipendente.",
                    ),
                    # 004
                    "data_nascita": types.Schema(
                        type=types.Type.STRING,
                        description="Data di nascita (GG-MM-AAAA).",
                    ),
                    # 005
                    "data_assunzione": types.Schema(
                        type=types.Type.STRING,
                        description="Data di assunzione (GG-MM-AAAA).",
                    ),
                    # 006
                    "data_cessazione": types.Schema(
                        type=types.Type.STRING,
                        description="Data cessazione (GG-MM-AAAA).",
                    ),
                    # 007
                    "data_prossimo_scatto": types.Schema(
                        type=types.Type.STRING,
                        description="Data prossimo scatto (GG-MM-AAAA).",
                    ),
                    # 008
                    "data_anzianita_convenzionale": types.Schema(
                        type=types.Type.STRING,
                        description="Data anzianità convenzionale (GG-MM-AAAA).",
                    ),
                    # 012
                    "qualifica": types.Schema(
                        type=types.Type.STRING,
                        description="Qualifica professionale del dipendente.",
                    ),
                    # 013
                    "livello": types.Schema(
                        type=types.Type.STRING, description="Livello contrattuale."
                    ),
                },
                required=[
                    "codice_dipendente",
                    "cognome_nome",
                    "codice_fiscale_dipendente",
                ],
            ),
            "periodo_riferimento": types.Schema(
                type=types.Type.OBJECT,
                description="Periodo di retribuzione.",
                properties={
                    # MESE, MENS
                    "mese": types.Schema(
                        type=types.Type.INTEGER,
                        description="Mese di riferimento (es. 9).",
                    ),
                    # ANNO
                    "anno": types.Schema(
                        type=types.Type.INTEGER,
                        description="Anno di riferimento (es. 2023).",
                    ),
                },
                required=["mese", "anno"],
            ),
            # CDC
            "cdc": types.Schema(type=types.Type.STRING, description="Centro di Costo."),
            # GDT
            "giorni_detrazione": types.Schema(
                type=types.Type.NUMBER, description="Giorni Detrazione."
            ),
            # PIF
            "progressivo_imponibile_fiscale": types.Schema(
                type=types.Type.NUMBER, description="Progressivo Imponibile Fiscale."
            ),
            # 010
            "numero_scatti": types.Schema(
                type=types.Type.INTEGER, description="Numero Scatti."
            ),
            # 014
            "perc_pt": types.Schema(
                type=types.Type.NUMBER, description="Percentuale le PT."
            ),
            # EPT
            "elementi_retributivi": types.Schema(
                type=types.Type.OBJECT,
                description="Elementi retributivi.",
                properties={
                    "minimo_tabellare": types.Schema(
                        type=types.Type.NUMBER,
                        description="Valore Minimo Tabellare.",
                    ),
                    "contingenza": types.Schema(
                        type=types.Type.NUMBER,
                        description="Valore Contingenza.",
                    ),
                    "scatti_anzianita": types.Schema(
                        type=types.Type.NUMBER,
                        description="Valore Scatti di Anzianità.",
                    ),
                    "superminimo": types.Schema(
                        type=types.Type.NUMBER,
                        description="Valore Superminimo. Superminimo Non Assorbibile",
                    ),
                    "superminimo_ass": types.Schema(
                        type=types.Type.NUMBER,
                        description="Valore Superminimo Ass o Ass.le. Superminimo  Assorbibile.",
                    ),
                    "edr_contr": types.Schema(
                        type=types.Type.NUMBER,
                        description="Valore EDR contrattuale 18.5.2021. EDR 1.1.2022",
                    ),
                    "ice_ex_ccnl": types.Schema(
                        type=types.Type.NUMBER,
                        description="Valore ICE Ex.ccnl 18/5.2021. Indennità Copertura Economica",
                    ),
                    "epa": types.Schema(
                        type=types.Type.NUMBER,
                        description="Valore E.P.A.",
                    ),
                    "scatti_congedo": types.Schema(
                        type=types.Type.NUMBER,
                        description="Valore Scatti di Congedo.",
                    ),
                },
            ),
            "dati_paga": types.Schema(
                type=types.Type.OBJECT,
                description="Principali importi della busta paga.",
                properties={
                    # T01
                    "totale_competenze": types.Schema(
                        type=types.Type.NUMBER,
                        description="Importo totale delle competenze.",
                    ),
                    # T02
                    "totale_trattenute": types.Schema(
                        type=types.Type.NUMBER,
                        description="Importo totale delle trattenute/ritenute.",
                    ),
                    # T03
                    "netto_in_busta": types.Schema(
                        type=types.Type.NUMBER,
                        description="Importo netto da pagare al dipendente.",
                    ),
                },
            ),
            "voci_corpo_cedolino": types.Schema(
                type=types.Type.ARRAY,
                description="Elenco delle voci di dettaglio presenti nel corpo del cedolino.",
                items=types.Schema(
                    type=types.Type.OBJECT,
                    properties={
                        # C01
                        "codice": types.Schema(
                            type=types.Type.STRING,
                            description="Codice della voce (es. A01, C01).",
                        ),
                        # C02
                        "descrizione": types.Schema(
                            type=types.Type.STRING,
                            description="Descrizione della voce.",
                        ),
                        # C03
                        "quantita_ore": types.Schema(
                            type=types.Type.NUMBER,
                            description="Quantità o ore relative alla voce, se applicabile.",
                        ),
                        # C04
                        "importo_base": types.Schema(
                            type=types.Type.NUMBER,
                            description="Importo base o tarifa oraria, se applicabile.",
                        ),
                        # C05
                        "dato_figurativo": types.Schema(
                            type=types.Type.NUMBER,
                            description="Corpo: Colonna Dato Figurativo.",
                        ),
                        # C06
                        "dato_competenze_ritenute": types.Schema(
                            type=types.Type.NUMBER,
                            description="Corpo: Colonna Dato Competenze/Trattenute.",
                        ),
                    },
                ),
            ),
            # 054
            "detrazioni_lavoro_dipendente": types.Schema(
                type=types.Type.NUMBER,
                description="Detrazioni Lavoro Dipendente.",
            ),
            # 055
            "detrazioni_coniuge": types.Schema(
                type=types.Type.NUMBER, description="Detrazioni Coniuge."
            ),
            # 056
            "detrazioni_figli": types.Schema(
                type=types.Type.NUMBER, description="Detrazioni Figli."
            ),
            # 057
            "detrazioni_altri_familiari": types.Schema(
                type=types.Type.NUMBER, description="Detrazioni Altri Familiari."
            ),
        },
        required=["azienda", "dipendente", "periodo_riferimento", "dati_paga"],
    )

    # 2. Carica e verifica l'immagine
    if not os.path.exists(image_path):
        print(f"Errore: file '{image_path}' non trovato.")
        return None
    img = Image.open(image_path)

    # 3. Few-shot example per guidare il modello (opzionale)
    example_user = types.Content(
        role="user",
        parts=[
            types.Part.from_text(text="Estrai questi dettagli dal testo: 'Il libro...'")
        ],
    )
    example_model = types.Content(
        role="model",
        parts=[
            types.Part.from_text(
                text='{"autore":"Douglas Adams","titolo":"Guida Galattica...","anno_pubblicazione":1978}'
            )
        ],
    )

    with open(image_path, "rb") as f:
        image_bytes = f.read()

    # 4. Input effettivo: prompt + immagine
    prompt = types.Content(
        role="user",
        parts=[
            types.Part.from_text(
                text="Analizza l'immagine della busta paga e restituisci un JSON conforme allo schema."
            ),
            types.Part.from_bytes(
                data=image_bytes,
                mime_type="image/jpeg",
            ),
        ],
    )

    contents = [example_user, example_model, prompt]

    # 5. Configura la generazione
    generate_config = types.GenerateContentConfig(
        response_mime_type="application/json",
        response_schema=payslip_schema,
        temperature=0.1,
    )

    # 6. Richiesta al modello
    print("Invio richiesta a Gemini...")
    response = client.models.generate_content(
        model="gemini-2.0-flash", contents=contents, config=generate_config
    )

    try:
        data = json.loads(response.text)
        return data
    except json.JSONDecodeError as e:
        print("Errore decodifica JSON:", e)
        return None


if __name__ == "__main__":
    image_file = "/data/datascience/ai_playground/Test OCR/Esempi_ConvDocumenti/3001003-202411-9999-DM0002-Cedolini mese_redacted/page_001.png"
    result = extract_structured_data_from_image(image_file)
    if result:
        # 7. Output e parsing
        print("\n--- JSON Estratto ---\n")
        print(json.dumps(result, indent=2, ensure_ascii=False))
