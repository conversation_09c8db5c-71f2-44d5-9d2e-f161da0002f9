import React, { useState, useEffect, useRef } from 'react';
import ReactMarkdown from 'react-markdown';
import { FaMicrophone, FaArrowRight, FaRobot, FaCopy, FaSun, FaMoon, FaTimes, FaEye, FaEyeSlash } from 'react-icons/fa';
import { FcEngineering } from "react-icons/fc";
import { IoDocumentText, IoDocumentAttach } from "react-icons/io5";
import EngineSelector from '../../EngineSelector';
import './JobAdWriter.css';

// temi code response
import { Prism as SyntaxHighlighter } from 'react-syntax-highlighter';
import { materialLight, materialDark } from 'react-syntax-highlighter/dist/esm/styles/prism';

const JobAdWriter = ({
    card,
    externalResponse,
    handleSubmit,
    onFileUpload,
    onCloseFiles,
    files,
    externalInput,
    onResponse,
}) => {
    const [internalInput, setInternalInput] = useState('');
    const [selectedOption, setSelectedOption] = useState(card.options ? card.options[0].value : '');
    const [isLoading, setIsLoading] = useState(false);
    const [copyStatus, setCopyStatus] = useState('Copy code');
    const [isDarkTheme, setIsDarkTheme] = useState(false); // Stato per il tema
    const [isDragOver, setIsDragOver] = useState(false);
    const [isEngineSelectorOpen, setIsEngineSelectorOpen] = useState(false);
    const [selectedEngine, setSelectedEngine] = useState('vllm');
    const fileInputRef = useRef(null);

    const submit = async (
        input,
        files,
        selectedOption,
        isStream = true
    ) => {


        // Non inviare se non c'è input o files
        if (!input && !files) return;

        // Reset chat history
        onResponse([]);

        setIsLoading(true); // Inizia il caricamento della risposta     

        try {

            const api_address = `/${card.title.replace(/[\s-]+/g, '_').toLowerCase()}`;
            console.log(api_address)
            console.log(input)
            console.log(isStream)

            // Utilizza submit per inviare i dati
            await handleSubmit({
                apiEndpoint: api_address,
                input: input,
                selectedOption: selectedOption, // Inserisci qui eventuali opzioni se necessario
                files: files,
                isStream: isStream,
                withChatHistory: false,
                engine: selectedEngine,
            });

        } catch (error) {
            console.error("Error during API call", error.message);
            onResponse("Error during API call", error.message);
        } finally {
            setIsLoading(false);
        }
    };

    useEffect(() => {

        if (externalInput) {
            setInternalInput(externalInput);
        }

        if (externalInput) {
            submit(externalInput, files, selectedOption, true);
        }
    }, [externalInput]); // Si attiva ogni volta che files cambia

    const submitButton = (
        <button onClick={(e) => { e.stopPropagation(); submit(internalInput, files, selectedOption, true); }} disabled={isLoading}>
            {isLoading ? 'Waiting...' : <FaArrowRight />}
        </button>
    );

    const handleDragOver = (e) => {
        if (!isLoading) {
            e.preventDefault();
            e.stopPropagation();
            setIsDragOver(true);
        }
    };

    const handleDragLeave = (e) => {
        if (!isLoading) {
            e.preventDefault();
            e.stopPropagation();
            setIsDragOver(false);
        }
    };

    const handleDrop = (e) => {
        if (!isLoading) {
            e.preventDefault();
            e.stopPropagation();
            setIsDragOver(false);
            onFileUpload(e);
        }
    };

    const truncateFileName = (name, maxLength = 20) => {
        if (name.length <= maxLength) return name;
        const extIndex = name.lastIndexOf('.');
        const ext = extIndex !== -1 ? name.substring(extIndex) : '';
        const baseName = name.substring(0, extIndex !== -1 ? extIndex : name.length);
        return baseName.length > (maxLength - ext.length - 3) ? `${baseName.substring(0, maxLength - ext.length - 3)}...${ext}` : name;
    };

    const copyToClipboard = (text) => {
        navigator.clipboard.writeText(text).then(() => {
            setCopyStatus('Code copied ✔');
            setTimeout(() => setCopyStatus('Copy code'), 2000); // Reset after 2 seconds
        }, (err) => {
            console.error('Errore nel copiare il testo: ', err);
        });
    };

    const toggleTheme = () => {
        setIsDarkTheme((prevTheme) => !prevTheme);
    };
    const syntaxHighlighterTheme = isDarkTheme ? materialDark : materialLight; // Seleziona il tema

    // Componente per il codice
    const CodeBlock = ({ node, inline, className, children, ...props }) => {
        const match = /language-(\w+)/.exec(className || '');
        const codeString = String(children).replace(/\n$/, '');
        const language = match ? match[1] : null;

        if (!inline && language) {
            return (
                <div style={{ position: 'relative' }}>
                    <div className='code-language-container'>
                        <span style={{ padding: '10px 0 0 0' }}>
                            {language.toUpperCase()}
                        </span>
                        <button
                            className='theme-toggle-button'
                            onClick={toggleTheme}
                        >
                            {isDarkTheme ? <FaSun /> : <FaMoon />}
                        </button>
                        <button
                            className="code-copy-button"
                            onClick={() => copyToClipboard(codeString)}
                        >
                            <FaCopy style={{ marginRight: '5px' }} />
                            {copyStatus}
                        </button>
                    </div>
                    <SyntaxHighlighter
                        style={syntaxHighlighterTheme}
                        language={language}
                        PreTag="div"
                        className="jobad-code-block"
                        showLineNumbers={false}
                        wrapLongLines={true}
                        customStyle={{
                            fontSize: '0.8em',
                            lineHeight: '1.5',
                            padding: '10px 10px 10px 10px',
                            borderRadius: '5px',
                        }}
                        {...props}
                    >
                        {codeString}
                    </SyntaxHighlighter>
                </div>
            );
        }
        return (
            <code className={className} {...props}>
                {children}
            </code>
        );
    };

    // Funzione per capire se mostrare il messaggio di "Ongoing reasoning..."
    const NextResponseMessage = () => {
        return externalResponse[externalResponse.length-1].type != 'thinking';
    };


    const renderBotMessage = (msg, index) => {
        const { type, content } = msg;

        if (type === 'thinking') {
            // Se il prossimo messaggio è "response", non mostriamo il messaggio di attesa.
            if (NextResponseMessage()) return null;

            return (
                <div className="jobad-thinking-container">
                    <div className="jobad-thinking-spinner"></div>
                    <span className="jobad-thinking-text">Ongoing reasoning...</span>
                </div>
            );
        }

        if (type === 'response') {
            return (
                <ReactMarkdown
                    children={content}
                    components={content.includes('```') ? { code: CodeBlock } : undefined}
                />
            );
        }

        return null;
    };


    return (
        <div className="functionality">

            {card.options && card.options.length > 0 && (
                <div className="jobad-radio-group">
                    {card.options.map(option => (
                        <label
                            key={option.value}
                            style={{ display: 'flex' }}
                        >
                            <input
                                type="radio"
                                value={option.value}
                                checked={selectedOption === option.value}
                                onChange={(e) => setSelectedOption(e.target.value)}
                                style={{ marginRight: '5px' }}
                            />
                            <h2 style={{ color: '#333' }} >{option.label}</h2>
                        </label>
                    ))}
                </div>
            )}

            {card.hasUpload && (
                <>
                    <div
                        className={`jobad-user-message ${isLoading ? 'disabled' : ''}`}
                        onDragOver={handleDragOver}
                        onDragLeave={handleDragLeave}
                        onDrop={handleDrop}
                    >
                        <div className={`jobad-input-area ${isDragOver ? 'drag-over' : ''}`}>
                            <div className={`jobad-input-essentials`}>
                                <input
                                    type="file"
                                    id={`file-upload-${card.id}`}
                                    accept=".pdf,.doc,.docx"
                                    onChange={onFileUpload}
                                    style={{ display: 'none' }}
                                    ref={fileInputRef} // Assegna il riferimento all'input
                                    disabled={isLoading}
                                />
                                <textarea
                                    className="jobad-textarea-style"
                                    value={internalInput}
                                    onChange={(e) => setInternalInput(e.target.value)}
                                    placeholder="Enter your input here or drag a file..."
                                    onKeyDown={(e) => {
                                        if (e.key === 'Enter' && !e.shiftKey && !isLoading) { // Invio da tastiera
                                            e.preventDefault();  // Previene il comportamento predefinito del tasto Enter
                                            submit(internalInput, files, selectedOption, true);  // submit and start API with streaming=true
                                        }
                                    }}
                                    disabled={isLoading}
                                />
                                <div className="jobad-input-icons">
                                    <FaMicrophone className="jobad-mic-icon" />
                                    {submitButton}
                                </div>

                            </div>
                            <div className="jobad-input-icons-advanced">
                                <FcEngineering
                                    className="jobad-mic-icon"
                                    onClick={() => setIsEngineSelectorOpen(!isEngineSelectorOpen)} // Toggle per aprire/chiudere il selettore
                                />
                                {isEngineSelectorOpen && ( // Mostra il selettore solo se è aperto
                                    <EngineSelector
                                        selectedEngine={selectedEngine}
                                        onEngineSelect={(engine) => {
                                            setSelectedEngine(engine);
                                            setIsEngineSelectorOpen(false); // Chiudi il selettore dopo la selezione
                                        }}
                                    />
                                )}
                                <IoDocumentAttach
                                    className="jobad-upload-icon"
                                    onClick={(e) => {
                                        e.stopPropagation();
                                        if (!isLoading) {
                                            fileInputRef.current.click();
                                        }
                                    }}
                                    title="Upload File"
                                />
                            </div>
                        </div>


                        {/* Nuovo Div per Visualizzare i File Caricati */}
                        {files && files.length > 0 && (
                            <div className="chatbot-uploaded-files">
                                {files.map((file, index) => (
                                    <div key={index} className="chatbot-uploaded-file">
                                        <IoDocumentText className="chatbot-uploaded-file-icon" />
                                        <span className="chatbot-uploaded-file-name" title={file.name}>
                                            {truncateFileName(file.name)}
                                        </span>
                                        <FaTimes className="chatbot-delete-file-icon" onClick={() => onCloseFiles(file)} title="Delete File" />
                                    </div>
                                ))}
                            </div>
                        )}

                    </div>

                </>
            )}


            {externalResponse && (
                <div className="response-area">
                    <h4><FaRobot /></h4>
                    {externalResponse
                        .filter((msg) => msg.sender === "bot")
                        .map((msg, index) => (
                            <div key={index} className="bot-message">
                                {renderBotMessage(msg, index)}
                            </div>
                        ))
                    }
                </div>
            )}


        </div>
    );
};

export default JobAdWriter;