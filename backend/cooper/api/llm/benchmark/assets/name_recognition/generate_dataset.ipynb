{"cells": [{"cell_type": "code", "execution_count": 9, "metadata": {}, "outputs": [{"data": {"text/html": ["<div><style>\n", ".dataframe > thead > tr,\n", ".dataframe > tbody > tr {\n", "  text-align: right;\n", "  white-space: pre-wrap;\n", "}\n", "</style>\n", "<small>shape: (5_000, 6)</small><table border=\"1\" class=\"dataframe\"><thead><tr><th>ID_USER</th><th>ID_CHAT</th><th>ID_MESSAGE</th><th>ROLE</th><th>CONTENT</th><th>ORDER</th></tr><tr><td>i64</td><td>i64</td><td>i64</td><td>str</td><td>str</td><td>i64</td></tr></thead><tbody><tr><td>5732</td><td>0</td><td>1</td><td>&quot;user&quot;</td><td>&quot;Indovina il mio nome!&quot;</td><td>0</td></tr><tr><td>5732</td><td>0</td><td>2</td><td>&quot;system&quot;</td><td>&quot;Come ti chiami?&quot;</td><td>1</td></tr><tr><td>5732</td><td>0</td><td>3</td><td>&quot;user&quot;</td><td>&quot;Francesco!&quot;</td><td>2</td></tr><tr><td>5732</td><td>0</td><td>4</td><td>&quot;system&quot;</td><td>&quot;Ah, bene Francesco!&quot;</td><td>3</td></tr><tr><td>5732</td><td>0</td><td>5</td><td>&quot;user&quot;</td><td>&quot;Hai capito come mi chiamo? Con…</td><td>4</td></tr><tr><td>&hellip;</td><td>&hellip;</td><td>&hellip;</td><td>&hellip;</td><td>&hellip;</td><td>&hellip;</td></tr><tr><td>3620</td><td>999</td><td>4996</td><td>&quot;user&quot;</td><td>&quot;Indovina il mio nome!&quot;</td><td>0</td></tr><tr><td>3620</td><td>999</td><td>4997</td><td>&quot;system&quot;</td><td>&quot;Come ti chiami?&quot;</td><td>1</td></tr><tr><td>3620</td><td>999</td><td>4998</td><td>&quot;user&quot;</td><td>&quot;Umberto!&quot;</td><td>2</td></tr><tr><td>3620</td><td>999</td><td>4999</td><td>&quot;system&quot;</td><td>&quot;Ah, bene Umberto!&quot;</td><td>3</td></tr><tr><td>3620</td><td>999</td><td>5000</td><td>&quot;user&quot;</td><td>&quot;Hai capito come mi chiamo? Con…</td><td>4</td></tr></tbody></table></div>"], "text/plain": ["shape: (5_000, 6)\n", "┌─────────┬─────────┬────────────┬────────┬─────────────────────────────────┬───────┐\n", "│ ID_USER ┆ ID_CHAT ┆ ID_MESSAGE ┆ ROLE   ┆ CONTENT                         ┆ ORDER │\n", "│ ---     ┆ ---     ┆ ---        ┆ ---    ┆ ---                             ┆ ---   │\n", "│ i64     ┆ i64     ┆ i64        ┆ str    ┆ str                             ┆ i64   │\n", "╞═════════╪═════════╪════════════╪════════╪═════════════════════════════════╪═══════╡\n", "│ 5732    ┆ 0       ┆ 1          ┆ user   ┆ Indovina il mio nome!           ┆ 0     │\n", "│ 5732    ┆ 0       ┆ 2          ┆ system ┆ Come ti chiami?                 ┆ 1     │\n", "│ 5732    ┆ 0       ┆ 3          ┆ user   ┆ Francesco!                      ┆ 2     │\n", "│ 5732    ┆ 0       ┆ 4          ┆ system ┆ Ah, bene <PERSON>!             ┆ 3     │\n", "│ 5732    ┆ 0       ┆ 5          ┆ user   ┆ Hai capito come mi chiamo? Con… ┆ 4     │\n", "│ …       ┆ …       ┆ …          ┆ …      ┆ …                               ┆ …     │\n", "│ 3620    ┆ 999     ┆ 4996       ┆ user   ┆ Indovina il mio nome!           ┆ 0     │\n", "│ 3620    ┆ 999     ┆ 4997       ┆ system ┆ Come ti chiami?                 ┆ 1     │\n", "│ 3620    ┆ 999     ┆ 4998       ┆ user   ┆ Umberto!                        ┆ 2     │\n", "│ 3620    ┆ 999     ┆ 4999       ┆ system ┆ Ah, bene <PERSON>!               ┆ 3     │\n", "│ 3620    ┆ 999     ┆ 5000       ┆ user   ┆ Hai capito come mi chiamo? Con… ┆ 4     │\n", "└─────────┴─────────┴────────────┴────────┴─────────────────────────────────┴───────┘"]}, "execution_count": 9, "metadata": {}, "output_type": "execute_result"}], "source": ["import polars as pl\n", "import random\n", "\n", "# Definizione dei nomi casuali per gli utenti\n", "NAMES = [\"<PERSON><PERSON>\", \"<PERSON>\", \"<PERSON><PERSON><PERSON>\", \"Giulia\", \"Francesco\"]\n", "\n", "def generate_chat_data(n_chats: int = 10):\n", "    \"\"\"Genera un dataset di conversazioni simulate con struttura Polars.\"\"\"\n", "    data = []\n", "    message_template = [\n", "        {\"role\": \"user\", \"content\": \"Indovina il mio nome!\"},\n", "        {\"role\": \"system\", \"content\": \"Come ti chiami?\"},\n", "        {\"role\": \"user\", \"content\": \"{name}!\"},\n", "        {\"role\": \"system\", \"content\": \"Ah, bene {name}!\"},\n", "        {\"role\": \"user\", \"content\": \"Hai capito come mi chiamo? Conosci qualche storia riguardo al mio nome?\"},\n", "    ]\n", "\n", "    message_id = 0  # ID univoco per ogni messaggio\n", "\n", "    for chat_id in range(n_chats):\n", "        user_id = random.randint(1000, 9999)  # Genera un ID utente casuale\n", "        random_name = random.choice(NAMES)  # Seleziona un nome casuale\n", "\n", "        for order, msg in enumerate(message_template):\n", "            message_id += 1\n", "            data.append({\n", "                \"ID_USER\": user_id,\n", "                \"ID_CHAT\": chat_id,\n", "                \"ID_MESSAGE\": message_id,\n", "                \"ROLE\": msg[\"role\"],\n", "                \"CONTENT\": msg[\"content\"].format(name=random_name),\n", "                \"ORDER\": order\n", "            })\n", "\n", "    return pl.DataFrame(data)\n", "\n", "# Generiamo un dataset di 20 chat\n", "chat_data = generate_chat_data(n_chats=1000)\n", "chat_data.write_parquet(\"/home/<USER>/workarea/ai_playground/backend/cooper/api/llm/benchmark/assets/name_recognition/chat_data.parquet\")\n", "chat_data"]}, {"cell_type": "code", "execution_count": 10, "metadata": {}, "outputs": [{"data": {"text/html": ["<div><style>\n", ".dataframe > thead > tr,\n", ".dataframe > tbody > tr {\n", "  text-align: right;\n", "  white-space: pre-wrap;\n", "}\n", "</style>\n", "<small>shape: (1_000, 3)</small><table border=\"1\" class=\"dataframe\"><thead><tr><th>ID_USER</th><th>ID_CHAT</th><th>messages</th></tr><tr><td>i64</td><td>i64</td><td>list[struct[2]]</td></tr></thead><tbody><tr><td>7985</td><td>597</td><td>[{&quot;user&quot;,&quot;Indovina il mio nome!&quot;}, {&quot;system&quot;,&quot;Come ti chiami?&quot;}, … {&quot;user&quot;,&quot;Hai capito come mi chiamo? Conosci qualche storia riguardo al mio nome?&quot;}]</td></tr><tr><td>2187</td><td>133</td><td>[{&quot;user&quot;,&quot;Indovina il mio nome!&quot;}, {&quot;system&quot;,&quot;Come ti chiami?&quot;}, … {&quot;user&quot;,&quot;Hai capito come mi chiamo? Conosci qualche storia riguardo al mio nome?&quot;}]</td></tr><tr><td>2370</td><td>497</td><td>[{&quot;user&quot;,&quot;Indovina il mio nome!&quot;}, {&quot;system&quot;,&quot;Come ti chiami?&quot;}, … {&quot;user&quot;,&quot;Hai capito come mi chiamo? Conosci qualche storia riguardo al mio nome?&quot;}]</td></tr><tr><td>5330</td><td>242</td><td>[{&quot;user&quot;,&quot;Indovina il mio nome!&quot;}, {&quot;system&quot;,&quot;Come ti chiami?&quot;}, … {&quot;user&quot;,&quot;Hai capito come mi chiamo? Conosci qualche storia riguardo al mio nome?&quot;}]</td></tr><tr><td>2148</td><td>901</td><td>[{&quot;user&quot;,&quot;Indovina il mio nome!&quot;}, {&quot;system&quot;,&quot;Come ti chiami?&quot;}, … {&quot;user&quot;,&quot;Hai capito come mi chiamo? Conosci qualche storia riguardo al mio nome?&quot;}]</td></tr><tr><td>&hellip;</td><td>&hellip;</td><td>&hellip;</td></tr><tr><td>3862</td><td>174</td><td>[{&quot;user&quot;,&quot;Indovina il mio nome!&quot;}, {&quot;system&quot;,&quot;Come ti chiami?&quot;}, … {&quot;user&quot;,&quot;Hai capito come mi chiamo? Conosci qualche storia riguardo al mio nome?&quot;}]</td></tr><tr><td>6694</td><td>113</td><td>[{&quot;user&quot;,&quot;Indovina il mio nome!&quot;}, {&quot;system&quot;,&quot;Come ti chiami?&quot;}, … {&quot;user&quot;,&quot;Hai capito come mi chiamo? Conosci qualche storia riguardo al mio nome?&quot;}]</td></tr><tr><td>8262</td><td>214</td><td>[{&quot;user&quot;,&quot;Indovina il mio nome!&quot;}, {&quot;system&quot;,&quot;Come ti chiami?&quot;}, … {&quot;user&quot;,&quot;Hai capito come mi chiamo? Conosci qualche storia riguardo al mio nome?&quot;}]</td></tr><tr><td>7851</td><td>429</td><td>[{&quot;user&quot;,&quot;Indovina il mio nome!&quot;}, {&quot;system&quot;,&quot;Come ti chiami?&quot;}, … {&quot;user&quot;,&quot;Hai capito come mi chiamo? Conosci qualche storia riguardo al mio nome?&quot;}]</td></tr><tr><td>5987</td><td>270</td><td>[{&quot;user&quot;,&quot;Indovina il mio nome!&quot;}, {&quot;system&quot;,&quot;Come ti chiami?&quot;}, … {&quot;user&quot;,&quot;Hai capito come mi chiamo? Conosci qualche storia riguardo al mio nome?&quot;}]</td></tr></tbody></table></div>"], "text/plain": ["shape: (1_000, 3)\n", "┌─────────┬─────────┬─────────────────────────────────┐\n", "│ ID_USER ┆ ID_CHAT ┆ messages                        │\n", "│ ---     ┆ ---     ┆ ---                             │\n", "│ i64     ┆ i64     ┆ list[struct[2]]                 │\n", "╞═════════╪═════════╪═════════════════════════════════╡\n", "│ 7985    ┆ 597     ┆ [{\"user\",\"Indovina il mio nome… │\n", "│ 2187    ┆ 133     ┆ [{\"user\",\"Indovina il mio nome… │\n", "│ 2370    ┆ 497     ┆ [{\"user\",\"Indovina il mio nome… │\n", "│ 5330    ┆ 242     ┆ [{\"user\",\"Indovina il mio nome… │\n", "│ 2148    ┆ 901     ┆ [{\"user\",\"Indovina il mio nome… │\n", "│ …       ┆ …       ┆ …                               │\n", "│ 3862    ┆ 174     ┆ [{\"user\",\"Indovina il mio nome… │\n", "│ 6694    ┆ 113     ┆ [{\"user\",\"Indovina il mio nome… │\n", "│ 8262    ┆ 214     ┆ [{\"user\",\"Indovina il mio nome… │\n", "│ 7851    ┆ 429     ┆ [{\"user\",\"Indovina il mio nome… │\n", "│ 5987    ┆ 270     ┆ [{\"user\",\"Indovina il mio nome… │\n", "└─────────┴─────────┴─────────────────────────────────┘"]}, "execution_count": 10, "metadata": {}, "output_type": "execute_result"}], "source": ["formatted_data = (\n", "    chat_data.sort([\"ID_USER\", \"ID_CHAT\", \"ORDER\"])\n", "    .group_by([\"ID_USER\", \"ID_CHAT\"])\n", "    .agg([\n", "        pl.struct([\"ROLE\", \"CONTENT\"])\n", "        .alias(\"messages\")\n", "    ])\n", ")\n", "formatted_data"]}, {"cell_type": "code", "execution_count": 11, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["shape: (5,)\n", "Series: '' [struct[2]]\n", "[\n", "\t{\"user\",\"Indovina il mio nome!\"}\n", "\t{\"system\",\"Come ti chiami?\"}\n", "\t{\"user\",\"<PERSON>iada!\"}\n", "\t{\"system\",\"Ah, bene G<PERSON>!\"}\n", "\t{\"user\",\"Hai capito come mi chiamo? Conosci qualche storia riguardo al mio nome?\"}\n", "]\n", "shape: (5,)\n", "Series: '' [struct[2]]\n", "[\n", "\t{\"user\",\"Indovina il mio nome!\"}\n", "\t{\"system\",\"Come ti chiami?\"}\n", "\t{\"user\",\"<PERSON>iada!\"}\n", "\t{\"system\",\"Ah, bene G<PERSON>!\"}\n", "\t{\"user\",\"Hai capito come mi chiamo? Conosci qualche storia riguardo al mio nome?\"}\n", "]\n"]}], "source": ["# Metodo 1: usando item()\n", "print(formatted_data[\"messages\"].item(0))\n", "\n", "# Metodo 2: usando first()\n", "print(formatted_data[\"messages\"][0])\n"]}], "metadata": {"kernelspec": {"display_name": "ai_playground", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.10.16"}}, "nbformat": 4, "nbformat_minor": 2}