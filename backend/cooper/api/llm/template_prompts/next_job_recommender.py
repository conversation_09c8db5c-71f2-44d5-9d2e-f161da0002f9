from __future__ import annotations

from pydantic import BaseModel


def get_default_system_prompt():
    return """
    You are an advanced job recommendation system designed to analyze resumes (CVs) and generate career suggestions. 
    Your task is to process the provided CV and extract relevant information to make detailed job recommendations. 

    ### Input:
    - A CV in plain text containing:
    - Professional experiences (job titles, companies, and durations).
    - Skills and competencies.
    - Educational background.
    - Any additional relevant information (e.g., certifications, languages, personal projects).

    ### Output:
    A structured JSON object containing:
    - **JobTitle**: The title of the recommended job role.
    - **Seniority**: The suggested seniority level for the role. Choose from:
    - "Junior": Entry-level roles requiring foundational skills.
    - "Mid-level": Roles requiring moderate experience and specialized skills.
    - "Senior": Advanced roles requiring significant expertise and responsibility.
    - "Managerial": Roles emphasizing team leadership, project management, and strategic decision-making.
    - **Industry**: The industry relevant to the job role (e.g., "Technology/AI", "Finance", "Healthcare").
    - **Skills to Enhance**: A list of skills that could be further developed to strengthen alignment with the job role. Include both skills entirely missing and those requiring improvement.
    - **AdherencePercentage**: A percentage score indicating how well the candidate aligns with the job role based on their CV.

    ### Example Output:
    ```json
    [
    {
        "JobTitle": "AI Team Lead",
        "Seniority": "Managerial",
        "Industry": "Technology/AI",
        "SkillsToEnhance": [
            "Team Leadership",
            "Cross-Functional Communication",
            "Strategic AI Project Planning"
        ],
        "AdherencePercentage": 85.0
    },
    {
        "JobTitle": "Machine Learning Engineer",
        "Seniority": "Senior",
        "Industry": "Technology/AI",
        "SkillsToEnhance": [
            "Cloud Computing for AI Deployment",
            "Model Optimization Techniques",
            "Advanced Reinforcement Learning"
        ],
        "AdherencePercentage": 90.0
    },
    {
        "JobTitle": "AI Solutions",
        "Seniority": "Managerial",
        "Industry": "Technology/AI",
        "SkillsToEnhance": [
            "AI Project Lifecycle Management",
            "Stakeholder Engagement",
            "Budget and Resource Allocation"
        ],
        "AdherencePercentage": 78.0
    }
    ]
    ```

    ### Constraints:
    - Analyze the CV carefully to extract relevant information about the candidate's skills, experiences, and education.
    - Ensure recommendations align with the candidate's current trajectory while suggesting meaningful next steps in their career.
    - Be specific about the skills to enhance, avoiding overly generic suggestions.
    - Provide a realistic adherence percentage based on how closely the candidate's profile matches the recommended job role.
    """


class JobRecommendation(BaseModel):
    JobTitle: str  # The title of the recommended job role
    Seniority: str
    Industry: str  # The industry relevant to the job role
    SkillsToEnhance: list[str]  # List of skills to improve or acquire for the role
    AdherencePercentage: float  # How closely the candidate aligns with the role


class JobRecommendationResponse(BaseModel):
    recommendations: list[
        JobRecommendation
    ]  # List of job recommendations based on the CV input


JobRecommendationResponse.model_rebuild()


def get_schema():
    return JobRecommendationResponse.model_json_schema()


if __name__ == "__main__":
    # Example instantiation to test the model
    example_data = {
        "recommendations": [
            {
                "JobTitle": "AI Team Lead",
                "Seniority": "Managerial",
                "Industry": "Technology/AI",
                "SkillsToEnhance": [
                    "Team Leadership",
                    "Cross-Functional Communication",
                    "Strategic AI Project Planning",
                ],
                "AdherencePercentage": 85.0,
            },
            {
                "JobTitle": "Machine Learning Engineer",
                "Seniority": "Senior",
                "Industry": "Technology/AI",
                "SkillsToEnhance": [
                    "Cloud Computing for AI Deployment",
                    "Model Optimization Techniques",
                    "Advanced Reinforcement Learning",
                ],
                "AdherencePercentage": 90.0,
            },
            {
                "JobTitle": "AI Solutions",
                "Seniority": "Managerial",
                "Industry": "Technology/AI",
                "SkillsToEnhance": [
                    "AI Project Lifecycle Management",
                    "Stakeholder Engagement",
                    "Budget and Resource Allocation",
                ],
                "AdherencePercentage": 78.0,
            },
        ]
    }

    # Create an instance of the model with example data
    response = JobRecommendationResponse(**example_data)
    print(response.model_dump_json(indent=4))
