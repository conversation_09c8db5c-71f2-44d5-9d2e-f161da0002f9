import React, { useState, useEffect, useRef } from 'react';
import remarkGfm from 'remark-gfm';
import remarkBreaks from 'remark-breaks';
import remarkMath from 'remark-math';
import rehypeKatex from 'rehype-katex';
import 'katex/dist/katex.min.css';
import ReactMarkdown from 'react-markdown';
import { FaArrowRight, FaRobot, FaCopy, FaSun, FaMoon, FaUser, FaTimes, FaRocket } from 'react-icons/fa';
import { MdGraphicEq } from "react-icons/md";
import { IoDocumentText, IoDocumentAttach } from "react-icons/io5";
import { FcEngineering } from "react-icons/fc";
import EngineSelector from '../../EngineSelector';
import AudioRecorder from '../../AudioRecorder';
import SpiritualBackground from "./SpiritualBackground";
import './ChatBot.css';

// markdown processor powered by plugins
// https://remark.js.org/

// temi per il SyntaxHighlighter
import { Prism as SyntaxHighlighter } from 'react-syntax-highlighter';
import { materialLight, materialDark } from 'react-syntax-highlighter/dist/esm/styles/prism';

const ChatBot = ({
    card,
    externalResponse,
    handleSubmit,
    onFileUpload,
    onCloseFiles,
    files,
    externalInput,
    onResponse,
    isMicActive,
    setIsMicActive,
    isSidebarOpen,
}) => {
    const [internalInput, setInternalInput] = useState('');
    const [isLoading, setIsLoading] = useState(false);
    const [copyStatus, setCopyStatus] = useState('Copy code');
    const [isDarkTheme, setIsDarkTheme] = useState(false); // Stato per il tema
    const [messages, setMessages] = useState([]); // Stato per i messaggi
    const [selectedEngine, setSelectedEngine] = useState('vllm');
    const [isEngineSelectorOpen, setIsEngineSelectorOpen] = useState(false);
    const [isDragOver, setIsDragOver] = useState(false);
    const [isRecording, setIsRecording] = useState(false);
    const [textToSpeech, setTextToSpeech] = useState(false);
    const fileInputRef = useRef(null);
    const messagesEndRef = useRef(null); // Per lo scroll automatico


    const submit = async (
        input,
        files,
        isStream = true,
        withChatHistory = true
    ) => {

        // Non inviare se non c'è input o files
        if (!input && !files) return;

        setIsLoading(true); // Inizia il caricamento della risposta  
        setInternalInput(''); // Clean input

        try {

            const api_address = `/${card.title.replace(/[\s-]+/g, '_').toLowerCase()}`;
            // console.log(api_address)
            // console.log(input)
            // console.log(isStream)

            // Utilizza submit per inviare i dati
            await handleSubmit({
                apiEndpoint: api_address,
                input: input,
                selectedOption: null, // Inserisci qui eventuali opzioni se necessario
                files: files,
                isStream: isStream,
                withChatHistory: withChatHistory,
                engine: selectedEngine,
                textToSpeech: textToSpeech,
            });

        } catch (error) {
            console.error("Error during API call", error.message);
            onResponse("Error during API call", error.message);
        } finally {
            // Release lock
            setIsLoading(false);
        }
    };

    /**
     * useEffect che intercetta il flusso in streaming (externalResponse)
     * e aggiorna i messaggi di tutta la conversazione "dinamicamente".
     */
    useEffect(() => {
        if (!externalResponse) return;

        setMessages(externalResponse);
    }, [externalResponse]);

    // Scrolla automaticamente all'ultimo messaggio
    useEffect(() => {
        if (messages.length > 0 && messagesEndRef.current) {
            messagesEndRef.current.scrollIntoView({ behavior: 'smooth' });
        }
    }, [messages]);

    // Se c'è un externalInput (ad es. un comando esterno) lo eseguiamo
    useEffect(() => {

        if (externalInput) {
            setInternalInput(externalInput);
            submit(externalInput, files, true);
        }

    }, [externalInput]); // Si attiva ogni volta che externalInput cambia

    const submitButton = (
        <button onClick={(e) => {
            e.stopPropagation();
            submit(internalInput, files, true);
        }}
            disabled={isLoading}
        >
            {isLoading ? 'Waiting...' : <FaArrowRight />}
        </button>
    );

    const copyToClipboard = (text) => {
        navigator.clipboard.writeText(text).then(() => {
            setCopyStatus('Code copied ✔');
            setTimeout(() => setCopyStatus('Copy code'), 2000); // Reset after 2 seconds
        }, (err) => {
            console.error('Errore nel copiare il testo: ', err);
        });
    };

    const handleDragOver = (e) => {
        if (!isLoading) {
            e.preventDefault();
            e.stopPropagation();
            setIsDragOver(true);
        }
    };

    const handleDragLeave = (e) => {
        if (!isLoading) {
            e.preventDefault();
            e.stopPropagation();
            setIsDragOver(false);
        }
    };

    const handleDrop = (e) => {
        if (!isLoading) {
            e.preventDefault();
            e.stopPropagation();
            setIsDragOver(false);
            onFileUpload(e);
        }
    };

    const truncateFileName = (name, maxLength = 20) => {
        if (name.length <= maxLength) return name;
        const extIndex = name.lastIndexOf('.');
        const ext = extIndex !== -1 ? name.substring(extIndex) : '';
        const baseName = name.substring(0, extIndex !== -1 ? extIndex : name.length);
        return baseName.length > (maxLength - ext.length - 3) ? `${baseName.substring(0, maxLength - ext.length - 3)}...${ext}` : name;
    };

    const toggleTheme = () => {
        setIsDarkTheme((prevTheme) => !prevTheme);
    };
    const syntaxHighlighterTheme = isDarkTheme ? materialDark : materialLight;


    // Componente per il codice
    const CodeBlock = ({ node, inline, className, children, ...props }) => {
        const match = /language-(\w+)/.exec(className || '');
        const codeString = String(children).replace(/\n$/, '');
        const language = match?.[1]; //match ? match[1] : 'text';

        if (!inline && language) {
            return (
                <div style={{ position: 'relative' }}>
                    <div className='chatbot-code-language-container'>
                        <span style={{ padding: '10px 0 0 0' }}>
                            {language.toUpperCase()}
                        </span>
                        <button
                            className='chatbot-theme-toggle-button'
                            onClick={toggleTheme}
                        >
                            {isDarkTheme ? <FaSun /> : <FaMoon />}
                        </button>
                        <button
                            className="chatbot-code-copy-button"
                            onClick={() => copyToClipboard(codeString)}
                        >
                            <FaCopy style={{ marginRight: '5px' }} />
                            {copyStatus}
                        </button>
                    </div>
                    <SyntaxHighlighter
                        style={syntaxHighlighterTheme}
                        language={language}
                        PreTag="div"
                        className="chatbot-code-block"
                        showLineNumbers={false}
                        wrapLongLines={true}
                        customStyle={{
                            whiteSpace: 'pre-wrap',
                            wordWrap: 'break-word',
                            fontSize: '0.8em',
                            lineHeight: '1.5',
                            padding: '10px 10px 10px 10px',
                            borderRadius: '5px',
                        }}
                        {...props}
                    >
                        {codeString}
                    </SyntaxHighlighter>
                </div>
            );
        }
        return (
            <code className={className} {...props}>
                {children}
            </code>
        );
    };

    /**
     * Funzione per renderizzare il messaggio del bot,
     * distinguendo tra codice e testo semplice, e aggiungendo la sezione <think>.
     */
    const renderBotMessage = (msg, index) => {
        // 1) distrutturiamo solo `type` da const, e usiamo let per content
        const { type } = msg;
        let content = msg.content;

        // 2) applichiamo la pulizia del fenced block altrimenti non possiamo renderizzare
        // le tabelle in markdown
        if (type === 'response' && typeof content === 'string') {
            content = content.replace(
                /^```(?:markdown)?\n([\s\S]*?)\n```$/m,
                (_, inner) => inner.trim()
            );
        }

        let ThinkingSection = null;
        let ProgressiveThinkingBox = null;
        const hasStoppedThinking = messages[index + 1]?.sender === "bot" && messages[index + 1]?.type === "response";

        // Gestione del tipo "thinking"
        if (type === "thinking") {

            // Componente comune per il thinking (espandibile)
            ThinkingSection = (
                <div className="chatbot-think-container">
                    <details className="chatbot-think-details">
                        <summary className="chatbot-think-summary">
                            <strong>Show / Hide internal reasoning</strong>
                        </summary>
                        <div className="chatbot-think-text">
                            <ReactMarkdown>{content}</ReactMarkdown>
                        </div>
                    </details>
                </div>
            );

            // Prendi l'ultimo ragionamento dal thinking (split su "\n")
            const lastThought = content.split('\n').filter(line => line.trim() !== '').pop();

            // Componente per la visualizzazione progressiva del pensiero
            // Se il testo della risposta inizia a essere popolato, nascondiamo il box del ragionamento
            ProgressiveThinkingBox = (
                <div className="chatbot-progressive-thinking">
                    <span className="thinking-icon">🤖 Thinking...</span>
                    <ReactMarkdown className="thought-content">{lastThought}</ReactMarkdown>
                </div>
            );

        }

        return (
            <>
                {type === "retrieving" && (
                    <ReactMarkdown>{content}</ReactMarkdown>
                )}
                {type === "thinking" && (
                    <>
                        {ThinkingSection}
                        {!hasStoppedThinking && ProgressiveThinkingBox}
                    </>
                )}
                {type === "response" && (
                    <ReactMarkdown
                        children={content}
                        remarkPlugins={[remarkGfm, remarkBreaks, remarkMath]}
                        rehypePlugins={[rehypeKatex]}
                        components={content.includes('```') ? { code: CodeBlock } : undefined}
                    />
                )}
                {(type === "stats") && (
                    // Note: A 'chunk' is not necessarily the same as a 'token'.
                    // Each chunk may contain multiple tokens or even partial tokens, depending on the LLM's streaming implementation.
                    // For an exact token count, further processing (e.g., tokenization) might be necessary.

                    <div className="stats-box">
                        <span className="rocket-icon">
                            <FaRocket />
                        </span>
                        <span><b>{content.token_per_sec}</b> chunks/s • </span>
                        <span><b>{content.total_duration}s</b> total duration • </span>
                        <span><b>{content.time_to_first_token}s</b> to first token • </span>
                        <span><b>{content.num_chunks}</b> chunks</span>
                    </div>
                )}
            </>
        );

    };

    const isFirstBotMessage = (msg, index, messages) => {
        return msg.sender === 'bot' && (index === 0 || messages[index - 1].sender !== 'bot');
    };


    return (
        <div className={`chatbot-functionality ${messages.length > 0 ? 'has-messages' : ''} ${isSidebarOpen ? 'sidebar open' : 'sidebar closed'}`}>

            <div className="chatbot-messages-area">
                {messages.map((msg, index) => {

                    const isUser = (msg.sender != "bot");

                    const firstBotFlag = isFirstBotMessage(msg, index, messages);
                    // Serve per non mostrare la linea sull'ultimo messaggio o se dopo c'è un messaggio user
                    const nextIsUser = index === messages.length - 1 || messages[index + 1]?.sender === "user";

                    return (
                        <div key={index} className={`chatbot-message ${msg.sender} ${msg.type}`}>
                            {isUser ? (
                                <FaUser className="chatbot-user-icon" />

                            ) : (
                                firstBotFlag ? (
                                    <>
                                        <div className="timeline-column">
                                            {!nextIsUser && <div className="vertical-line"></div>}
                                            {textToSpeech > 0 && (
                                                <SpiritualBackground
                                                    color1="rgba(255, 255, 255, 0.1)"
                                                    color2="rgb(0 122 255)"
                                                    size={30}
                                                />
                                            )}
                                            <FaRobot className="chatbot-bot-icon" />
                                        </div>
                                    </>
                                ) : (
                                    <div className="timeline-column">
                                        {!nextIsUser && <div className="vertical-line"></div>}
                                        <div className="marker"></div>
                                    </div>
                                )
                            )}

                            <div className="chatbot-message-content">
                                {isUser
                                    ? <ReactMarkdown>{msg.content}</ReactMarkdown>
                                    : renderBotMessage(msg, index)
                                }
                            </div>
                        </div>
                    );
                })}
                <div ref={messagesEndRef} />
            </div>

            {card.hasUpload && (
                <>
                    <div
                        className={`chatbot-user-message ${messages.length > 0 ? 'bottom-position' : ''} ${isLoading ? 'disabled' : ''} ${isSidebarOpen ? 'sidebar open' : 'sidebar closed'}`}
                        onDragOver={handleDragOver}
                        onDragLeave={handleDragLeave}
                        onDrop={handleDrop}
                    >
                        {messages.length === 0 && (
                            <div className="chatbot-initial-message">
                                <h1>Welcome! How can I assist you today?</h1>
                                <p>I can help you screen candidates, analyze resumes, or answer HR-related questions. Try asking me:</p>
                                <ul>
                                    <li>"Can you review this resume?"</li>
                                    <li>"What's a good way to write a job post for a software engineer?"</li>
                                    <li>"How do I handle candidate rejections professionally?"</li>
                                </ul>
                            </div>
                        )}

                        <div className={`chatbot-input-area ${isDragOver ? 'drag-over' : ''}`}>
                            <div className={`chatbot-input-essentials`}>
                                <input
                                    type="file"
                                    id={`file-upload-${card.id}`}
                                    accept=".pdf,.doc,.docx"
                                    onChange={onFileUpload}
                                    style={{ display: 'none' }}
                                    ref={fileInputRef} // Assegna il riferimento all'input
                                    disabled={isLoading}
                                />
                                <textarea
                                    className="chatbot-textarea-style"
                                    value={internalInput}
                                    onChange={(e) => setInternalInput(e.target.value)}
                                    placeholder="Enter your input here or drag a file..."
                                    onKeyDown={(e) => {
                                        if (e.key === 'Enter' && !e.shiftKey && !isLoading) { // Invio da tastiera
                                            e.preventDefault();  // Previene il comportamento predefinito del tasto Enter
                                            submit(internalInput, files, true);  // submit and start API with streaming=true
                                        }
                                    }}
                                    disabled={isLoading}
                                />
                                <div className="chatbot-input-icons">
                                    <AudioRecorder
                                        onTranscriptReceived={(transcript) => setInternalInput(transcript)}
                                        isMicActive={isMicActive}
                                        setIsMicActive={setIsMicActive}
                                        isRecording={isRecording}
                                        setIsRecording={setIsRecording}
                                        classStyleFaMicrophone="chatbot-mic-icon"
                                    />
                                    {submitButton}
                                </div>

                            </div>
                            <div className="chatbot-input-icons-advanced">
                                <FcEngineering
                                    className="chatbot-mic-icon"
                                    onClick={() => setIsEngineSelectorOpen(!isEngineSelectorOpen)}
                                />
                                {isEngineSelectorOpen && (
                                    <EngineSelector
                                        selectedEngine={selectedEngine}
                                        onEngineSelect={(engine) => {
                                            setSelectedEngine(engine);
                                            setIsEngineSelectorOpen(false);
                                        }}
                                        onClose={() => setIsEngineSelectorOpen(false)}
                                    />
                                )}
                                <IoDocumentAttach
                                    className="chatbot-upload-icon"
                                    onClick={(e) => {
                                        e.stopPropagation();
                                        if (!isLoading) {
                                            fileInputRef.current.click();
                                        }
                                    }}
                                    title="Upload File"
                                />
                                {/* Icona per il text-to-speech */}
                                <MdGraphicEq
                                    className={`chatbot-speech-icon ${textToSpeech ? "active" : ""}`}
                                    onClick={() => setTextToSpeech((prev) => !prev)}
                                    title={textToSpeech ? "Disable Text to Speech" : "Enable Text to Speech"}
                                />
                            </div>
                        </div>

                        {/* Nuovo Div per Visualizzare i File Caricati */}
                        {files && files.length > 0 && (
                            <div className="chatbot-uploaded-files">
                                {files.map((file, index) => (
                                    <div key={index} className="chatbot-uploaded-file">
                                        <IoDocumentText className="chatbot-uploaded-file-icon" />
                                        <span className="chatbot-uploaded-file-name" title={file.name}>
                                            {truncateFileName(file.name)}
                                        </span>
                                        <FaTimes
                                            className="chatbot-delete-file-icon"
                                            onClick={() => onCloseFiles(file)}
                                            title="Delete File"
                                        />
                                    </div>
                                ))}
                            </div>
                        )}
                        <p className='chatbot-mistake-message'>GiGpt may make mistakes. Please evaluate the answers with critical thinking.</p>
                    </div>
                </>
            )}
        </div>
    );
};

export default ChatBot;
