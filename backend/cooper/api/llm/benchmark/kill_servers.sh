#!/bin/bash

PID_FILE="/home/<USER>/workarea/ai_playground/backend/cooper/api/llm/benchmark/vllm_pids.txt"

if [ -f "$PID_FILE" ]; then
    while read -r pid; do
        echo "Chiudo il processo con PID: $pid"
        kill $pid
    done < "$PID_FILE"
    echo "Tutti i processi sono stati fermati."
    rm -f "$PID_FILE"  # Rimuove il file dei PID dopo la chiusura
else
    echo "Nessun file dei PID trovato. Nessun processo da chiudere."
fi