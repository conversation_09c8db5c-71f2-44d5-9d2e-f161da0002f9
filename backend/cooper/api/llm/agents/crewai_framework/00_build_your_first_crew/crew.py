# src/research_crew/crew.py
from __future__ import annotations

import os

from crewai import LLM, Agent, Crew, Process, Task
from crewai.agents.agent_builder.base_agent import BaseAgent
from crewai.project import CrewBase, agent, crew, task
from tools.custom_tool import DuckDuckGoSearchTool

llm = LLM(
    provider="openai",
    model="hosted_vllm/" + os.getenv("VLLM_DISTRIBUTION"),
    base_url=os.getenv("VLLM_ENDPOINT"),
    api_key=os.getenv("VLLM_API_KEY")
)
@CrewBase
class ResearchCrew:
    """Research crew for comprehensive topic analysis and reporting"""

    agents: list[BaseAgent]
    tasks: list[Task]

    @agent
    def researcher(self) -> Agent:
        return Agent(
            role=self.agents_config["researcher"]["role"],
            goal=self.agents_config["researcher"]["goal"],
            backstory=self.agents_config["researcher"]["backstory"],
            llm=llm,
            verbose=True,
            tools=[DuckDuckGoSearchTool()],
        )

    @agent
    def analyst(self) -> Agent:
        return Agent(
            role=self.agents_config["analyst"]["role"],
            goal=self.agents_config["analyst"]["goal"],
            backstory=self.agents_config["analyst"]["backstory"],
            llm=llm,
            verbose=True,
        )

    @task
    def research_task(self) -> Task:
        return Task(
            config=self.tasks_config["research_task"]  # type: ignore[index]
        )

    @task
    def analysis_task(self) -> Task:
        return Task(
            config=self.tasks_config["analysis_task"],  # type: ignore[index]
            output_file="backend/cooper/api/llm/agents/crewai_framework/00_build_your_first_crew/output/report.md",
        )

    @crew
    def crew(self) -> Crew:
        """Creates the research crew"""
        return Crew(
            agents=self.agents,
            tasks=self.tasks,
            process=Process.sequential,
            verbose=True,
        )
