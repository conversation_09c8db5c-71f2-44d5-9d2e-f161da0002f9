- [Project Structure](#project-structure)
- [Installation](#installation)
  - [Requirements](#requirements)
    - [Install NodeJS](#install-nodejs)
    - [Create .env file a livello di root:](#create-env-file-a-livello-di-root)
  - [Install nginx](#install-nginx)
  - [Install llama.cpp](#install-llamacpp)
    - [CUDA: Docker to run llama.cpp](#cuda-docker-to-run-llamacpp)
    - [CPU: Docker to run llama.cpp](#cpu-docker-to-run-llamacpp)
  - [Environment:](#environment)
    - [Create the environment:](#create-the-environment)
    - [Install crawl4ai](#install-crawl4ai)
    - [(AVOID) Install llama.cpp locally](#avoid-install-llamacpp-locally)
    - [Install React packages](#install-react-packages)
    - [(EXAMPLE) Install new React Package](#example-install-new-react-package)
    - [Remove environment:](#remove-environment)
  - [Login postgres](#login-postgres)
  - [Launch Documentation](#launch-documentation)
  - [Launch application](#launch-application)
  - [Launch VLLM embedder](#launch-vllm-embedder)
  - [Launch VLLM transcription](#launch-vllm-transcription)
- [Deploying models](#deploying-models)
- [Ollama - Deploying models](#ollama---deploying-models)
- [Vllm - Deploying models with docker](#vllm---deploying-models-with-docker)
  - [Multimodal models](#multimodal-models)
  - [LLM models (no thinking)](#llm-models-no-thinking)
  - [LLM models (thinking)](#llm-models-thinking)
  - [VLM models](#vlm-models)
- [Vllm - Distribute server requests among different models](#vllm---distribute-server-requests-among-different-models)
  - [START](#start)
  - [STOP](#stop)


## Project Structure
```bash
ai_playground/
│
├── backend/
│   └── cooper/
│       ├── api/
│       └── __init__.py
│
├── pyproject.toml
├── setup.py
└── README.md

│
├── frontend/                # Frontend con React
│   ├── public/
│   │   └── index.html       # HTML di base
│   ├── src/
│   │   ├── App.js           # Componente principale di React
│   │   ├── index.js         # Entrypoint React
│   │   ├── components/
│   │   │   ├── Playground.js       # Componente principale del Playground
│   │   │   └── ...     
│   │   ├── services/
│   │   │   └── api.js       # File per gestire le chiamate API
│   │   └── styles/
│   │       └── styles.css   # File CSS per gli stili
│   └── package.json         # Dipendenze npm per React
└── README.md
└── .env
```


------------

## Installation

### Requirements
#### Install NodeJS
```bash
curl -sL https://rpm.nodesource.com/setup_18.x | sudo bash -
sudo yum install -y nodejs
```

#### Create .env file a livello di root:

Crea un file .env prendnedo come esempio il file [.env.example](/.env.example)

### Install nginx
```bash
cd /home/<USER>/workarea/ai_playground
docker build . -f .dockerfiles/Dockerfile.nginx --tag nginx-lb
docker network create vllm_nginx
```

### Install llama.cpp

```bash
# 3. Getting the GGUF

export HUGGINGFACE_TOKEN=<token>
huggingface-cli login --token $HUGGINGFACE_TOKEN

# Download the GGUF model that you want with huggingface-cli:
# EXAMPLE: huggingface-cli download <model_repo> <gguf_file> --local-dir <local_dir>
# (e.g. 1)

# (e.g. 2)

```

#### CUDA: Docker to run llama.cpp

```bash
# qwen2.5-0.5b-instruct-q5_k_m.gguf
huggingface-cli download Qwen/Qwen2.5-0.5B-Instruct-GGUF qwen2.5-0.5b-instruct-q5_k_m.gguf --local-dir /data/datascience/.shared/.transformers_cache/


docker run --rm \
    --name qwen2.5-0.5b-instruct-q5_k_m.gguf \
    --gpus all \
    -v /data/datascience/.shared/.transformers_cache:/models \
    -p 8000:8000 \
    ghcr.io/ggerganov/llama.cpp:full-cuda \
    --server -m /models/qwen2.5-0.5b-instruct-q5_k_m.gguf \
    --port 8000 --host 0.0.0.0 \
    -fa -ngl 80 -n 512 --ctx-size 8192

# DeepSeek-R1-UD-Q2_K_X
huggingface-cli download unsloth/DeepSeek-R1-GGUF --include "DeepSeek-R1-UD-Q2_K_XL/*" --local-dir /data/datascience/.shared/.transformers_cache/

docker run --rm \
    --name DeepSeek-R1-UD-Q2_K_X \
    --gpus all \
    -v /data/datascience/.shared/.transformers_cache:/models \
    -p 8000:8000 \
    ghcr.io/ggerganov/llama.cpp:full-cuda \
    --server -m /models/DeepSeek-R1-UD-Q2_K_XL/DeepSeek-R1-UD-Q2_K_XL-00001-of-00005.gguf \
    --port 8000 --host 0.0.0.0 \
    -fa -ngl 80 -n 4096 --temp 0.6 --ctx-size 8192
```


#### CPU: Docker to run llama.cpp

```bash
# qwen2.5-0.5b-instruct-q5_k_m.gguf
huggingface-cli download Qwen/Qwen2.5-0.5B-Instruct-GGUF qwen2.5-0.5b-instruct-q5_k_m.gguf --local-dir /data/datascience/.shared/.transformers_cache/

docker run --rm \
    --name qwen2.5-0.5b-instruct-q5_k_m.gguf \
    -v /data/datascience/.shared/.transformers_cache:/models \
    -p 8000:8000 \
    ghcr.io/ggerganov/llama.cpp:full \
    --server -m /models/qwen2.5-0.5b-instruct-q5_k_m.gguf \
    --port 8000 --host 0.0.0.0 \
    -n 512 --ctx-size 4096

# 1.58bit UD-IQ1_S 131GB Accuracy: Fair
huggingface-cli download unsloth/DeepSeek-R1-GGUF --include "DeepSeek-R1-UD-IQ1_S/*" --local-dir /data/datascience/.shared/.transformers_cache/

docker run --rm \
    --name DeepSeek-R1-UD-IQ1_S \
    -v /data/datascience/.shared/.transformers_cache:/models \
    -p 8000:8000 \
    ghcr.io/ggerganov/llama.cpp:full \
    --server -m /models/DeepSeek-R1-UD-IQ1_S/DeepSeek-R1-UD-IQ1_S-00001-of-00003.gguf \
    --port 8000 --host 0.0.0.0 \
    -n 4096 --temp 0.6 --ctx-size 4096

# 2.51bit UD-Q2_K_XL 212GB Accuracy: Best
huggingface-cli download unsloth/DeepSeek-R1-GGUF --include "DeepSeek-R1-UD-Q2_K_XL/*" --local-dir /data/datascience/.shared/.transformers_cache/

docker run --rm \
    --name DeepSeek-R1-UD-Q2_K_X \
    -v /data/datascience/.shared/.transformers_cache:/models \
    -p 8000:8000 \
    ghcr.io/ggerganov/llama.cpp:full \
    --server -m /models/DeepSeek-R1-UD-Q2_K_XL/DeepSeek-R1-UD-Q2_K_XL-00001-of-00005.gguf \
    --port 8000 --host 0.0.0.0 \
    -n 4096 --temp 0.6 --ctx-size 4096
```


So what does the command we used actually do? Let's explain a little:

    -m or --model:
    Model path, obviously.

    -co or --color:
    Colorize output to distinguish prompt and user input from generations. Prompt text is dark yellow; user text is green; generated text is white; error text is red.

    -cnv or --conversation:
    Run in conversation mode. The program will apply the chat template accordingly.

    -p or --prompt:
    In conversation mode, it acts as the system message.

    -fa or --flash-attn:
    Enable Flash Attention if the program is compiled with GPU support.

    -ngl or --n-gpu-layers:
    Layers to the GPU for computation if the program is compiled with GPU support.

    -n or --predict:
    Number of tokens to predict.

You can also explore other options by

    ./llama-cli -h


### Environment: 

#### Create the environment: 
```bash
conda create -n=ai_playground python=3.10 -y && conda activate ai_playground && pip install uv

# Install production libs
uv pip install -e .

# Install development libs
uv pip install -e '.[dev,docs]'
```

#### Install crawl4ai
```bash
# https://github.com/unclecode/crawl4ai
uv pip install -U crawl4ai==0.4.248

# Run post-installation setup
crawl4ai-setup 

# (Optional) Skip this step if already installed
# If you encounter any browser-related issues, you can install them manually:
playwright install

# (Optional) Skip this step
sudo yum install -y mesa-libgbm      

# Verify your installation
crawl4ai-doctor
```

#### (AVOID) Install llama.cpp locally
```bash
conda activate ai_playground && sudo yum install -y ccache libgomp ninja-build

# Configura il progetto e prepara i file necessari alla compilazione.
# Compila il codice e genera le librerie necessarie.
# Puoi cancellare llama-cpp-python se hai installato la libreria con uv pip install ., perché i file necessari verranno copiati nel tuo ambiente Python.
git clone --recursive https://github.com/abetlen/llama-cpp-python.git && cd llama-cpp-python
cmake -B build -DGGML_CUDA=ON -DCMAKE_CUDA_ARCHITECTURES=all-major
cmake --build build --config Release -j 4
GGML_CUDA_ENABLE_UNIFIED_MEMORY=1 && uv pip install .


#(NOT WORK)
UV_THREADPOOL_SIZE=4 \
GGML_CUDA_ENABLE_UNIFIED_MEMORY=1 \
CUDACXX=/usr/local/cuda-12/bin/nvcc \
CMAKE_ARGS="-DGGML_CUDA=ON -DCMAKE_CUDA_ARCHITECTURES=all-major" \
FORCE_CMAKE=1 uv pip install . --no-cache-dir --force-reinstall --upgrade
```

- **`-GGML_CUDA_ENABLE_UNIFIED_MEMORY=1`** →  Questa opzione fa sì che la memoria venga **swapata su RAM invece di far crashare il processo quando la VRAM è satura**.
- **`-DGGML_CUDA=ON`** → È il flag più aggiornato per attivare il backend CUDA.
- **`-DCMAKE_CUDA_ARCHITECTURES=80`** → Specifica **Ampere (A100)** per ottimizzare il codice e ridurre la latenza.
- **`FORCE_CMAKE=1`** → Forza la ricompilazione.
- **`--no-cache-dir --force-reinstall --upgrade`** → Pulisce e forza una nuova installazione.



#### Install React packages
    
    npm install --prefix frontend
    
    npm install react-router-dom


#### (EXAMPLE) Install new React Package
    
    npm install --prefix frontend <new_package>

#### Remove environment: 
```bash
conda deactivate && conda remove -n=ai_playground --all -y
```

### Login postgres

Vai qui: http://localhost:5433/ e accedi così, guarda .env per le credenziali complete

![alt text](./.images/postgres.png)

### Launch Documentation

    mkdocs serve --dev-addr=127.0.0.1:8080

### Launch application
```bash
# Nell' .env c'è la porta di default del frontend, ma quella non va tenuta conto, è solo
# per dire che il servizio viene esposto lì (di default è la porta 3000), ma volendo
# potresti modificarla dell' .env

# Per avviare il server di sviluppo di React e vedere le modifiche in tempo reale:
npm --prefix frontend start & python backend/cooper/app.py --mode debug --port 5004

# Qui di seguito invece per distribuire la tua applicazione in produzione. Crea una versione ottimizzata della tua app che può essere servita dal backend.
npm --prefix frontend run build && python backend/cooper/app.py --mode production --port 5003
```

### Launch VLLM embedder

```bash
docker run -itd --rm \
    --gpus '"device=0"' \
    -v /home/<USER>/.transformers_cache:/home/<USER>/.transformers_cache \
    -p 8090:8090 \
    --name multilingual-e5-large-instruct \
    vllm/vllm-openai:latest \
    --model intfloat/multilingual-e5-large-instruct \
    --port 8090 \
    --dtype float32 \
    --tensor-parallel-size 1 \
    --max-model-len 512 \
    --task embed \
    --download-dir home/.shared/.transformers_cache
```

### Launch VLLM transcription
```bash
docker build -t vllm/vllm-openai:v0.9.full \
    -f /home/<USER>/workarea/ai_playground/.dockerfiles/Dockerfile.vllm_full_v0.9 \
    /home/<USER>/workarea/ai_playground/

docker run -itd --rm \
    --gpus '"device=3"' \
    -v /home/<USER>/.transformers_cache:/home/<USER>/.transformers_cache \
    -p 8091:8091 \
    --name whisper-large-v3 \
    vllm/vllm-openai:v0.8.4.full \
    --model openai/whisper-large-v3 \
    --port 8091 \
    --dtype float16 \
    --tensor-parallel-size 1 \
    --task transcription \
    --download-dir /home/<USER>/.transformers_cache
```

## Deploying models

![alt text](.images/0.png)

[qwen2.5 evaluation](https://qwenlm.github.io/blog/qwen2.5-llm/) 

![alt text](.images/1.PNG)

[![qwen2-VL-72B-instuct](.images/qwen2-VL-72B-instuct-model-size.PNG)](https://huggingface.co/Qwen/Qwen2-VL-72B-Instruct-GPTQ-Int8#performance-of-quantized-models)

[![qwen2-VL-72B-instuct](.images/qwen2-VL-72B-instuct-model-evaluation.PNG)](https://huggingface.co/Qwen/Qwen2-VL-72B-Instruct-GPTQ-Int8#speed-benchmark)


## Ollama - Deploying models
Scarica ollama e installa un modello, ad es: Llama-3.2-1B-Instruct-GGUF e Llama-3.2-3B-Instruct-GGUF
```bash
# Download ollama
curl -fsSL https://ollama.com/install.sh | sh

# Download a model
ollama run hf.co/Qwen/Qwen2.5-3B-Instruct-GGUF:Q6_K
ollama run deepseek-r1:32b
ollama run hf.co/bartowski/Ministral-8B-Instruct-2410-GGUF:Q6_K_L
```

## Vllm - Deploying models with docker

### Multimodal models

    Phi-4-multimodal-instruct



### LLM models (no thinking)
```bash
# !!!!! 
# Aspetta il download! Se scarica il modello non lo vedrai nel log. Vai sulla cache per vedere gli aggiornamenti...
# !!!!!

# Opinione: molto buono
docker run --name Llama-3.3-70B-Instruct-FP8-dynamic \
    --runtime nvidia \
    --gpus '"device=0,1"' \
    -v /data/datascience/.shared/.transformers_cache:/data/datascience/.shared/.transformers_cache \
    -p 8081:8081 \
    vllm/vllm-openai:v0.7.1 \
    --model "nm-testing/Llama-3.3-70B-Instruct-FP8-dynamic" \
    --port 8081 \
    --gpu_memory_utilization 0.9 \
    --enable-prefix-caching \
    --max_model_len 30720 \
    --tensor_parallel_size 2 \
    --api-key token-abc123 \
    --download-dir /data/datascience/.shared/.transformers_cache \
    --generation-config auto

# Opinione: dovrebbe essere migliore di Qwen-2.5-32B, da usare per capire meglio
docker run -itd --rm --name Mistral-Small-24B-Instruct-2501-FP8-Dynamic \
    --runtime nvidia \
    --gpus '"device=0"' \
    -v /data/datascience/.shared/.transformers_cache:/data/datascience/.shared/.transformers_cache \
    -p 8081:8081 \
    vllm/vllm-openai:v0.8.4.full \
    --model "neuralmagic/Mistral-Small-24B-Instruct-2501-FP8-Dynamic" \
    --port 8081 \
    --gpu_memory_utilization 0.9 \
    --enable-prefix-caching \
    --max_model_len 6144 \
    --tensor_parallel_size 1 \
    --api-key token-abc123 \
    --download-dir /data/datascience/.shared/.transformers_cache

docker run -itd --rm --name Qwen2.5-Coder-32B-Instruct \
    --runtime nvidia \
    --gpus '"device=1"' \
    -v /data/datascience/.shared/.transformers_cache:/data/datascience/.shared/.transformers_cache \
    -p 8081:8081 \
    vllm/vllm-openai:v0.8.4.full \
    --model "Qwen/Qwen2.5-Coder-32B-Instruct" \
    --port 8081 \
    --gpu_memory_utilization 0.9 \
    --enable-prefix-caching \
    --max_model_len 32768 \
    --tensor_parallel_size 1 \
    --api-key token-abc123 \
    --download-dir /data/datascience/.shared/.transformers_cache
```

### LLM models (thinking)

```bash
## Generation-config
--generation-config

The folder path to the generation config. Defaults to None, no generation config is loaded, vLLM defaults will be used. If set to ‘auto’, the generation config will be loaded from model path. If set to a folder path, the generation config will be loaded from the specified folder path. If max_new_tokens is specified in generation config, then it sets a server-wide limit on the number of output tokens for all requests.

Example of generation_config.json: 
https://huggingface.co/deepseek-ai/DeepSeek-R1-Distill-Llama-70B/blob/main/generation_config.json
```

```bash
# Opinione: non mi convince molto, nonostante ho settato i parametri di temperatura 
# dettati da generation_config.json, non mi soddisfa per lo meno con l'italiano...
docker run -itd --rm --name DeepSeek-R1-Distill-Qwen-32B \
    --runtime nvidia \
    --gpus '"device=0"' \
    -v /data/datascience/.shared/.transformers_cache:/data/datascience/.shared/.transformers_cache \
    -p 8081:8081 \
    vllm/vllm-openai:v0.7.2 \
    --model "deepseek-ai/DeepSeek-R1-Distill-Qwen-32B" \
    --port 8081 \
    --gpu_memory_utilization 0.9 \
    --enable-prefix-caching \
    --max_model_len 6144 \
    --tensor_parallel_size 1 \
    --api-key token-abc123 \
    --download-dir /data/datascience/.shared/.transformers_cache \
    --generation-config auto

# The only true DeepSeek-R1 model is the 671B version
docker run --name DeepSeek-R1 \
    --runtime nvidia \
    --gpus '"device=0,1,2,3"' \
    -v /data/datascience/.shared/.transformers_cache:/data/datascience/.shared/.transformers_cache \
    -p 8081:8081 \
    --privileged \
    --ipc=host \
    -e PYTORCH_CUDA_ALLOC_CONF=expandable_segments:True \
    vllm/vllm-openai:v0.7.1 \
    --model "deepseek-ai/DeepSeek-R1" \
    --port 8081 \
    --gpu_memory_utilization 0.6 \
    --enable-prefix-caching \
    --max_model_len 6144 \
    --tensor_parallel_size 4 \
    --cpu-offload-gb 300 \
    --api-key token-abc123 \
    --download-dir /data/datascience/.shared/.transformers_cache \
    --generation-config auto \
    --trust-remote-code
```

### VLM models

Important info:

- Before and vllm-openai:v0.7.2:
    
    --enable-prefix-caching is currently not supported for multimodal models and has been disabled.

Qwen 2-VL-XB-Instruct
```bash
docker run -itd --rm --name Qwen2-VL-72B-Instruct-GPTQ-Int8 \
    --runtime nvidia \
    --gpus '"device=0,1"' \
    -v /data/datascience/.shared/.transformers_cache:/data/datascience/.shared/.transformers_cache \
    -p 8081:8081 \
    vllm/vllm-openai:latest \
    --model "Qwen/Qwen2-VL-72B-Instruct-GPTQ-Int8" \
    --port 8081 \
    --gpu_memory_utilization 0.9 \
    --tensor_parallel_size 2 \
    --api-key token-abc123 \
    --download-dir /data/datascience/.shared/.transformers_cache

# Install Qwen 2.5-VL-XB-Instruct on vllm-openai:v0.7.2 (maybe later version will be easier)

docker run -itd --rm --name Qwen2.5-VL-7B-Instruct \
    --runtime nvidia \
    --gpus '"device=0"' \
    -v /data/datascience/.shared/.transformers_cache:/data/datascience/.shared/.transformers_cache \
    -p 8081:8081 \
    -e HF_HUB_DOWNLOAD_TIMEOUT=120 \
    vllm/vllm-openai:v0.8.4.full \
    --model "Qwen/Qwen2.5-VL-7B-Instruct" \
    --port 8081 \
    --gpu_memory_utilization 0.9 \
    --max_model_len 30720 \
    --limit-mm-per-prompt image=2 \
    --tensor_parallel_size 1 \
    --api-key token-abc123 \
    --download-dir /data/datascience/.shared/.transformers_cache 

# Qwen2.5-VL-72B-Instruct it doesn't work since cuda max memory error, you need quantization... 
```

## Vllm - Distribute server requests among different models
### START
```bash
docker run -itd --rm --name vllm0 \
    --ipc host \
    --privileged \
    --network vllm_nginx \
    --runtime nvidia \
    --env CUDA_VISIBLE_DEVICES=0 \
    --shm-size=10.24gb \
    -v /data/datascience/.shared/.transformers_cache:/data/datascience/.shared/.transformers_cache \
    -p 8082:8081 \
    vllm/vllm-openai:v0.7.1 \
    --model "Qwen/Qwen2.5-32B-Instruct-GPTQ-Int4" \
    --port 8081 \
    --gpu_memory_utilization 0.9 \
    --enable-prefix-caching \
    --tensor_parallel_size 1 \
    --download-dir /data/datascience/.shared/.transformers_cache

docker run -itd --rm --name vllm1 \
    --ipc host \
    --privileged \
    --network vllm_nginx \
    --runtime nvidia \
    --env CUDA_VISIBLE_DEVICES=1 \
    --shm-size=10.24gb \
    -v /data/datascience/.shared/.transformers_cache:/data/datascience/.shared/.transformers_cache \
    -p 8083:8081 \
    vllm/vllm-openai:v0.7.1 \
    --model "Qwen/Qwen2.5-32B-Instruct-GPTQ-Int4" \
    --port 8081 \
    --gpu_memory_utilization 0.9 \
    --enable-prefix-caching \
    --tensor_parallel_size 1 \
    --download-dir /data/datascience/.shared/.transformers_cache

docker run -itd --rm --name vllm2 \
    --ipc host \
    --privileged \
    --network vllm_nginx \
    --runtime nvidia \
    --env CUDA_VISIBLE_DEVICES=2 \
    --shm-size=10.24gb \
    -v /data/datascience/.shared/.transformers_cache:/data/datascience/.shared/.transformers_cache \
    -p 8084:8081 \
    vllm/vllm-openai:v0.7.1 \
    --model "Qwen/Qwen2.5-32B-Instruct-GPTQ-Int4" \
    --port 8081 \
    --gpu_memory_utilization 0.9 \
    --enable-prefix-caching \
    --tensor_parallel_size 1 \
    --download-dir /data/datascience/.shared/.transformers_cache

docker run -itd --rm --name vllm3 \
    --ipc host \
    --privileged \
    --network vllm_nginx \
    --runtime nvidia \
    --env CUDA_VISIBLE_DEVICES=3 \
    --shm-size=10.24gb \
    -v /data/datascience/.shared/.transformers_cache:/data/datascience/.shared/.transformers_cache \
    -p 8085:8081 \
    vllm/vllm-openai:v0.7.1 \
    --model "Qwen/Qwen2.5-32B-Instruct-GPTQ-Int4" \
    --port 8081 \
    --gpu_memory_utilization 0.9 \
    --enable-prefix-caching \
    --tensor_parallel_size 1 \
    --download-dir /data/datascience/.shared/.transformers_cache

docker run -itd \
    --rm \
    --network vllm_nginx \
    -p 8081:8080 \
    -v "$HOME/workarea/ai_playground/backend/cooper/nginx_conf/:/etc/nginx/conf.d/" \
    --name nginx-lb \
    nginx-lb:latest
```

### STOP
```bash
docker stop vllm0 vllm1 vllm2 vllm3 nginx-lb