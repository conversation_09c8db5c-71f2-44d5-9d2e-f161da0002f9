config_engine_llm: /home/<USER>/workarea/ai_playground/backend/cooper/api/llm/benchmark/config_engine_llm.yml
template_module_path: /home/<USER>/workarea/ai_playground/backend/cooper/api/llm/template_prompts/chat_inference.py
document_input_file: /home/<USER>/workarea/ai_playground/backend/cooper/api/llm/benchmark/assets/name_recognition/chat_data.parquet
guided_decoding_backend: null
results_dir: /home/<USER>/workarea/ai_playground/backend/cooper/api/llm/benchmark/assets/name_recognition
server_log: /home/<USER>/workarea/ai_playground/backend/cooper/api/llm/benchmark/.log
max_concurrent_requests: 1000  # Numero massimo di richieste attive contemporaneamente ## NEW
batch_size: 1000
use_stream: false
load_balancer_url: http://localhost:5000/v1/chat/completions
role_column: ROLE # column name: null | str: - column content: str: user / system
order_column: ORDER # column name: null | str: - column content: int: 0,1,...
text_columns: 
  - CONTENT
id_columns:
  - ID_USER
  - ID_CHAT
n_rows: 5000
is_benchmark: true # if not you'll skip all files already parsed