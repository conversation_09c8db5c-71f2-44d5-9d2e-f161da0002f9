from __future__ import annotations

import asyncio
import os

from dotenv import load_dotenv
from openai import AsyncOpenAI
from pydub import AudioSegment

from cooper.utils import get_logger

load_dotenv()

logger = get_logger(__name__)

# NOTA: timestamp non è ancora supportato con VLLM
# NOTA: timestamp non è ancora supportato con VLLM

WHISPER_PORT = os.getenv("WHISPER_PORT")
WHISPER_MODEL = os.getenv("WHISPER_MODEL")


class AudioTranscriber:
    def __init__(
        self,
        base_url: str | None = None,
        model: str | None = "openai/whisper-large-v3",
        batch_size=24,
        chunk_length_s=30,
    ):
        if base_url is None:
            base_url = f"http://localhost:{WHISPER_PORT}/v1"

        if model is None:
            model = WHISPER_MODEL

        """Inizializza il client per la trascrizione audio."""
        self.client = AsyncOpenAI(base_url=base_url, api_key="not-needed")
        self.model = model
        self.batch_size = batch_size
        self.chunk_length_ms = chunk_length_s * 1000  # Converti in millisecondi

    async def transcribe_chunk(self, idx: int, chunk: AudioSegment):
        """Trascrive un singolo chunk e restituisce il testo + timestamp."""
        temp_filename = f"chunk_{idx}.flac"
        chunk.export(temp_filename, format="flac")

        with open(temp_filename, "rb") as audio_file:
            audio_bytes = audio_file.read()

        os.remove(temp_filename)  # Elimina il file temporaneo dopo l'uso

        try:
            response = await self.client.audio.transcriptions.create(
                model=self.model,
                file=("audio.flac", audio_bytes, "audio/flac"),
                response_format="json",  # Necessario per i timestamp
                language="it",
                temperature=0.0,
            )
            return response.text
        except Exception as e:
            logger.error("Errore nel chunk %s: %s", idx, e)
            return ""

    async def transcribe_audio(self, file_path: str):
        """Divide l'audio in chunk e li processa in parallelo con batching."""
        audio = AudioSegment.from_file(file_path)

        # Suddivide l'audio in chunk della lunghezza specificata
        chunks = [
            audio[i : i + self.chunk_length_ms]
            for i in range(0, len(audio), self.chunk_length_ms)
        ]

        all_segments = []
        for i in range(0, len(chunks), self.batch_size):
            # Seleziona i chunk per il batch attuale
            batch = chunks[i : i + self.batch_size]

            # Processa i chunk in parallelo
            batch_segments = await asyncio.gather(
                *[self.transcribe_chunk(i + j, chunk) for j, chunk in enumerate(batch)]
            )

            # Unisce i risultati
            all_segments.extend(batch_segments)

        return " ".join(all_segments)


# Esempio di utilizzo
if __name__ == "__main__":

    async def main():
        import time

        file_name = "/home/<USER>/workarea/ai_playground/.tmp/sam_altman_lex_podcast_367.flac"

        transcriber = AudioTranscriber()

        start_time = time.time()
        result = await transcriber.transcribe_audio(file_name)
        end_time = time.time()

        logger.info(result)
        logger.info("Tempo di inferenza totale: %.2f secondi", (end_time - start_time))

    asyncio.run(main())
