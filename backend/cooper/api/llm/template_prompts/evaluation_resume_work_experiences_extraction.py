from __future__ import annotations

from pydantic import BaseModel, Field


def get_default_system_prompt():
    return """
    You are an expert in analyzing resumes and evaluating the quality of automated data extraction. Your task is to assess the accuracy and completeness of the **work experience data** extracted from resumes by a specific product. I will provide you with two inputs:

    1. A **resume** in text format, which may have formatting issues (e.g., layout errors, special characters, or structural inconsistencies).  
    2. A **JSON file** representing the automatically extracted work experience data.

    Your analysis should include the following:

    ### 1. Data Comparison
    - **Missing Fields:** Identify work experience details present in the resume but not captured in the JSON, such as company names, job titles, job descriptions, skills (hard and soft), dates, or other attributes.
    - **Extraction Errors:** Highlight inaccuracies in the extracted data (e.g., incorrect company names, job titles, years, or skill associations).
    - **Inaccuracies:** Identify partially correct or incomplete extractions (e.g., truncated job descriptions or miscategorized skills).
    - **Field Consistency:** Assess whether the extracted fields (e.g., company names, job titles) align with the structure and terminology used in the resume.

    ### 2. Overall Evaluation
    Evaluate the overall extraction quality based on the following criteria:
    - **Accuracy:** How correct the extracted data is compared to the original resume.  
    - **Completeness:** How much of the relevant work experience information has been successfully extracted.  
    - **Robustness:** The product's ability to handle challenges such as layout inconsistencies, formatting errors, or ambiguous information in the resume.

    ### 3. Scoring and Feedback
    Provide:
    - A **score from 1 to 10** representing the overall extraction quality, with 10 being perfect extraction and 1 indicating severe issues.  
    - A clear explanation for your score, supported by examples from your analysis.  
    - Specific **suggestions for improvement** to enhance extraction quality.

    ### Expected Outcome
    - A detailed comparison highlighting errors, omissions, and inconsistencies in the extraction.  
    - A clear rationale for the assigned score.  
    - Actionable feedback to improve the product's data extraction capabilities.      
    """


class EvaluationDetail(BaseModel):
    score: int = Field(..., ge=0, le=10, description="Score from 0 to 10")
    description: str = Field(..., description="Brief explanation for the given score")


class ExtractionEvaluation(BaseModel):
    accuracy: EvaluationDetail = Field(
        ...,
        description="Evaluation of how correct the extracted data is compared to the resume",
    )
    completeness: EvaluationDetail = Field(
        ...,
        description="Evaluation of how much of the resume content has been successfully extracted",
    )
    robustness: EvaluationDetail = Field(
        ...,
        description="Evaluation of the product's ability to handle formatting or layout challenges",
    )
    overall: EvaluationDetail = Field(
        ..., description="Overall score and justification"
    )


class ResumeExtractionAssessment(BaseModel):
    comparison_summary: str = Field(
        ...,
        description="Detailed markdown-formatted description of the comparison between resume and extracted data",
    )
    evaluation: ExtractionEvaluation = Field(
        ...,
        description="Evaluation of the overall extraction quality based on defined criteria",
    )


ResumeExtractionAssessment.model_rebuild()


def get_schema():
    return ResumeExtractionAssessment.model_json_schema()


if __name__ == "__main__":
    # Example instantiation (replace with actual data during implementation):
    example_data = {
        "comparison_summary": """ 
        ### Comparison Summary
        - **Missing Fields:**
          - Phone Number
          - Education Details
        - **Extraction Errors:**
          - Name extracted incorrectly as 'John D' instead of 'John Doe'
        - **Inaccuracies:**
          - Job title partially extracted as 'Software' instead of 'Software Engineer'
        - **Formatting Issues:**
          - Dates formatted inconsistently compared to resume
        """,
        "evaluation": {
            "accuracy": {
                "score": 7,
                "description": "Most data was correct, but some key fields were inaccurate",
            },
            "completeness": {
                "score": 6,
                "description": "Several fields were missing from the extraction",
            },
            "robustness": {
                "score": 8,
                "description": "Handled layout challenges well, but struggled with minor formatting issues",
            },
            "overall": {
                "score": 7,
                "description": "Good performance overall, but improvements needed in accuracy and completeness",
            },
        },
    }

    # Create an instance of the model with example data
    response = ResumeExtractionAssessment(**example_data)
    print(response.model_dump_json(indent=4))
