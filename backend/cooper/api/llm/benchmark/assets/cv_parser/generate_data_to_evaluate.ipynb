{"cells": [{"cell_type": "code", "execution_count": 13, "metadata": {}, "outputs": [], "source": ["import polars as pl\n", "from pathlib import Path\n", "import pandas as pd\n", "import json\n", "import os\n", "from tqdm import tqdm"]}, {"cell_type": "code", "execution_count": 17, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>response</th>\n", "      <th>DATABASE</th>\n", "      <th>EMPLOYEE_ID</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>{'educationHistory': [{'degreeDirection': 'Tec...</td>\n", "      <td>ITALIA</td>\n", "      <td>2995251</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>{'educationHistory': [{'degreeDirection': 'DIP...</td>\n", "      <td>ITALIA</td>\n", "      <td>2994920</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>{'educationHistory': [{'degreeDirection': 'Scu...</td>\n", "      <td>ITALIA</td>\n", "      <td>2995333</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>{'educationHistory': [{'degreeDirection': 'Dip...</td>\n", "      <td>ITALIA</td>\n", "      <td>2995139</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>{'educationHistory': [{'degreeDirection': 'Sci...</td>\n", "      <td>ITALIA</td>\n", "      <td>2997997</td>\n", "    </tr>\n", "    <tr>\n", "      <th>5</th>\n", "      <td>{'educationHistory': [{'degreeDirection': 'Tec...</td>\n", "      <td>ITALIA</td>\n", "      <td>2995503</td>\n", "    </tr>\n", "    <tr>\n", "      <th>6</th>\n", "      <td>{'educationHistory': [{'degreeDirection': 'Amm...</td>\n", "      <td>ITALIA</td>\n", "      <td>2996891</td>\n", "    </tr>\n", "    <tr>\n", "      <th>7</th>\n", "      <td>{'educationHistory': [{'degreeDirection': 'Dip...</td>\n", "      <td>ITALIA</td>\n", "      <td>2997285</td>\n", "    </tr>\n", "    <tr>\n", "      <th>8</th>\n", "      <td>{'educationHistory': [{'degreeDirection': 'Dip...</td>\n", "      <td>ITALIA</td>\n", "      <td>2998801</td>\n", "    </tr>\n", "    <tr>\n", "      <th>9</th>\n", "      <td>{'educationHistory': [{'degreeDirection': 'Lic...</td>\n", "      <td>ITALIA</td>\n", "      <td>2998489</td>\n", "    </tr>\n", "    <tr>\n", "      <th>10</th>\n", "      <td>{'educationHistory': [{'degreeDirection': 'Pro...</td>\n", "      <td>ITALIA</td>\n", "      <td>2998675</td>\n", "    </tr>\n", "    <tr>\n", "      <th>11</th>\n", "      <td>{'educationHistory': [{'degreeDirection': 'Lan...</td>\n", "      <td>ITALIA</td>\n", "      <td>2999253</td>\n", "    </tr>\n", "    <tr>\n", "      <th>12</th>\n", "      <td>{'educationHistory': [{'degreeDirection': 'Dip...</td>\n", "      <td>ITALIA</td>\n", "      <td>2999737</td>\n", "    </tr>\n", "    <tr>\n", "      <th>13</th>\n", "      <td>{'educationHistory': [{'degreeDirection': 'Lic...</td>\n", "      <td>ITALIA</td>\n", "      <td>2999623</td>\n", "    </tr>\n", "    <tr>\n", "      <th>14</th>\n", "      <td>{'educationHistory': [{'degreeDirection': 'Tur...</td>\n", "      <td>ITALIA</td>\n", "      <td>2999783</td>\n", "    </tr>\n", "    <tr>\n", "      <th>15</th>\n", "      <td>{'educationHistory': [{'degreeDirection': 'Cuo...</td>\n", "      <td>ITALIA</td>\n", "      <td>3000263</td>\n", "    </tr>\n", "    <tr>\n", "      <th>16</th>\n", "      <td>{'educationHistory': [{'degreeDirection': 'Let...</td>\n", "      <td>ITALIA</td>\n", "      <td>3001941</td>\n", "    </tr>\n", "    <tr>\n", "      <th>17</th>\n", "      <td>{'educationHistory': [{'degreeDirection': 'Ope...</td>\n", "      <td>ITALIA</td>\n", "      <td>3000649</td>\n", "    </tr>\n", "    <tr>\n", "      <th>18</th>\n", "      <td>{'educationHistory': [{'degreeDirection': 'Dip...</td>\n", "      <td>ITALIA</td>\n", "      <td>3003549</td>\n", "    </tr>\n", "    <tr>\n", "      <th>19</th>\n", "      <td>{'educationHistory': [{'degreeDirection': 'pit...</td>\n", "      <td>ITALIA</td>\n", "      <td>3001027</td>\n", "    </tr>\n", "    <tr>\n", "      <th>20</th>\n", "      <td>{'educationHistory': [{'degreeDirection': None...</td>\n", "      <td>ITALIA</td>\n", "      <td>3004097</td>\n", "    </tr>\n", "    <tr>\n", "      <th>21</th>\n", "      <td>{'educationHistory': [{'degreeDirection': 'Ope...</td>\n", "      <td>ITALIA</td>\n", "      <td>3005257</td>\n", "    </tr>\n", "    <tr>\n", "      <th>22</th>\n", "      <td>{'educationHistory': [{'degreeDirection': 'Dip...</td>\n", "      <td>ITALIA</td>\n", "      <td>3005015</td>\n", "    </tr>\n", "    <tr>\n", "      <th>23</th>\n", "      <td>{'educationHistory': [{'degreeDirection': 'OPE...</td>\n", "      <td>ITALIA</td>\n", "      <td>3004757</td>\n", "    </tr>\n", "    <tr>\n", "      <th>24</th>\n", "      <td>{'educationHistory': [{'degreeDirection': 'aer...</td>\n", "      <td>ITALIA</td>\n", "      <td>3005477</td>\n", "    </tr>\n", "    <tr>\n", "      <th>25</th>\n", "      <td>{'educationHistory': [{'degreeDirection': 'Eco...</td>\n", "      <td>ITALIA</td>\n", "      <td>3005737</td>\n", "    </tr>\n", "    <tr>\n", "      <th>26</th>\n", "      <td>{'educationHistory': [{'degreeDirection': 'FIN...</td>\n", "      <td>ITALIA</td>\n", "      <td>3005433</td>\n", "    </tr>\n", "    <tr>\n", "      <th>27</th>\n", "      <td>{'educationHistory': [{'degreeDirection': 'DIP...</td>\n", "      <td>ITALIA</td>\n", "      <td>3005667</td>\n", "    </tr>\n", "    <tr>\n", "      <th>28</th>\n", "      <td>{'educationHistory': [{'degreeDirection': 'Bio...</td>\n", "      <td>ITALIA</td>\n", "      <td>3007083</td>\n", "    </tr>\n", "    <tr>\n", "      <th>29</th>\n", "      <td>{'educationHistory': [{'degreeDirection': 'Sci...</td>\n", "      <td>ITALIA</td>\n", "      <td>3007315</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["                                             response DATABASE  EMPLOYEE_ID\n", "0   {'educationHistory': [{'degreeDirection': 'Tec...   ITALIA      2995251\n", "1   {'educationHistory': [{'degreeDirection': 'DIP...   ITALIA      2994920\n", "2   {'educationHistory': [{'degreeDirection': 'Scu...   ITALIA      2995333\n", "3   {'educationHistory': [{'degreeDirection': 'Dip...   ITALIA      2995139\n", "4   {'educationHistory': [{'degreeDirection': 'Sci...   ITALIA      2997997\n", "5   {'educationHistory': [{'degreeDirection': 'Tec...   ITALIA      2995503\n", "6   {'educationHistory': [{'degreeDirection': 'Amm...   ITALIA      2996891\n", "7   {'educationHistory': [{'degreeDirection': 'Dip...   ITALIA      2997285\n", "8   {'educationHistory': [{'degreeDirection': 'Dip...   ITALIA      2998801\n", "9   {'educationHistory': [{'degreeDirection': 'Lic...   ITALIA      2998489\n", "10  {'educationHistory': [{'degreeDirection': 'Pro...   ITALIA      2998675\n", "11  {'educationHistory': [{'degreeDirection': 'Lan...   ITALIA      2999253\n", "12  {'educationHistory': [{'degreeDirection': 'Dip...   ITALIA      2999737\n", "13  {'educationHistory': [{'degreeDirection': 'Lic...   ITALIA      2999623\n", "14  {'educationHistory': [{'degreeDirection': 'Tur...   ITALIA      2999783\n", "15  {'educationHistory': [{'degreeDirection': 'Cuo...   ITALIA      3000263\n", "16  {'educationHistory': [{'degreeDirection': 'Let...   ITALIA      3001941\n", "17  {'educationHistory': [{'degreeDirection': 'Ope...   ITALIA      3000649\n", "18  {'educationHistory': [{'degreeDirection': 'Dip...   ITALIA      3003549\n", "19  {'educationHistory': [{'degreeDirection': 'pit...   ITALIA      3001027\n", "20  {'educationHistory': [{'degreeDirection': None...   ITALIA      3004097\n", "21  {'educationHistory': [{'degreeDirection': 'Ope...   ITALIA      3005257\n", "22  {'educationHistory': [{'degreeDirection': 'Dip...   ITALIA      3005015\n", "23  {'educationHistory': [{'degreeDirection': 'OPE...   ITALIA      3004757\n", "24  {'educationHistory': [{'degreeDirection': 'aer...   ITALIA      3005477\n", "25  {'educationHistory': [{'degreeDirection': 'Eco...   ITALIA      3005737\n", "26  {'educationHistory': [{'degreeDirection': 'FIN...   ITALIA      3005433\n", "27  {'educationHistory': [{'degreeDirection': 'DIP...   ITALIA      3005667\n", "28  {'educationHistory': [{'degreeDirection': 'Bio...   ITALIA      3007083\n", "29  {'educationHistory': [{'degreeDirection': 'Sci...   ITALIA      3007315"]}, "execution_count": 17, "metadata": {}, "output_type": "execute_result"}], "source": ["# Directory contenente i file JSON\n", "directory = './.results_struct_xgrammar'\n", "\n", "# Lista per memorizzare i dati\n", "data = []\n", "\n", "# Itera su tutti i file nella directory\n", "for filename in os.listdir(directory):\n", "    if filename.endswith(\".json\"):\n", "        filepath = os.path.join(directory, filename)\n", "        with open(filepath, 'r') as f:\n", "            try:\n", "                # Carica il file JSON\n", "                json_data = json.load(f)\n", "                data.append(json_data)\n", "            except json.JSONDecodeError:\n", "                print(f\"Errore nel parsing del file {filename}\")\n", "\n", "# Converti i dati in un dataframe\n", "df = pd.DataFrame(data)\n", "\n", "df[[\"DATABASE\", \"EMPLOYEE_ID\"]] = df[\"document_id\"].str.split(\"_\", expand=True)\n", "df[\"EMPLOYEE_ID\"] = df[\"EMPLOYEE_ID\"].astype(int)\n", "df = df[[\"response\", \"DATABASE\", \"EMPLOYEE_ID\"]]\n", "df"]}, {"cell_type": "code", "execution_count": 19, "metadata": {}, "outputs": [], "source": ["cv_all_country_file = Path(\n", "    \"/data/datascience/cv_extract_info/datasets/tk_cv_sample.parquet\"\n", ")"]}, {"cell_type": "code", "execution_count": 20, "metadata": {}, "outputs": [], "source": ["df_cv = pl.scan_parquet(cv_all_country_file).head(800).collect().to_pandas()"]}, {"cell_type": "code", "execution_count": 22, "metadata": {}, "outputs": [{"data": {"text/plain": ["Index(['CV_EMPLOYEE_EMAIL', 'CV_LATEST_CREATED_AT', 'DATABASE', 'EMPLOYEE_ID',\n", "       'CV_FILE_ID', 'CVI_CANDIDATE_CV_CLEANED', 'CV_CONTENT_PROCESSED',\n", "       'text_kernel_id', 'text_kernel_path', 'text_kernel_email',\n", "       'text_kernel_creations', '_id', '_source',\n", "       'WORK_EXPERIENCES_EXTRACTED'],\n", "      dtype='object')"]}, "execution_count": 22, "metadata": {}, "output_type": "execute_result"}], "source": ["df_cv.columns"]}, {"cell_type": "code", "execution_count": 23, "metadata": {}, "outputs": [{"data": {"text/plain": ["0      {'educationHistory': [{'degreeDirection': 'DIP...\n", "1      {'educationHistory': [{'degreeDirection': '', ...\n", "2      {'educationHistory': [{'degreeDirection': 'Dip...\n", "3      {'educationHistory': [{'degreeDirection': '', ...\n", "4      {'educationHistory': [{'degreeDirection': 'Ele...\n", "                             ...                        \n", "795    {'educationHistory': [{'degreeDirection': 'RSP...\n", "796    {'educationHistory': [{'degreeDirection': '\"Sc...\n", "797    {'educationHistory': [{'degreeDirection': '', ...\n", "798    {'educationHistory': [{'degreeDirection': 'dip...\n", "799    {'educationHistory': [{'degreeDirection': 'Dip...\n", "Name: _source, Length: 800, dtype: object"]}, "execution_count": 23, "metadata": {}, "output_type": "execute_result"}], "source": ["def map_json_structure(input_json):\n", "    # Copia dell'input per mantenere l'originale intatto\n", "\n", "    output_json = input_json[\"cv\"].copy()\n", "\n", "    if \"documentHtml\" in output_json:\n", "        del output_json[\"documentHtml\"]\n", "\n", "    if \"documentText\" in output_json:\n", "        del output_json[\"documentText\"]\n", "    \n", "    if \"educationHistory\" in output_json:\n", "        for item in output_json[\"educationHistory\"]:\n", "            # DELETE ITEMS\n", "            if \"diplomaCode\" in item:\n", "                del item[\"diplomaCode\"]\n", "            \n", "            if \"diplomaCodeDescription\" in item:\n", "                del item[\"diplomaCodeDescription\"]\n", "            \n", "            if \"levelCode\" in item:\n", "                del item[\"levelCode\"]\n", "            \n", "            if \"levelCodeDE\" in item:\n", "                del item[\"levelCodeDE\"]\n", "            \n", "            # RENAME ITEMS\n", "            if \"gradePointAverage\" in item:\n", "                item[\"grade\"] = item.pop(\"gradePointAverage\")\n", "    \n", "    if \"employmentHistory\" in output_json:\n", "        for item in output_json[\"employmentHistory\"]:\n", "            # DELETE ITEMS\n", "            if \"jobClass\" in item:\n", "                del item[\"jobClass\"]\n", "\n", "            if \"jobClassDescription\" in item:\n", "                del item[\"jobClassDescription\"]\n", "\n", "            if \"jobCode\" in item:\n", "                del item[\"jobCode\"]\n", "\n", "            if \"jobCodeDescription\" in item:\n", "                del item[\"jobCodeDescription\"]\n", "\n", "            if \"jobGroup\" in item:\n", "                del item[\"jobGroup\"]\n", "\n", "            if \"jobGroupDescription\" in item:\n", "                del item[\"jobGroupDescription\"]\n", "\n", "            if \"jobTypes\" in item:\n", "                del item[\"jobTypes\"]\n", "\n", "    if \"other\" in output_json:\n", "        if \"hobbies\" in output_json[\"other\"]:\n", "            output_json[\"other\"][\"hobbies\"] = [\n", "                item[\"hobby\"] for item in output_json[\"other\"][\"hobbies\"] if \"hobby\" in item\n", "            ]\n", "\n", "    if \"personal\" in output_json:\n", "        if \"address\" in output_json[\"personal\"]:\n", "            address = output_json[\"personal\"][\"address\"]\n", "            output_json[\"personal\"][\"address\"] = {\n", "                \"street\": f\"{address.get('streetName', '')} {address.get('streetNumberBase', '')}\".strip(),\n", "                \"city\": address.get(\"city\", \"\"),\n", "                \"region\": address.get(\"region\", {}).get(\"codeDescription\", \"\"),\n", "                \"country\": address.get(\"country\", {}).get(\"codeDescription\", \"\"),\n", "                \"postalCode\": address.get(\"postalCode\", \"\")\n", "            }\n", "\n", "\n", "        if \"email\" in output_json[\"personal\"]:\n", "            output_json[\"personal\"][\"email\"] = [\n", "                item[\"email\"] for item in output_json[\"personal\"][\"email\"] if \"email\" in item\n", "            ]\n", "\n", "        if \"gender\" in output_json[\"personal\"]:\n", "            output_json[\"personal\"][\"gender\"] = output_json[\"personal\"][\"gender\"].get(\"codeDescription\", \"Unknown\")\n", "\n", "\n", "        if \"homePhone\" in output_json[\"personal\"]:\n", "            output_json[\"personal\"][\"homePhone\"] = [\n", "                item[\"phone\"] for item in output_json[\"personal\"][\"homePhone\"] if \"phone\" in item\n", "            ]\n", "\n", "        if \"mobilePhone\" in output_json[\"personal\"]:\n", "            output_json[\"personal\"][\"mobilePhone\"] = [\n", "                item[\"phone\"] for item in output_json[\"personal\"][\"mobilePhone\"] if \"phone\" in item\n", "            ]\n", "\n", "\n", "    if \"skills\" in output_json:\n", "        if \"computerSkill\" in output_json[\"skills\"]:\n", "            output_json[\"skills\"][\"computerSkill\"] = [\n", "                item[\"name\"] for item in output_json[\"skills\"][\"computerSkill\"] if \"name\" in item\n", "            ]\n", "\n", "        if \"languageSkill\" in output_json[\"skills\"]:\n", "            output_json[\"skills\"][\"languageSkill\"] = [\n", "                {\n", "                    \"language\": item[\"languageCodeDescription\"],\n", "                    \"level\": item[\"proficiencyCodeDescription\"]\n", "                }\n", "                for item in output_json[\"skills\"][\"languageSkill\"]\n", "                if \"languageCodeDescription\" in item and \"proficiencyCodeDescription\" in item\n", "            ]\n", "        \n", "        if \"otherSkill\" in output_json[\"skills\"]:\n", "            output_json[\"skills\"][\"otherSkill\"] = [\n", "                item[\"name\"] for item in output_json[\"skills\"][\"otherSkill\"] if \"name\" in item\n", "            ]\n", "        \n", "        if \"softSkill\" in output_json[\"skills\"]:\n", "            output_json[\"skills\"][\"softSkill\"] = [\n", "                item[\"name\"] for item in output_json[\"skills\"][\"softSkill\"] if \"name\" in item\n", "            ]\n", "\n", "    if \"skillsToMap\" in output_json:\n", "        del output_json[\"skillsToMap\"]\n", "\n", "    if \"summary\" in output_json:\n", "        del output_json[\"summary\"]\n", "\n", "    return output_json\n", "\n", "# mapped_json = map_json_structure(json_loaded)\n", "# pretty_json = json.dumps(mapped_json, indent=4, ensure_ascii=False)\n", "# print(pretty_json)\n", "\n", "df_cv[\"_source\"] = df_cv[\"_source\"].apply(map_json_structure)\n", "df_cv[\"_source\"]"]}, {"cell_type": "code", "execution_count": 24, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th>response</th>\n", "      <th>CVI_CANDIDATE_CV_CLEANED</th>\n", "      <th>_source</th>\n", "    </tr>\n", "    <tr>\n", "      <th>DATABASE</th>\n", "      <th>EMPLOYEE_ID</th>\n", "      <th>CV_FILE_ID</th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th rowspan=\"30\" valign=\"top\">ITALIA</th>\n", "      <th>2995251</th>\n", "      <th>19657775</th>\n", "      <td>{'educationHistory': [{'degreeDirection': 'Tec...</td>\n", "      <td>Curriculum vitae \\n\\n\\nINFORMAZIONI PERSONALI ...</td>\n", "      <td>{'educationHistory': [{'degreeDirection': 'Dip...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2994920</th>\n", "      <th>14910697</th>\n", "      <td>{'educationHistory': [{'degreeDirection': 'DIP...</td>\n", "      <td>Formato europeo per il curriculum vitae\\n\\n\\nI...</td>\n", "      <td>{'educationHistory': [{'degreeDirection': 'DIP...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2995333</th>\n", "      <th>13487937</th>\n", "      <td>{'educationHistory': [{'degreeDirection': 'Scu...</td>\n", "      <td>Nome: <PERSON> \\n\\nCognome: <PERSON><PERSON><PERSON> \\n\\nLuogo ...</td>\n", "      <td>{'educationHistory': [{'degreeDirection': '', ...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2995139</th>\n", "      <th>20311079</th>\n", "      <td>{'educationHistory': [{'degreeDirection': 'Dip...</td>\n", "      <td>Con la seguente, ai sensi del Regolamento UE 2...</td>\n", "      <td>{'educationHistory': [{'degreeDirection': '', ...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2997997</th>\n", "      <th>15780943</th>\n", "      <td>{'educationHistory': [{'degreeDirection': 'Sci...</td>\n", "      <td>GIORGIAGUADALUPI \\nADDETTO ALL’AMMINISTRAZIONE...</td>\n", "      <td>{'educationHistory': [{'degreeDirection': '', ...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2995503</th>\n", "      <th>19059437</th>\n", "      <td>{'educationHistory': [{'degreeDirection': 'Tec...</td>\n", "      <td><PERSON>\\nRoveda 13/B\\ntorino\\n10136\\n3889...</td>\n", "      <td>{'educationHistory': [{'degreeDirection': 'Ele...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2996891</th>\n", "      <th>16781987</th>\n", "      <td>{'educationHistory': [{'degreeDirection': 'Amm...</td>\n", "      <td>Curriculum Vitae ANA DRACINSCHI \\n\\n\\nINFORMAZ...</td>\n", "      <td>{'educationHistory': [{'degreeDirection': 'Cor...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2997285</th>\n", "      <th>13765671</th>\n", "      <td>{'educationHistory': [{'degreeDirection': 'Dip...</td>\n", "      <td>!\\n\\nEsperienze Lavorative\\n\\nIstruzione e For...</td>\n", "      <td>{'educationHistory': [{'degreeDirection': 'Cor...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2998801</th>\n", "      <th>15437879</th>\n", "      <td>{'educationHistory': [{'degreeDirection': 'Dip...</td>\n", "      <td>Nome: <PERSON> \\r\\nCognome: Libero \\r\\nEmail: lib...</td>\n", "      <td>{'educationHistory': [{'degreeDirection': '', ...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2998489</th>\n", "      <th>19511293</th>\n", "      <td>{'educationHistory': [{'degreeDirection': 'Lic...</td>\n", "      <td>22/08/2023 © Unione europea, 2002-2019 | http:...</td>\n", "      <td>{'educationHistory': [{'degreeDirection': 'TEC...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2998675</th>\n", "      <th>20380853</th>\n", "      <td>{'educationHistory': [{'degreeDirection': 'Pro...</td>\n", "      <td><PERSON><PERSON>\\nSenna <PERSON>, Lombardi...</td>\n", "      <td>{'educationHistory': [{'degreeDirection': 'Qua...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2999253</th>\n", "      <th>15282423</th>\n", "      <td>{'educationHistory': [{'degreeDirection': 'Lan...</td>\n", "      <td>C1 - Internal use \\n\\n+39 3497754049\\n\\nmartin...</td>\n", "      <td>{'educationHistory': [{'degreeDirection': 'POS...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2999737</th>\n", "      <th>15089913</th>\n", "      <td>{'educationHistory': [{'degreeDirection': 'Dip...</td>\n", "      <td>Curriculum Vitae ALESSANDRO PASQUA \\n\\n © Unio...</td>\n", "      <td>{'educationHistory': [{'degreeDirection': 'Dip...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2999623</th>\n", "      <th>15679795</th>\n", "      <td>{'educationHistory': [{'degreeDirection': 'Lic...</td>\n", "      <td>Curriculum Vitae Ndiaye Nafissatou \\n\\n © Unio...</td>\n", "      <td>{'educationHistory': [{'degreeDirection': 'Lic...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2999783</th>\n", "      <th>15440379</th>\n", "      <td>{'educationHistory': [{'degreeDirection': 'Tur...</td>\n", "      <td>A2\\n\\nA1\\n\\nA2\\n\\nPROFILO\\nPROFESSIONALE\\n\\nAs...</td>\n", "      <td>{'educationHistory': [{'degreeDirection': 'Ass...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3000263</th>\n", "      <th>19857769</th>\n", "      <td>{'educationHistory': [{'degreeDirection': 'Cuo...</td>\n", "      <td>FORMATO EUROPEO \\nPER IL CURRICULUM \\n\\nVITAE ...</td>\n", "      <td>{'educationHistory': [{'degreeDirection': 'Cuo...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3001941</th>\n", "      <th>19059201</th>\n", "      <td>{'educationHistory': [{'degreeDirection': 'Let...</td>\n", "      <td>Ricerca e selezione candidati\\nElaborazione e ...</td>\n", "      <td>{'educationHistory': [{'degreeDirection': 'Lau...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3000649</th>\n", "      <th>14173165</th>\n", "      <td>{'educationHistory': [{'degreeDirection': 'Ope...</td>\n", "      <td>© Unione europea, 2002-2013 | hiip://europass....</td>\n", "      <td>{'educationHistory': [{'degreeDirection': 'Seg...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3003549</th>\n", "      <th>19573059</th>\n", "      <td>{'educationHistory': [{'degreeDirection': 'Dip...</td>\n", "      <td>PRESENTAZIONE\\nEsperienza lavorativa della dur...</td>\n", "      <td>{'educationHistory': [{'degreeDirection': 'Cer...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3001027</th>\n", "      <th>15943739</th>\n", "      <td>{'educationHistory': [{'degreeDirection': 'pit...</td>\n", "      <td>PROFILO PROFESSIONALE \\nSono una persona adatt...</td>\n", "      <td>{'educationHistory': [{'degreeDirection': '', ...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3004097</th>\n", "      <th>15998127</th>\n", "      <td>{'educationHistory': [{'degreeDirection': None...</td>\n", "      <td>Nome: Chiara\\nCognome: <PERSON><PERSON><PERSON>\\nData di nasci...</td>\n", "      <td>{'educationHistory': [{'degreeDirection': '', ...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3005257</th>\n", "      <th>17085065</th>\n", "      <td>{'educationHistory': [{'degreeDirection': 'Ope...</td>\n", "      <td>DATI ANAGRAFICI \\n\\nCognome: IZZO Residenza: R...</td>\n", "      <td>{'educationHistory': [{'degreeDirection': 'Ope...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3005015</th>\n", "      <th>14216779</th>\n", "      <td>{'educationHistory': [{'degreeDirection': 'Dip...</td>\n", "      <td>PRESENTAZIONE\\nSalve, vorrei sottoporre alla v...</td>\n", "      <td>{'educationHistory': [{'degreeDirection': 'Dip...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3004757</th>\n", "      <th>15404483</th>\n", "      <td>{'educationHistory': [{'degreeDirection': 'OPE...</td>\n", "      <td>��������\\t\\n�����\\n\\n�\\n�����\\n\\n�\\n\\n�\\n\\n�\\n...</td>\n", "      <td>{'educationHistory': [{'degreeDirection': '', ...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3005477</th>\n", "      <th>15492501</th>\n", "      <td>{'educationHistory': [{'degreeDirection': 'aer...</td>\n", "      <td>Manal Q<PERSON>laoui \\nProduct Owner \\n\\n\\nEmploymen...</td>\n", "      <td>{'educationHistory': [{'degreeDirection': 'Jav...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3005737</th>\n", "      <th>19045489</th>\n", "      <td>{'educationHistory': [{'degreeDirection': 'Eco...</td>\n", "      <td>ESPERIENZA LAVORATIVA\\n\\nImpiegato amministrat...</td>\n", "      <td>{'educationHistory': [{'degreeDirection': 'Dip...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3005433</th>\n", "      <th>18100417</th>\n", "      <td>{'educationHistory': [{'degreeDirection': 'FIN...</td>\n", "      <td>Curriculum Vitae \\n\\nInformazioni Personali \\n...</td>\n", "      <td>{'educationHistory': [{'degreeDirection': 'LAU...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3005667</th>\n", "      <th>15222661</th>\n", "      <td>{'educationHistory': [{'degreeDirection': 'DIP...</td>\n", "      <td>Curriculum Vitae\\n\\nCognome e Nome MEMOLA LUIG...</td>\n", "      <td>{'educationHistory': [{'degreeDirection': 'DIP...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3007083</th>\n", "      <th>14060257</th>\n", "      <td>{'educationHistory': [{'degreeDirection': 'Bio...</td>\n", "      <td>Coordonnées\\n\\nEmail\\n\\nsakinat<PERSON>zare@gmail.c...</td>\n", "      <td>{'educationHistory': [{'degreeDirection': 'Lau...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3007315</th>\n", "      <th>13931191</th>\n", "      <td>{'educationHistory': [{'degreeDirection': 'Sci...</td>\n", "      <td>11/09/2021 © Unione europea, 2002-2021 \\n\\n\\nI...</td>\n", "      <td>{'educationHistory': [{'degreeDirection': 'Cor...</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["                                                                          response  \\\n", "DATABASE EMPLOYEE_ID CV_FILE_ID                                                      \n", "ITALIA   2995251     19657775    {'educationHistory': [{'degreeDirection': 'Tec...   \n", "         2994920     14910697    {'educationHistory': [{'degreeDirection': 'DIP...   \n", "         2995333     13487937    {'educationHistory': [{'degreeDirection': 'Scu...   \n", "         2995139     20311079    {'educationHistory': [{'degreeDirection': 'Dip...   \n", "         2997997     15780943    {'educationHistory': [{'degreeDirection': 'Sci...   \n", "         2995503     19059437    {'educationHistory': [{'degreeDirection': 'Tec...   \n", "         2996891     16781987    {'educationHistory': [{'degreeDirection': 'Amm...   \n", "         2997285     13765671    {'educationHistory': [{'degreeDirection': 'Dip...   \n", "         2998801     15437879    {'educationHistory': [{'degreeDirection': 'Dip...   \n", "         2998489     19511293    {'educationHistory': [{'degreeDirection': 'Lic...   \n", "         2998675     20380853    {'educationHistory': [{'degreeDirection': 'Pro...   \n", "         2999253     15282423    {'educationHistory': [{'degreeDirection': 'Lan...   \n", "         2999737     15089913    {'educationHistory': [{'degreeDirection': 'Dip...   \n", "         2999623     15679795    {'educationHistory': [{'degreeDirection': 'Lic...   \n", "         2999783     15440379    {'educationHistory': [{'degreeDirection': 'Tur...   \n", "         3000263     19857769    {'educationHistory': [{'degreeDirection': 'Cuo...   \n", "         3001941     19059201    {'educationHistory': [{'degreeDirection': 'Let...   \n", "         3000649     14173165    {'educationHistory': [{'degreeDirection': 'Ope...   \n", "         3003549     19573059    {'educationHistory': [{'degreeDirection': 'Dip...   \n", "         3001027     15943739    {'educationHistory': [{'degreeDirection': 'pit...   \n", "         3004097     15998127    {'educationHistory': [{'degreeDirection': None...   \n", "         3005257     17085065    {'educationHistory': [{'degreeDirection': 'Ope...   \n", "         3005015     14216779    {'educationHistory': [{'degreeDirection': 'Dip...   \n", "         3004757     15404483    {'educationHistory': [{'degreeDirection': 'OPE...   \n", "         3005477     15492501    {'educationHistory': [{'degreeDirection': 'aer...   \n", "         3005737     19045489    {'educationHistory': [{'degreeDirection': 'Eco...   \n", "         3005433     18100417    {'educationHistory': [{'degreeDirection': 'FIN...   \n", "         3005667     15222661    {'educationHistory': [{'degreeDirection': 'DIP...   \n", "         3007083     14060257    {'educationHistory': [{'degreeDirection': 'Bio...   \n", "         3007315     13931191    {'educationHistory': [{'degreeDirection': 'Sci...   \n", "\n", "                                                          CVI_CANDIDATE_CV_CLEANED  \\\n", "DATABASE EMPLOYEE_ID CV_FILE_ID                                                      \n", "ITALIA   2995251     19657775    Curriculum vitae \\n\\n\\nINFORMAZIONI PERSONALI ...   \n", "         2994920     14910697    Formato europeo per il curriculum vitae\\n\\n\\nI...   \n", "         2995333     13487937    Nome: <PERSON> \\n\\nCognome: <PERSON><PERSON><PERSON> \\n\\nLuogo ...   \n", "         2995139     20311079    Con la seguente, ai sensi del Regolamento UE 2...   \n", "         2997997     15780943    GIORGIAGUADALUPI \\nADDETTO ALL’AMMINISTRAZIONE...   \n", "         2995503     19059437    <PERSON>\\nRoveda 13/B\\ntorino\\n10136\\n3889...   \n", "         2996891     16781987    Curriculum Vitae ANA DRACINSCHI \\n\\n\\nINFORMAZ...   \n", "         2997285     13765671    !\\n\\nEsperienze Lavorative\\n\\nIstruzione e For...   \n", "         2998801     15437879    Nome: <PERSON> \\r\\nCognome: Libero \\r\\nEmail: lib...   \n", "         2998489     19511293    22/08/2023 © Unione europea, 2002-2019 | http:...   \n", "         2998675     20380853    <PERSON><PERSON>\\nSenna <PERSON>, Lombardi...   \n", "         2999253     15282423    C1 - Internal use \\n\\n+39 3497754049\\n\\nmartin...   \n", "         2999737     15089913    Curriculum Vitae ALESSANDRO PASQUA \\n\\n © Unio...   \n", "         2999623     15679795    Curriculum Vitae Ndiaye <PERSON> \\n\\n © Unio...   \n", "         2999783     15440379    A2\\n\\nA1\\n\\nA2\\n\\nPROFILO\\nPROFESSIONALE\\n\\nAs...   \n", "         3000263     19857769    FORMATO EUROPEO \\nPER IL CURRICULUM \\n\\nVITAE ...   \n", "         3001941     19059201    Ricerca e selezione candidati\\nElaborazione e ...   \n", "         3000649     14173165    © Unione europea, 2002-2013 | hiip://europass....   \n", "         3003549     19573059    PRESENTAZIONE\\nEsperienza lavorativa della dur...   \n", "         3001027     15943739    PROFILO PROFESSIONALE \\nSono una persona adatt...   \n", "         3004097     15998127    Nome: Chiara\\nCognome: <PERSON><PERSON><PERSON>\\nData di nasci...   \n", "         3005257     17085065    DATI ANAGRAFICI \\n\\nCognome: IZZO Residenza: R...   \n", "         3005015     14216779    PRESENTAZIONE\\nSalve, vorrei sottoporre alla v...   \n", "         3004757     15404483    ��������\\t\\n�����\\n\\n�\\n�����\\n\\n�\\n\\n�\\n\\n�\\n...   \n", "         3005477     15492501    Manal Qablaoui \\nProduct Owner \\n\\n\\nEmploymen...   \n", "         3005737     19045489    ESPERIENZA LAVORATIVA\\n\\nImpiegato amministrat...   \n", "         3005433     18100417    Curriculum Vitae \\n\\nInformazioni Personali \\n...   \n", "         3005667     15222661    Curriculum Vitae\\n\\nCognome e Nome MEMOLA LUIG...   \n", "         3007083     14060257    Coordonnées\\n\\nEmail\\n\\nsakinat<PERSON><PERSON>e@gmail.c...   \n", "         3007315     13931191    11/09/2021 © Unione europea, 2002-2021 \\n\\n\\nI...   \n", "\n", "                                                                           _source  \n", "DATABASE EMPLOYEE_ID CV_FILE_ID                                                     \n", "ITALIA   2995251     19657775    {'educationHistory': [{'degreeDirection': 'Dip...  \n", "         2994920     14910697    {'educationHistory': [{'degreeDirection': 'DIP...  \n", "         2995333     13487937    {'educationHistory': [{'degreeDirection': '', ...  \n", "         2995139     20311079    {'educationHistory': [{'degreeDirection': '', ...  \n", "         2997997     15780943    {'educationHistory': [{'degreeDirection': '', ...  \n", "         2995503     19059437    {'educationHistory': [{'degreeDirection': 'Ele...  \n", "         2996891     16781987    {'educationHistory': [{'degreeDirection': 'Cor...  \n", "         2997285     13765671    {'educationHistory': [{'degreeDirection': 'Cor...  \n", "         2998801     15437879    {'educationHistory': [{'degreeDirection': '', ...  \n", "         2998489     19511293    {'educationHistory': [{'degreeDirection': 'TEC...  \n", "         2998675     20380853    {'educationHistory': [{'degreeDirection': 'Qua...  \n", "         2999253     15282423    {'educationHistory': [{'degreeDirection': 'POS...  \n", "         2999737     15089913    {'educationHistory': [{'degreeDirection': 'Dip...  \n", "         2999623     15679795    {'educationHistory': [{'degreeDirection': 'Lic...  \n", "         2999783     15440379    {'educationHistory': [{'degreeDirection': 'Ass...  \n", "         3000263     19857769    {'educationHistory': [{'degreeDirection': 'Cuo...  \n", "         3001941     19059201    {'educationHistory': [{'degreeDirection': 'Lau...  \n", "         3000649     14173165    {'educationHistory': [{'degreeDirection': 'Seg...  \n", "         3003549     19573059    {'educationHistory': [{'degreeDirection': 'Cer...  \n", "         3001027     15943739    {'educationHistory': [{'degreeDirection': '', ...  \n", "         3004097     15998127    {'educationHistory': [{'degreeDirection': '', ...  \n", "         3005257     17085065    {'educationHistory': [{'degreeDirection': 'Ope...  \n", "         3005015     14216779    {'educationHistory': [{'degreeDirection': 'Dip...  \n", "         3004757     15404483    {'educationHistory': [{'degreeDirection': '', ...  \n", "         3005477     15492501    {'educationHistory': [{'degreeDirection': 'Jav...  \n", "         3005737     19045489    {'educationHistory': [{'degreeDirection': 'Dip...  \n", "         3005433     18100417    {'educationHistory': [{'degreeDirection': 'LAU...  \n", "         3005667     15222661    {'educationHistory': [{'degreeDirection': 'DIP...  \n", "         3007083     14060257    {'educationHistory': [{'degreeDirection': 'Lau...  \n", "         3007315     13931191    {'educationHistory': [{'degreeDirection': 'Cor...  "]}, "execution_count": 24, "metadata": {}, "output_type": "execute_result"}], "source": ["df = df.merge(\n", "    df_cv[[\"CVI_CANDIDATE_CV_CLEANED\", \"DATABASE\", \"EMPLOYEE_ID\", \"CV_FILE_ID\", \"_source\"]],\n", "    on=[\"DATABASE\", \"EMPLOYEE_ID\"],\n", "    how=\"inner\",\n", ").set_index([\"DATABASE\", \"EMPLOYEE_ID\", \"CV_FILE_ID\"])\n", "df"]}, {"cell_type": "code", "execution_count": 25, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["df_cv_parser_tk:\n", "                                                                          TEMPLATE\n", "DATABASE EMPLOYEE_ID CV_FILE_ID                                                   \n", "ITALIA   2995251     19657775    \\nHere are your two inputs:  \\n\\n**Input 1 (Re...\n", "         2994920     14910697    \\nHere are your two inputs:  \\n\\n**Input 1 (Re...\n", "         2995333     13487937    \\nHere are your two inputs:  \\n\\n**Input 1 (Re...\n", "         2995139     20311079    \\nHere are your two inputs:  \\n\\n**Input 1 (Re...\n", "         2997997     15780943    \\nHere are your two inputs:  \\n\\n**Input 1 (Re...\n", "df_cv_parser_qwq:\n", "                                                                          TEMPLATE\n", "DATABASE EMPLOYEE_ID CV_FILE_ID                                                   \n", "ITALIA   2995251     19657775    \\nHere are your two inputs:  \\n\\n**Input 1 (Re...\n", "         2994920     14910697    \\nHere are your two inputs:  \\n\\n**Input 1 (Re...\n", "         2995333     13487937    \\nHere are your two inputs:  \\n\\n**Input 1 (Re...\n", "         2995139     20311079    \\nHere are your two inputs:  \\n\\n**Input 1 (Re...\n", "         2997997     15780943    \\nHere are your two inputs:  \\n\\n**Input 1 (Re...\n"]}], "source": ["# Template\n", "template = \"\"\"\n", "Here are your two inputs:  \n", "\n", "**Input 1 (Resume - Text):**  \n", "{resume}\n", "\n", "\n", "**Input 2 (Extracted Data - JSON):**  \n", "{json}\n", "\"\"\"\n", "\n", "df_cv_parser_tk  = pd.DataFrame(\n", "    index=df.index,\n", "    data={\n", "        \"TEMPLATE\": df.apply(\n", "            lambda x: template.format(\n", "                resume=x[\"CVI_CANDIDATE_CV_CLEANED\"], json=x[\"_source\"]\n", "            ),\n", "            axis=1,\n", "        )\n", "    },\n", ")\n", "\n", "df_cv_parser_qwq  = pd.DataFrame(\n", "    index=df.index,\n", "    data={\n", "        \"TEMPLATE\": df.apply(\n", "            lambda x: template.format(\n", "                resume=x[\"CVI_CANDIDATE_CV_CLEANED\"], json=x[\"response\"]\n", "            ),\n", "            axis=1,\n", "        )\n", "    },\n", ")\n", "\n", "# Mostra i risultati\n", "print(\"df_cv_parser_tk:\")\n", "print(df_cv_parser_tk.head())\n", "\n", "print(\"df_cv_parser_qwq:\")\n", "print(df_cv_parser_qwq.head())"]}, {"cell_type": "code", "execution_count": 106, "metadata": {}, "outputs": [], "source": ["df_cv_parser_tk.to_parquet(\"/data/datascience/cv_extract_info/datasets/evaluation_cv_parser_tk.parquet\")\n", "df_cv_parser_qwq.to_parquet(\"/data/datascience/cv_extract_info/datasets/evaluation_cv_parser_qwq.parquet\")"]}], "metadata": {"kernelspec": {"display_name": "ai_playground", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.10.15"}}, "nbformat": 4, "nbformat_minor": 2}