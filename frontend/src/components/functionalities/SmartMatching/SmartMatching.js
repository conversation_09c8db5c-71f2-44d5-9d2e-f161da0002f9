import React, { useState } from 'react';
import { FaSearch, FaSyncAlt, FaInfoCircle, FaExternalLinkAlt } from 'react-icons/fa';
import { TbArrowBackUp } from "react-icons/tb";
import './SmartMatching.css';

const SmartMatching = ({ card, externalResponse, handleSubmit, onFileUpload, files, externalInput, onResponse }) => {
    const [internalInput, setInternalInput] = useState(839997);
    const [isLoading, setIsLoading] = useState(false);
    const [selectedDatabase, setSelectedDatabase] = useState('ITALY');
    const [useGeoFilter, setUseGeoFilter] = useState(false);
    const [isEditingGeoFilter, setIsEditingGeoFilter] = useState(false);
    const [tempRadius, setTempRadius] = useState("");
    const [radius, setRadius] = useState(0);
    const [limit, setLimit] = useState(10);
    const [offset, setOffset] = useState(1);
    const [expandedRow, setExpandedRow] = useState(null);

    const databaseLinks = {
        ITALY: "https://itgigroupspinner.gigroup.local/#/employee/",
        BRAZIL: "https://brspinner.gigroup.local/#/employee/",
        POLAND: "https://plspinner.gigroup.local/#/employee/",
        PORTUGAL: "https://ptspinner.gigroup.local/#/employee/",
        SPAIN_GIG: "https://esgigroupspinner.gigroup.local/#/employee/",
        SPAIN_WYSER: "https://esspinner.gigroup.local//#/employee/",
    };

    const [vacancyData, setVacancyData] = useState(null);
    const [candidatesData, setCandidatesData] = useState(null);

    const toggleGeoFilter = () => {
        if (useGeoFilter) {
            // Se il filtro è attivo, disattivalo
            setUseGeoFilter(false);
            setRadius(0);
        } else {
            // Se il filtro è disattivo, entra in modalità di editing
            setIsEditingGeoFilter(true);
            setTempRadius(radius); // Inizializza tempRadius con il valore corrente di radius
        }
    };

    const sm_search_query = async ({ selectedDatabase, vacancyId }) => {

        setIsLoading(true);

        try {

            const apiEndpoint = `/api/sm_search_query`;

            // create object with data to send to the API
            const formData = new FormData();
            formData.append("selectedDatabase", selectedDatabase);
            formData.append("vacancyId", vacancyId);
            formData.append('with_payload', true);
            formData.append('with_vectors', false);

            let response = await fetch(apiEndpoint, {
                method: 'POST',
                body: formData,
            });

            const data = await response.json();
            setVacancyData(data)

        } catch (error) {
            console.error("Error during API call", error.message);
            onResponse("Error during API call", error.message);
        } finally {
            setIsLoading(false);
        }
    };


    // Questa è la sm_search_documents che viene chiamata quando clicchi su "Search Candidates"
    const sm_search_documents = async () => {
        setIsLoading(true);

        try {
            const apiEndpoint = `/api/sm_search_documents`;

            const formData = new FormData();
            formData.append("selectedDatabase", selectedDatabase);
            formData.append("vacancyId", internalInput);
            formData.append("useGeoFilter", useGeoFilter);
            formData.append("radius", radius);
            formData.append("limit", limit);
            formData.append("offset", offset);

            let response = await fetch(apiEndpoint, {
                method: 'POST',
                body: formData,
            });

            const data = await response.json();
            setCandidatesData(data.candidates);
            onResponse(data);

        } catch (error) {
            console.error("Error during API call", error.message);
            onResponse("Error during API call", error.message);
        } finally {
            setIsLoading(false);
        }
    };



    const handleApply = (e) => {
        e.preventDefault(); // Previene il comportamento predefinito del pulsante
        setRadius(Number(tempRadius));
        setUseGeoFilter(true);
        setIsEditingGeoFilter(false);
    };

    return (
        <div className="functionality">

            {/* Form per inviare le informazioni */}
            <form
                onSubmit={(e) => {
                    e.preventDefault();
                    sm_search_documents();
                }}
            >
                <div className="smart-matching-form">
                    {/* Vacancy ID Input */}
                    <div className="form-group">
                        <label>Vacancy ID</label>
                        <input
                            type="number"
                            value={internalInput}
                            onChange={(e) => setInternalInput(e.target.value)}
                            placeholder="Insert vacancy ID"
                        />
                    </div>

                    {/* Database Selection */}
                    <div className="form-group">
                        <label>Database</label>
                        <select
                            value={selectedDatabase}
                            onChange={(e) => setSelectedDatabase(e.target.value)}
                        >
                            <option value="ITALY">ITALY</option>
                            <option value="BRAZIL">BRAZIL</option>
                            <option value="POLAND">POLAND</option>
                            <option value="PORTUGAL">PORTUGAL</option>
                            <option value="SPAIN_GIG">SPAIN_GIG</option>
                            <option value="SPAIN_WYSER">SPAIN_WYSER</option>
                        </select>
                    </div>
                </div>

                {/* Button to Search Vacancy */}
                <button
                    type="button"
                    className="submit-button"
                    onClick={(e) => {
                        e.preventDefault();
                        sm_search_query({
                            selectedDatabase: selectedDatabase,
                            vacancyId: internalInput,
                        });
                    }}
                    disabled={isLoading}
                >
                    {isLoading ? 'Waiting...' : <>
                        <FaSearch /> Search Vacancy
                    </>}
                </button>

                {vacancyData && (
                    <div className="vacancy-section">
                        <h3>Vacancy Details</h3>
                        <table className="vacancy-table">
                            <tbody>
                                {(() => {
                                    // Primo livello di parsing
                                    const parsedText = JSON.parse(vacancyData["vacancy_payload"].text || "{}");
                                    // parsedText ora dovrebbe essere un oggetto con la chiave TEXT_VACANCY

                                    // Secondo livello di parsing
                                    const textVacancy = parsedText.TEXT_VACANCY || "{}";

                                    return Object.entries(JSON.parse(textVacancy)).map(([key, value]) => (
                                        <tr key={key}>
                                            <td className="key-column"><strong>{key.toUpperCase()}:</strong></td>
                                            <td className="value-column">{value}</td>
                                        </tr>
                                    ));
                                })()}
                            </tbody>
                        </table>
                    </div>
                )}


                {/* Blocchi filtro */}
                <div style={{ paddingTop: "20px" }}>
                    <label style={{ color: '#333', fontWeight: 600 }}>Filters</label>
                    <div className="filter-blocks">
                        <button
                            type="button"
                            className={`filter-button ${useGeoFilter ? 'active' : ''}`}
                            onClick={toggleGeoFilter}
                        >
                            Geographic
                        </button>
                    </div>
                </div>

                {/* Campo per impostare il raggio */}
                {
                    isEditingGeoFilter && (
                        <div className="filter-set-values">
                            <div className="filter-group">
                                <label>Raggio (km):</label>
                                <input
                                    type="number"
                                    placeholder="Insert radius"
                                    value={tempRadius}
                                    onChange={(e) => setTempRadius(e.target.value)}
                                />
                            </div>
                            <button
                                type="button"
                                className='filter-apply-button'
                                onClick={handleApply}
                            >
                                Apply
                            </button>
                        </div>
                    )
                }

                {/* Bottone per submit */}
                <button
                    type="submit"
                    className="submit-button"
                    onClick={(e) => {
                        e.preventDefault();
                        sm_search_documents();
                    }}
                    disabled={isLoading}>
                    {isLoading ? 'Waiting...' : <>
                        <FaSearch /> Search Candidates
                    </>}
                </button>
            </form >

            {/* Mostra il valore di externalResponse */}
            {externalResponse && externalResponse.candidates && externalResponse.candidates.length > 0 && (
                <div className="response-container">
                    {/* Mostra la tabella dei candidati solo se expandedRow è null */}
                    {expandedRow === null ? (
                        <>
                            <table className="response-table">
                                <thead>
                                    <tr>
                                        <th>DATABASE</th>
                                        <th>EMPLOYEE ID</th>
                                        <th>TEXT EMPLOYEE</th>
                                        <th>LAT</th>
                                        <th>LON</th>
                                        <th>Actions</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    {externalResponse.candidates.map((candidate, index) => {
                                        const parsedText = JSON.parse(candidate.text || "{}");
                                        const database = parsedText.DATABASE || "N/A";
                                        const employeeId = parsedText.EMPLOYEE_ID || "N/A";
                                        const textEmployee = parsedText.TEXT_EMPLOYEE || "N/A";
                                        const lat = parsedText.lat || "N/A";
                                        const lon = parsedText.lon || "N/A";

                                        // Recupera il link dal dizionario
                                        const baseLink = databaseLinks[database];
                                        const link = `${baseLink}${employeeId}`;

                                        return (
                                            <tr key={index}>
                                                <td>{database}</td>
                                                <td>{employeeId}</td>
                                                <td className="text-employee">{textEmployee}</td>
                                                <td>{lat}</td>
                                                <td>{lon}</td>
                                                <td>
                                                    <button
                                                        type="button"
                                                        className="icon-button"
                                                        onClick={() => setExpandedRow(index)}
                                                        title="View Details"
                                                    >
                                                        <FaInfoCircle /> {/* Icona per visualizzare */}
                                                    </button>

                                                    {/* Bottone per aprire il link esternamente */}
                                                    <button
                                                        type="button"
                                                        className="icon-button"
                                                        onClick={() => window.open(link, 'external-link', 'noopener,noreferrer')}
                                                        title="Open External Link"
                                                    >
                                                        <FaExternalLinkAlt /> {/* Icona per link esterno */}
                                                    </button>
                                                </td>
                                            </tr>
                                        );
                                    })}
                                </tbody>
                            </table>

                            {/* Controlli per la paginazione */}
                            <div className="pagination-controls">
                                <div className="pagination-input">
                                    <label>Limit per page:</label>
                                    <input
                                        type="number"
                                        value={limit}
                                        onChange={(e) => setLimit(Number(e.target.value))}
                                    />
                                </div>
                                <div className="pagination-input">
                                    <label>Page number:</label>
                                    <input
                                        type="number"
                                        value={offset}
                                        onChange={(e) => setOffset(Number(e.target.value))}
                                    />
                                </div>
                                <button
                                    type="button"
                                    className="update-button"
                                    onClick={(e) => {
                                        e.preventDefault();
                                        sm_search_documents();
                                    }}
                                >
                                    <FaSyncAlt /> Update results
                                </button>
                            </div>
                        </>
                    ) : (
                        <>
                            <div className='back-button'><button
                                type="button"
                                className="close-button"
                                onClick={() => setExpandedRow(null)}
                                style={{
                                    border: 'none',
                                    cursor: 'pointer',
                                }}
                            >
                                <TbArrowBackUp /> Back to Candidates
                            </button>
                            </div>
                            <div className="candidate-details">
                                <h3>Candidate Details</h3>
                                <table className="vacancy-table">
                                    <tbody>
                                        {Object.entries(JSON.parse(JSON.parse(externalResponse.candidates[expandedRow].text).TEXT_EMPLOYEE) || "{}")
                                            .filter(([key]) => key.toUpperCase() !== "IL CANDIDATO POTREBBE AVER MATURATO I SEGUENTI NUOVI JOB TITLE" && key.toUpperCase() !== "IL CANDIDATO POTREBBE AVER MATURATO LE SEGUENTI NUOVE SKILLS") // Filtro le chiavi da escludere
                                            .map(([key, value]) => (
                                                <tr key={key}>
                                                    <td className="key-column"><strong>{key.toUpperCase()}:</strong></td>
                                                    <td className="value-column">{value}</td>
                                                </tr>
                                            ))}
                                    </tbody>
                                </table>
                            </div>
                        </>
                    )}


                </div>
            )}
        </div >
    );

};

export default SmartMatching;