import React, { useState } from 'react';
import './InternalJobPosting.css';

/**
 * <PERSON><PERSON> fake per i target job e i relativi fitting jobs
 */
const targetJobsData = [
    {
        id: 'MLENG',
        label: 'ML Engineer',
        fittingJobs: [
            { id: 1, name: '<PERSON>L Engineer', score: 1 },
            { id: 2, name: 'Data Engineer', score: 0.85 },
            { id: 3, name: 'Junior Data Scientist', score: 0.75 },
        ],
    },
    {
        id: 'SENBACK',
        label: 'Senior Backend Developer',
        fittingJobs: [
            { id: 4, name: 'Senior Node.js Developer', score: 0.90 },
            { id: 5, name: 'Backend Tech Lead', score: 0.80 },
            { id: 6, name: 'Mid-level Java Developer', score: 0.70 },
        ],
    },
    {
        id: 'HEADDATA',
        label: 'Head of Data',
        fittingJobs: [
            { id: 7, name: 'Data Architect', score: 0.95 },
            { id: 8, name: 'Senior Data Engineer', score: 0.85 },
            { id: 9, name: 'Data Analyst', score: 0.75 },
        ],
    },
];

/**
 * <PERSON>ti fake che mappano ciascun ID di "fitting job"
 * a una lista di "replacement jobs".
 */
const replacementsMapping = {
    1: [
        { id: 101, name: 'Junior Data Scientist' },
        { id: 102, name: 'Junior Developer' },
        { id: 103, name: 'Fresh grad' },
    ],
    2: [
        { id: 201, name: 'ETL Specialist' },
        { id: 202, name: 'Data Analyst' },
    ],
    3: [
        { id: 301, name: 'Graduate Intern' },
        { id: 302, name: 'Trainee Data Scientist' },
    ],
    4: [
        { id: 401, name: 'Junior Backend Developer' },
        { id: 402, name: 'Intern Node.js Developer' },
    ],
    5: [
        { id: 501, name: 'Intermediate Backend Developer' },
        { id: 502, name: 'Backend Intern' },
    ],
    6: [
        { id: 601, name: 'Junior Java Developer' },
        { id: 602, name: 'Intern Java Developer' },
    ],
    7: [
        { id: 701, name: 'Data Specialist' },
        { id: 702, name: 'Data Intern' },
    ],
    8: [
        { id: 801, name: 'Intermediate Data Engineer' },
        { id: 802, name: 'Junior Data Engineer' },
    ],
    9: [
        { id: 901, name: 'Junior Data Analyst' },
        { id: 902, name: 'Data Intern' },
    ],
};

const InternalJobPosting = () => {
    /**
     * Stato: quale target job è selezionato (di default il primo: MLENG)
     */
    const [selectedTargetJob, setSelectedTargetJob] = useState(targetJobsData[0]);

    /**
     * Stato: quale fitting job tra quelli proposti è stato cliccato
     */
    const [selectedFittingJob, setSelectedFittingJob] = useState(null);

    /**
     * Stato: la lista di replacements che mostriamo una volta cliccato un fitting job
     */
    const [currentReplacements, setCurrentReplacements] = useState([]);

    /**
     * Quando si cambia la "target position", resettiamo i fitting e i replacements
     */
    const handleTargetJobChange = (event) => {
        const newSelected = targetJobsData.find(tj => tj.id === event.target.value);
        setSelectedTargetJob(newSelected);
        setSelectedFittingJob(null);
        setCurrentReplacements([]);
    };

    /**
     * Quando clicco su un fitting job:
     * 1) lo salvo come "selezionato"
     * 2) recupero la lista di replacement jobs corrispondenti
     */
    const handleFittingJobClick = (job) => {
        setSelectedFittingJob(job);
        setCurrentReplacements(replacementsMapping[job.id] || []);
    };

    return (
        <div className="internal-job-posting-container">
            <h2 className="ijp-title">Internal job posting</h2>

            {/* SEZIONE SELEZIONE DEL "TARGET JOB" */}
            <div className="ijp-block">
                <h4 className="ijp-subtitle">Select target position</h4>
                <select
                    value={selectedTargetJob.id}
                    onChange={handleTargetJobChange}
                    className="ijp-input"
                >
                    {targetJobsData.map((job) => (
                        <option key={job.id} value={job.id}>
                            {job.label}
                        </option>
                    ))}
                </select>
                <p className="ijp-hint">
                    (Esempio: “ML Engineer”, “Senior Backend Developer”, “Head of Data”)
                </p>
            </div>

            {/* SEZIONE: LISTA DI FITTING JOBS (dinamica in base a selectedTargetJob) */}
            <div className="ijp-block">
                <h4 className="ijp-subtitle">
                    Fitting jobs for {selectedTargetJob.label}
                </h4>
                <ul className="ijp-list">
                    {selectedTargetJob.fittingJobs.map((job) => (
                        <li
                            key={job.id}
                            className={`ijp-list-item ${selectedFittingJob && selectedFittingJob.id === job.id
                                    ? 'ijp-list-item-selected'
                                    : ''
                                }`}
                            onClick={() => handleFittingJobClick(job)}
                        >
                            <strong>{job.name}</strong> – {Math.round(job.score * 100)}% match
                        </li>
                    ))}
                </ul>
            </div>

            {/* SEZIONE: REPLACEMENTS SOLO SE HO SELEZIONATO UN FITTING JOB */}
            {selectedFittingJob && (
                <div className="ijp-block">
                    <h4 className="ijp-subtitle">
                        Replacements for {selectedFittingJob.name}
                    </h4>
                    <ul className="ijp-list">
                        {currentReplacements.map((rep) => (
                            <li key={rep.id} className="ijp-list-item">
                                {rep.name}
                            </li>
                        ))}
                    </ul>
                </div>
            )}

            <div className="ijp-footer">
                <small>Data shown here is purely indicative.</small>
            </div>
        </div>
    );
};

export default InternalJobPosting;
