upstream backend {
    least_conn;
    server vllm0:8081;
    server vllm1:8081;
    server vllm2:8081;
    server vllm3:8081;
}
server {
    listen 8080;
    location / {
        proxy_pass http://backend;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;

        # ⏳ Aumenta i timeout per evitare errori 504
        proxy_connect_timeout 30s;  # Tempo per connettersi a vLLM
        proxy_send_timeout 30s;    # Tempo per inviare la richiesta (batch grandi)
        proxy_read_timeout 600s;    # Tempo massimo per ricevere la risposta
    }
}