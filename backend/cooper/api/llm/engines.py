from __future__ import annotations

import asyncio
import os

from cooper.utils import (
    get_base64_content_from_url,
    get_base64_from_image_path,
    get_logger,
)
from dotenv import load_dotenv
from openai import AsyncAzureOpenAI, AsyncOpenAI

logger = get_logger(__name__)

load_dotenv()


# Base interface for LLM engines
class LLMEngine:
    def __init__(self, client, model):
        """
        :param client: An AsyncOpenAI or AsyncAzureOpenAI client.
        :param model: Name of the model/distribution to use.
        """
        self.client = client
        self.model = model

    async def generate_response(
        self,
        system_content: str,
        user_content: str,
        images_base64: list[str] | None = None,
        videos_base64: list[str] | None = None,
        stream: bool = False,
        extra_body: dict | None = None,
        chat_history: list[dict] | None = None,
    ):
        """
        Generates a response from the model using a chat format.
        This implementation is based on the VLLM approach, which
        includes seed support and a list-of-dicts user content.

        :param system_content: The system prompt or role instructions.
        :param user_content: The user's query or message content.
        :param stream: Whether to stream the response tokens or return all at once.
        :param extra_body: Additional parameters to be sent to the API (e.g. temperature).
        :param chat_history: A list of previous chat messages (role + content).
        :yield: Parts of the streamed response (if stream=True) or the full response (if stream=False).
        """

        try:
            if chat_history is None:
                chat_history = []

            user_message_content = [
                {
                    "type": "text",
                    "text": user_content,
                }
            ]

            if images_base64:
                user_message_content.extend(
                    [
                        {
                            "type": "image_url",
                            "image_url": {
                                "url": f"data:image/jpeg;base64,{image_base64}",
                            },
                        }
                        for image_base64 in images_base64
                    ]
                )

            if videos_base64:
                user_message_content.extend(
                    [
                        {
                            "type": "video_url",
                            "video_url": {
                                "url": f"data:video/mp4;base64,{video_base64}",
                            },
                        }
                        for video_base64 in videos_base64
                    ]
                )

            # NOTE: System message content MUST be a plain string.
            # Providing it as a list (e.g., [{"type": "text", "text": system_content}]),
            # even if it only contains text, causes an "AssertionError: Message content must be normalized"
            # error within the vLLM/mistral-common backend when processing the system prompt.
            # The list format is only supported for multi-modal content within 'user' or 'assistant' roles.
            messages = [
                {
                    "role": "system",
                    "content": system_content,
                },
                *chat_history,
                {
                    "role": "user",
                    "content": user_message_content,
                },
            ]

            # Send request to the client
            response = await self.client.chat.completions.create(
                model=self.model,
                messages=messages,
                stream=stream,
                extra_body=extra_body,
                seed=42,
            )

            # Stream or return the response
            if stream:
                async for chunk in response:
                    if chunk.choices:
                        yield chunk.choices[0].delta.content or ""
                    else:
                        yield ""
            else:
                yield response.choices[0].message.content

        except asyncio.CancelledError:
            logger.warning("Stream response was cancelled")
            return  # Silenzia l'errore se il client si disconnette


# OllamaEngine
class OllamaEngine(LLMEngine):
    # https://github.com/ollama/ollama/blob/main/docs/openai.md
    def __init__(self, base_url: str, api_key: str, model: str):
        """
        Ollama engine using AsyncOpenAI-like client pointing to the Ollama endpoint.
        """
        client = AsyncOpenAI(
            base_url=base_url,
            api_key=api_key,
        )
        super().__init__(client, model)


## (DEPRECATED) LlamaCPPEngine
# class LlamaCPPEngine(LLMEngine):
#     def __init__(self, base_url: str, api_key: str, model: str):
#         """
#         llama.cpp engine using AsyncOpenAI-like client pointing to the llama.cpp endpoint.
#         """
#         client = AsyncOpenAI(
#             base_url=base_url,
#             api_key=api_key,
#         )
#         super().__init__(client, model)


# VLLMEngine
class VLLMEngine(LLMEngine):
    def __init__(self, base_url: str, api_key: str, model: str):
        """
        VLLM engine using AsyncOpenAI-like client pointing to a VLLM endpoint.
        """
        client = AsyncOpenAI(
            base_url=base_url,
            api_key=api_key,
        )
        super().__init__(client, model)


# AzureOpenAIEngine
class AzureOpenAIEngine(LLMEngine):
    def __init__(
        self,
        azure_endpoint: str,
        api_key: str,
        api_version: str,
        model: str,
    ):
        """
        Azure OpenAI engine using the AsyncAzureOpenAI client.
        """
        client = AsyncAzureOpenAI(
            azure_endpoint=azure_endpoint,
            api_key=api_key,
            api_version=api_version,
        )
        super().__init__(client, model)


# Factory
class LLMEngineFactory:
    @staticmethod
    def get_engine(engine: str) -> LLMEngine:
        """
        Returns an instance of the requested engine type.
        """
        if engine == "ollama":
            return OllamaEngine(
                base_url=os.getenv("OLLAMA_ENDPOINT"),
                api_key=os.getenv("OLLAMA_API_KEY"),
                model=os.getenv("OLLAMA_DISTRIBUTION"),
            )

        # if engine == "llama_cpp":
        #     return LlamaCPPEngine(
        #         base_url=os.getenv("LLAMA_CPP_ENDPOINT"),
        #         api_key=os.getenv("LLAMA_CPP_API_KEY"),
        #         model=os.getenv("LLAMA_CPP_DISTRIBUTION"),
        #     )

        if engine == "vllm":
            return VLLMEngine(
                base_url=os.getenv("VLLM_ENDPOINT"),
                api_key=os.getenv("VLLM_API_KEY"),
                model=os.getenv("VLLM_DISTRIBUTION"),
            )

        if engine == "azure_openai":
            return AzureOpenAIEngine(
                azure_endpoint=os.getenv("AZURE_OPENAI_ENDPOINT"),
                api_key=os.getenv("AZURE_OPENAI_API_KEY"),
                api_version=os.getenv("AZURE_OPENAI_API_VERSION"),
                model=os.getenv("AZURE_OPENAI_DISTRIBUTION"),
            )

        msg = f"Engine '{engine}' not available"
        raise ValueError(msg)


async def main():
    engine = LLMEngineFactory.get_engine("vllm")

    image_1 = await get_base64_from_image_path(
        "/home/<USER>/workarea/ai_playground/.images/0.png"
    )
    image_2 = get_base64_content_from_url(
        "https://upload.wikimedia.org/wikipedia/commons/thumb/d/dd/Gfp-wisconsin-madison-the-nature-boardwalk.jpg/2560px-Gfp-wisconsin-madison-the-nature-boardwalk.jpg"
    )

    # Generazione di una risposta
    response_generator = engine.generate_response(
        system_content="You are a helpful assistant.",
        user_content="Qual è la differenza principale tra le due immagini?",
        images_base64=[image_1, image_2],
        stream=True,
    )

    # Stampiamo la risposta
    async for response in response_generator:
        print(response, end="")  # noqa: T201

    # # Generazione di una risposta
    # ImportError: Please install vllm[video] for video support
    # video_1 = get_base64_content_from_url(
    #     "http://commondatastorage.googleapis.com/gtv-videos-bucket/sample/ForBiggerFun.mp4"
    # )
    # response_generator = engine.generate_response(
    #     system_content="You are a helpful assistant.",
    #     user_content="Qual è la differenza principale tra le due immagini?",
    #     videos_base64=[video_1],
    #     stream=True,
    # )
    # # Stampiamo la risposta
    # async for response in response_generator:
    #     print(response, end="")  # noqa: T201


# Esegui lo script principale
if __name__ == "__main__":
    import asyncio

    asyncio.run(main())
