from __future__ import annotations

import argparse
import logging
import time
from pathlib import Path
from typing import Literal

import numpy as np
from cooper.api.llm.instructor_llm.instructor_extractor import InstructorExtractor
from cooper.api.llm.instructor_llm.template_prompts.cv_parser import (
    ExtractFunctionCvParser,
)
from cooper.api.llm.instructor_llm.template_prompts.jobad import ExtractFunctionJobAd
from cooper.api.llm.instructor_llm.template_prompts.work_experiences import (
    ExtractFunctionCV,
)
from cooper.utils import get_logger, parse_yaml
from dotenv import load_dotenv
from joblib import Parallel, delayed
from tqdm import tqdm

load_dotenv()
logger = get_logger(__name__, level=logging.DEBUG)


def get_extractor(name: Literal["cv", "jobad", "cv_parser"]):
    if name == "cv":
        return ExtractFunctionCV()
    elif name == "jobad":
        return ExtractFunctionJobAd()
    elif name == "cv_parser":
        return ExtractFunctionCvParser()
    else:
        msg = "Unsupported method of extraction"
        raise ValueError(msg)


def process_document(
    idx,
    batch,
    output_path,
    config_options,
    extract_fn,
    client,
    make_pipeline,
):
    output_path = output_path / f"{idx}.parquet"

    if output_path.exists():
        logger.info(f"File {output_path} already exists. No action taken.")
        return

    guidance = InstructorExtractor(
        model_name_or_path=config_options["llm_options__model_name_or_path"],
        extract_fn=get_extractor(extract_fn),
        client=client,
        pbar=False,
        batch_size=1,  # Must be 1
    ).set_output(transform="pandas")
    pipe = make_pipeline(guidance)  # make_pipeline(cleaner, guidance)
    result = pipe.fit_transform(batch)

    result.to_parquet(output_path)


def process_data(
    input_path: Path,
    output_path: Path,
    config_options: Path,
    client: Literal["openai", "azure", "groq"],
    extract_fn: Literal["cv", "jobad"],
    column_name: str,
    n_rows: int | None = None,
    max_cv_length: int = 12_000,
    n_jobs: int | None = None,
    batch_size: int | None = None,
):
    """
    Process data from an input file and extract guided work experience using a pre-trained model and the OpenAI API.

    Parameters:
    -----------
    input_path : Path
        The path to the input file in Parquet format.

    config_options : Path
        The path to the configuration options file in JSON format.

    output_path : Path
        The path to save the output file in Parquet format.

    client: Path
        Whether to use OpenAI or Groq (experimental, not working currently)

    extract_fn: "cv" or "jobad"
        Select whether to extract cv or jobad

    column_name : str, optional
        The name of the column in the input file containing the CV data. Default is "CVI_CANDIDATE_CV".

    n_rows : int, optional
        The number of rows to process from the input file. Default is None, which processes all rows.

    length_trim : int, optional
        The maximum length of the CV text to consider. CVs longer than this length will be excluded. Default is 12,000.

    Returns:
    --------
    None
    """

    import pandas as pd
    from cooper.api.llm.instructor_llm.engines import (
        make_instructor_azure_openai_client,
        make_instructor_groq_client,
        make_instructor_openai_client,
    )
    from sklearn.pipeline import make_pipeline

    df = pd.read_parquet(input_path)[column_name].dropna().head(n_rows).to_frame()
    df = df[df[column_name].str.len() < max_cv_length]

    # creates different configurations
    if client == "openai":
        client = make_instructor_openai_client(
            base_url=config_options["llm_options__base_url"],
            api_key=config_options["llm_options__api_key"],
        )
    elif client == "groq":
        client = make_instructor_groq_client(
            None,
            api_key=config_options["llm_options__api_key"],
        )
    elif client == "azure":
        client = make_instructor_azure_openai_client(
            base_url=config_options["llm_options__base_url"],
            api_key=config_options["llm_options__api_key"],
            api_version=config_options["llm_options__api_version"],
        )
    else:
        msg = f"Not supported client: {client}"
        raise ValueError(msg)

    if not batch_size or not n_jobs:
        batch_size = 1  # Imposta la dimensione del batch a 1 se non specificato
        n_jobs = 1  # Usa 1 solo job se n_jobs non è specificato

    # Dividi il DataFrame in batch
    batches = np.array_split(df, np.ceil(len(df) / batch_size))

    # Parallelizza il processo sui documenti
    t1 = time.time()
    Path.mkdir(output_path, exist_ok=True, parents=True)

    Parallel(n_jobs=n_jobs, backend="threading")(
        delayed(process_document)(
            idx,
            batch,
            output_path,
            config_options,
            extract_fn,
            client,
            make_pipeline,
        )
        for idx, batch in tqdm(
            enumerate(batches), total=len(batches), desc="Processing documents"
        )
    )
    logger.info(f"IL TEMPO RICHIESTO è: {time.time() - t1}")

    # do the actual fit transform
    # result = pipe.fit_transform(df)


def run(config_file: str):
    parameters = parse_yaml(config_file)

    process_data(
        input_path=Path(parameters["input_path"]),
        output_path=Path(parameters["output_path"]),
        config_options=parameters,
        client=parameters["llm_options__client"],
        extract_fn=parameters["extract_fn"],
        column_name=parameters["column_name"],
        n_rows=parameters["n_rows"],
        max_cv_length=parameters["max_cv_length"],
        n_jobs=parameters["n_jobs"],
        batch_size=1,
    )


if __name__ == "__main__":
    # conda activate ai_playground && python /home/<USER>/workarea/ai_playground/backend/cooper/api/llm/instructor_llm/main.py --config=/home/<USER>/workarea/ai_playground/backend/cooper/api/llm/instructor_llm/assets/configuration/employability_solutions/config_azure_test.yml

    # Crea un parser per gli argomenti della linea di comando
    parser = argparse.ArgumentParser(description="Launch API with configuration file.")
    parser.add_argument(
        "--config",
        type=str,
        required=False,
        help="Path to the configuration file.",
        default="/home/<USER>/workarea/ai_playground/backend/cooper/api/llm/instructor_llm/assets/configuration/employability_solutions/config_azure_test.yml",
    )

    # Analizza gli argomenti della linea di comando
    args = parser.parse_args()

    logger.info("RUN")
    run(config_file=args.config)

    logger.info("DONE")
